DEFAULT_BROWSER_TYPE=ADS # ADS/BIT/CHROME
OKX_WALLET_PASSWORD=your okx wallet password
BACKPACK_WALLET_PASSWORD=your backpack wallet password
KEPLR_WALLET_PASSWORD=your keplr wallet password
ICLOUD_EMAIL=your icloud email
ICLOUD_EMAIL_PASSWORD=your icloud email password
PROXY_URL=your proxy url

# deepseek api key
SILICONFLOW_API_KEY=your siliconflow api key https://siliconflow.cn/zh-cn/
BAIDU_API_KEY=your baidu api key https://cloud.baidu.com/product-s/qianfan_modelbuilder
AI_MODEL='ep-20250217110611-cmj89' # deepseek模型,参照平台事例
AI_BASE_URL='https://ark.cn-beijing.volces.com/api/v3' # 参照平台事例
AI_API_KEY="c6c34d9e-fe4d-4dce-a3cb-60073f225781"


AVATAR_PATH="your path/avatar_*.jpeg"

# x修改头像相关配置
# x_avatar_path=E:\\Downloads，则程序自动查找E:\\Downloads\\{id}.png、E:\\Downloads\\{id}.jpg、E:\\Downloads\\{id}.jpeg等文件
# id表示指纹对应的id
X_AVATAR_PATH=your twitter avatar file path
X_HEADER_IMAGE_PATH=your twitter header image file path

#  nocaptcha 相关配置
NOCAPTCHA_KEY=your nocaptcha key
NOCAPTCHA_DEV_CODE=your nocaptcha dev code

#yescaptcha
YESCAPTCHA_KEY=your yescaptcha key
NEWAPI_BASE_URL=your newapi url
NEWAPI_KEY=newapi token 

MONAD_RPC=https://testnet-rpc.monad.xyz
SOMNIA_RPC=https://dream-rpc.somnia.network


SOMNIC_INVITE_CODE=110FB896,E5A5E2C4

#微信推送 企业微信机器人地址
webhook_url=
corpid=
corpsecret=
agentid=

# SMTP配置
SMTP_SERVER=smtp.qq.com
SMTP_PORT=587

# 邮箱配置
SENDER_EMAIL=qq邮箱
EMAIL_PASSWORD= 授权码

# 默认收件人，多个收件人用逗号分隔
DEFAULT_RECIPIENTS=

# 邮件模板文件路径
ALERT_TEMPLATE_PATH=data/alert.html
REPORT_TEMPLATE_PATH=data/report.html

# ads指纹浏览器默认API地址
ADS_BROWSER_API_BASE_URL=http://127.0.0.1:50325
# bit指纹浏览器默认API地址
BIT_BROWSER_API_BASE_URL=http://127.0.0.1:54345
# more login指纹浏览器默认API地址
MORE_LOGIN_API_BASE_URL=http://127.0.0.1:40000