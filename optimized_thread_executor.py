from concurrent.futures import ThreadPoolExecutor, wait, ALL_COMPLETED, TimeoutError, as_completed
from typing import Callable, Optional, Union, Any, List
from functools import wraps
from loguru import logger
import time
import threading


class ThreadExecutor:
    """
    线程执行器
    用于处理需要多线程执行的任务，支持超时控制和失败重试
    """

    def __init__(
        self,
        workers: int = 5,  # 工作线程数
        timeout: int = 1800,  # 单任务超时时间(秒)
        retries: int = 3,  # 失败重试次数
        interval: int = 1,  # 任务间隔时间(秒)
        batch_interval: int = 3,  # 批量任务提交间隔时间(秒)
        raise_exception: bool = False,  # 是否抛出异常
        task_name: str = "",  # 任务名称，用于日志标识
        progress_callback: Optional[Callable[[int, int], None]] = None,  # 进度回调函数
    ):
        """
        初始化线程执行器
        Args:
            workers: 工作线程数，必须大于0
            timeout: 单任务超时时间(秒)，必须大于0
            retries: 失败重试次数，必须大于0
            interval: 任务间隔时间(秒)，必须大于等于0
            batch_interval: 批量任务提交间隔时间(秒)，必须大于等于0
            raise_exception: 是否在最后一次重试失败时抛出异常
            task_name: 任务名称，用于日志标识
            progress_callback: 进度回调函数，接收 (completed, total) 参数
        """
        self._validate_params(workers, timeout, retries, interval, batch_interval)
        self.workers = workers
        self.timeout = timeout
        self.retries = retries
        self.interval = interval
        self.batch_interval = batch_interval
        self.raise_exception = raise_exception
        self.task_name = task_name or "Task"
        self.progress_callback = progress_callback
        self._cancelled = threading.Event()

    @staticmethod
    def _validate_params(
        workers: int, timeout: int, retries: int, interval: int, batch_interval: int
    ) -> None:
        """
        验证输入参数
        Args:
            workers: 工作线程数
            timeout: 超时时间
            retries: 重试次数
            interval: 间隔时间
            batch_interval: 批量任务间隔时间
        Raises:
            ValueError: 当参数不合法时抛出
        """
        if workers < 1:
            raise ValueError("workers must be greater than 0")
        if timeout < 1:
            raise ValueError("timeout must be greater than 0")
        if retries < 1:
            raise ValueError("retries must be greater than 0")
        if interval < 0:
            raise ValueError("interval must be greater than or equal to 0")
        if batch_interval < 0:
            raise ValueError("batch_interval must be greater than or equal to 0")

    def cancel(self) -> None:
        """取消所有待执行的任务"""
        self._cancelled.set()
        logger.info(f"[{self.task_name}] 任务执行器已取消")

    def is_cancelled(self) -> bool:
        """检查是否已取消"""
        return self._cancelled.is_set()

    def run(self, func: Callable[..., Any], *args, **kwargs) -> Any:
        """
        执行多线程任务，只有在发生异常时才重试
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            **kwargs: 函数关键字参数
        Returns:
            函数的返回值，如果所有重试都失败则返回 False
        Raises:
            Exception: 如果 raise_exception=True，则在最后一次重试失败时抛出异常
        """
        if self.is_cancelled():
            logger.warning(f"[{self.task_name}] 任务已取消，跳过执行")
            return False

        last_error: Optional[Exception] = None
        task_id = f"{self.task_name}-{time.strftime('%Y%m%d%H%M%S')}"

        @wraps(func)
        def wrapped_func(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.success(
                    f"[{task_id}] 任务执行完成，耗时: {execution_time:.2f}秒，结果: {result}"
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"[{task_id}] 任务执行异常，耗时: {execution_time:.2f}秒, 错误: {str(e)}"
                )
                raise

        for attempt in range(self.retries):
            if self.is_cancelled():
                logger.warning(f"[{task_id}] 任务在重试过程中被取消")
                return False

            try:
                with ThreadPoolExecutor(
                    max_workers=1,  # 单任务执行只需要一个线程
                    thread_name_prefix=f"{task_id}-Worker-{attempt+1}",
                ) as executor:
                    future = executor.submit(wrapped_func, *args, **kwargs)

                    # 等待任务完成或超时
                    done, _ = wait(
                        [future], timeout=self.timeout, return_when=ALL_COMPLETED
                    )

                    # 超时异常，需要重试
                    if not done:
                        error_msg = f"[{task_id}] 任务执行超时 (attempt {attempt + 1}/{self.retries}, timeout={self.timeout}s)"
                        logger.error(error_msg)
                        last_error = TimeoutError(error_msg)
                        future.cancel()  # 尝试取消超时的任务
                        continue

                    # 执行异常，需要重试
                    if future.exception():
                        last_error = future.exception()
                        logger.error(
                            f"[{task_id}] 任务执行异常 (attempt {attempt + 1}/{self.retries}): {str(last_error)}"
                        )
                        continue

                    # 获取结果并直接返回
                    result = future.result()
                    return result

            except Exception as e:
                # 其他异常，需要重试
                last_error = e
                logger.error(
                    f"[{task_id}] 任务执行过程异常: {str(e)} (attempt {attempt + 1}/{self.retries})"
                )

            if attempt < self.retries - 1:
                logger.info(
                    f"[{task_id}] 等待 {self.interval} 秒后进行第 {attempt + 2} 次重试"
                )
                time.sleep(self.interval)

        # 所有重试都失败了
        logger.error(f"[{task_id}] 任务执行失败，已重试 {self.retries} 次")
        
        if self.raise_exception and last_error:
            raise last_error

        return False

    def _execute_single_task(self, func: Callable, item: Any, *args, **kwargs) -> Any:
        """
        执行单个任务的包装方法
        Args:
            func: 要执行的函数
            item: 任务项
            *args: 额外的函数位置参数
            **kwargs: 额外的函数关键字参数
        Returns:
            任务执行结果
        """
        return self.run(func, item, *args, **kwargs)

    def run_batch(self, func: Callable[..., Any], items: List[Any], *args, **kwargs) -> List[Any]:
        """
        批量执行多线程任务
        Args:
            func: 要执行的函数
            items: 任务项列表，每一项会作为func的第一个参数
            *args: 额外的函数位置参数
            **kwargs: 额外的函数关键字参数
        Returns:
            list: 所有任务的执行结果列表
        """
        if self.is_cancelled():
            logger.warning(f"[{self.task_name}] 批量任务已取消")
            return [False] * len(items)

        task_id = f"{self.task_name}-batch-{time.strftime('%Y%m%d%H%M%S')}"
        total_tasks = len(items)
        logger.info(
            f"[{task_id}] 开始批量执行任务，共 {total_tasks} 个任务，并发数 {self.workers}"
        )

        with ThreadPoolExecutor(
            max_workers=self.workers, thread_name_prefix=f"{task_id}-Worker"
        ) as executor:
            # 提交所有任务
            future_to_item = {}
            for i, item in enumerate(items):
                if self.is_cancelled():
                    logger.warning(f"[{task_id}] 批量任务在提交过程中被取消")
                    break
                    
                future = executor.submit(self._execute_single_task, func, item, *args, **kwargs)
                future_to_item[future] = (i, item)
                
                # 添加提交间隔，避免瞬间提交大量任务
                if i < len(items) - 1:  # 最后一个任务不需要等待
                    time.sleep(self.batch_interval)

            # 等待所有任务完成并收集结果
            results = [None] * total_tasks
            completed_count = 0
            
            for future in as_completed(future_to_item, timeout=self.timeout * total_tasks):
                item_index, item = future_to_item[future]
                try:
                    result = future.result()
                    results[item_index] = result
                    completed_count += 1
                    
                    # 调用进度回调
                    if self.progress_callback:
                        self.progress_callback(completed_count, total_tasks)
                        
                    logger.debug(f"[{task_id}] 任务 {item_index + 1}/{total_tasks} 完成，结果: {result}")
                    
                except Exception as e:
                    logger.error(f"[{task_id}] 任务 {item_index + 1} 执行失败: {e}")
                    results[item_index] = False
                    completed_count += 1
                    
                    if self.progress_callback:
                        self.progress_callback(completed_count, total_tasks)

            # 处理未完成的任务（如果有的话）
            for i, result in enumerate(results):
                if result is None:
                    results[i] = False

            # 统计结果
            success_count = sum(1 for r in results if r not in [False, None])
            if success_count == total_tasks:
                logger.success(
                    f"[{task_id}] 所有任务执行成功: {success_count}/{total_tasks}"
                )
            else:
                logger.warning(
                    f"[{task_id}] 部分任务执行失败: {success_count}/{total_tasks}"
                )

            return results
