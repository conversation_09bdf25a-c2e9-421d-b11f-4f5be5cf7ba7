[project]
name = "dp_common"
version = "0.1.0"
description = "Automation tools for crypto tasks"
requires-python = ">=3.12.6"
dependencies = [
    # 构建工具
    "setuptools>=61.0,<81.0",

    # 核心依赖
    "loguru==0.7.3",
    "click==8.1.8",
    "python-dateutil==2.9.0",
    "python-dotenv==1.0.1",
    "requests==2.32.3",
    "urllib3==2.3.0",
    "certifi==2024.12.14",

    # Web3相关
    "web3==7.6.1",
    "eth-account==0.13.4",
    "eth-utils==5.1.0",
    "eth-hash==0.7.0",
    "eth-keyfile==0.8.1",
    "eth-keys==0.6.0",
    "eth-rlp==2.1.0",
    "eth-typing==5.1.0",
    "eth_abi==5.1.0",
    "python-okx==0.3.5",
    "pycryptodome==3.21.0",
    "mnemonic==0.21",
    "bip-utils==2.9.3",
    "ecdsa==0.19.0",
    "coincurve==20.0.0",
    "hexbytes==1.2.1",
    "rlp==4.0.1",
    "cytoolz==1.0.1",
    "toolz==1.0.0",

    # 加密相关
    "cryptography==44.0.0",
    "PyNaCl==1.5.0",
    "pyOpenSSL==24.3.0",
    "ed25519-blake2b==1.4.1",
    "py-sr25519-bindings==0.2.1",

    # 浏览器自动化
    "DrissionPage==********",
    "pyautogui==0.9.54",
    "pyperclip==1.9.0",
    "opencv-python==*********",
    "pillow==11.1.0",
    "PyGetWindow==0.0.9",
    "PyMsgBox==1.0.9",
    "PyRect==0.2.0",
    "PyScreeze==1.0.1",
    "pytweening==1.2.0",
    "MouseInfo==0.1.3",
    "psutil==6.1.1",

    # 数据处理
    "pandas==2.2.3",
    "numpy==2.2.1",
    "bs4==0.0.2",
    "beautifulsoup4==4.12.3",
    "pypinyin==0.53.0",
    "openpyxl==3.1.5",
    "lxml==5.3.0",
    "cssselect==1.2.0",
    "soupsieve==2.6",

    # 网络请求
    "aiohttp==3.11.11",
    "aiohappyeyeballs==2.4.4",
    "aiosignal==1.3.2",
    "httpx==0.28.1",
    "httpcore==1.0.7",
    "h11==0.14.0",
    "h2==4.1.0",
    "hpack==4.0.0",
    "hyperframe==6.0.1",
    "curl_cffi==0.10.0",
    "websocket-client==0.59.0",
    "websockets==13.1",

    # 工具库
    "retry==0.9.2",
    "pyotp==2.9.0",
    "Faker==33.3.1",
    "fake-useragent==2.0.3",
    "tqdm==4.67.1",
    "filelock==3.16.1",
    "filetype==1.2.0",
    "regex==2024.11.6",
    "parsimonious==0.10.0",
    "decorator==5.1.1",

    # Discord相关
    "discum==1.4.1",

    # AI相关
    "openai==1.59.7",

    # 数据验证
    "pydantic==2.10.5",
    "pydantic_core==2.27.2",

    # 系统相关
    "pyobjc-core==10.3.2; sys_platform == 'darwin'",
    "pyobjc-framework-Cocoa==10.3.2; sys_platform == 'darwin'",
    "pyobjc-framework-Quartz==10.3.2; sys_platform == 'darwin'",
    "rubicon-objc==0.5.0; sys_platform == 'darwin'",

    # 其他依赖
    "attrs==24.3.0",
    "anyio==4.8.0",
    "sniffio==1.3.1",
    "idna==3.10",
    "charset-normalizer==3.4.1",
    "multidict==6.1.0",
    "yarl==1.18.3",
    "frozenlist==1.5.0",
    "propcache==0.2.1",
    "six==1.17.0",
    "typing_extensions==4.12.2",
    "annotated-types==0.7.0",
    "pytz==2024.2",
    "tzdata==2024.2",
    "zipp==3.21.0",
    "importlib_metadata==8.5.0",
    "more-itertools==10.5.0",
    "jiter==0.8.2",
    "colorama==0.4.6",
    "distro==1.9.0",
    "tldextract==5.1.3",
    "ua-parser==1.0.0",
    "ua-parser-builtins==0.18.0.post1",
    "pyunormalize==16.0.0",
    "crcmod==1.7",
    "bitarray==3.0.0",
    "cbor2==5.6.5",
    "asn1crypto==1.5.1",
    "cffi==1.17.1",
    "pycparser==2.22",
    "Brotli==1.1.0",
    "ckzg==2.0.1",
    "constantly==23.10.4",
    "Automat==24.8.1",
    "hyperlink==21.0.0",
    "incremental==24.7.2",
    "Twisted==24.11.0",
    "zope.interface==7.2",
    "PySocks==1.7.1",
    "requests-file==2.1.0",
    "requests-toolbelt==1.0.0",
    "types-requests==2.32.0.20241016",
    "et_xmlfile==2.0.0",
    "py==1.11.0",
    "keyring==25.6.0",
    "jaraco.classes==3.4.0",
    "jaraco.context==6.0.1",
    "jaraco.functools==4.1.0",
    "DataRecorder==3.6.2",
    "DownloadKit==2.0.7",
]

[project.optional-dependencies]
dev = [
    "black==23.12.1",
    "isort==5.13.2",
    "pre-commit==3.6.0",
    "pytest==7.4.0",
    "pytest-cov==4.1.0",
    "pytest-asyncio==0.21.0",
]

[build-system]
requires = ["setuptools>=61.0,<81.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["."]  # 在当前目录下查找包
include = ["src*", "config*", "data*"]
exclude = ["tests*", "examples*"]

[tool.setuptools.package-data]
"src" = ["*.py"]  # 确保包含所有 Python 文件

[tool.pytest.ini_options]
pythonpath = [
    "."
]

[tool.black]
line-length = 120
target-version = ["py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
    # 排除的目录
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | __pycache__
  | \.env
  | env
  | venv
  | logs
)/
'''
# 启用预览功能以获得最新的格式化特性
preview = true
# 跳过字符串标准化（保持原有的引号风格）
skip-string-normalization = false
# 跳过魔术尾随逗号
skip-magic-trailing-comma = false

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 120
known_first_party = ["src", "config", "cli", "story", "utils"]
skip = [
    ".eggs",
    ".git",
    ".mypy_cache",
    ".tox",
    ".venv",
    "_build",
    "buck-out",
    "build",
    "dist",
    "__pycache__",
    ".env",
    "env",
    "venv",
    "logs",
]

[tool.setuptools]
py-modules = []
