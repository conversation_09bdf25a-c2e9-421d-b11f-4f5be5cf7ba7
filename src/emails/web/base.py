from abc import ABC, abstractmethod
from dataclasses import dataclass
from DrissionPage._pages.chromium_page import Chromium


@dataclass
class WebMailConfig:
    """Web邮件配置"""

    email: str  # 邮箱地址
    password: str  # 密码


class BaseWebMailClient(ABC):
    """Web邮件客户端基类"""

    def __init__(self, page: Chromium, id: str):
        """初始化Web邮件客户端

        Args:
            page: 浏览器控制器实例
            id: 浏览器序号
        """
        self.page = page
        self.id = id

    @abstractmethod
    def login(self, email: str, password: str) -> bool:
        """登录邮箱"""
        pass

    @abstractmethod
    def logout(self) -> None:
        """登出邮箱"""
        pass

    @abstractmethod
    def update_password(self, password: str, new_password: str) -> bool:
        """更新密码

        Args:
            password: 当前密码
            new_password: 新密码

        Returns:
            bool: 更新是否成功

        Raises:
            MailAuthError: 认证失败
            MailConnectionError: 连接失败
        """
        pass

    @abstractmethod
    def update_bind_email(self, email: str) -> bool:
        """更新绑定邮箱

        Args:
            email: 新的绑定邮箱

        Returns:
            bool: 更新是否成功

        Raises:
            MailAuthError: 认证失败
            MailConnectionError: 连接失败
        """
        pass

    @property
    @abstractmethod
    def home_url(self) -> str:
        """邮箱首页URL"""
        pass
