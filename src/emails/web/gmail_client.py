from loguru import logger
from typing import Callable

from .base import BaseWebMailClient
from time import sleep
from DrissionPage import Chromium


class GmailWebClient(BaseWebMailClient):
    def __init__(self, page: Chromium, id: str):
        super().__init__(page, id)

    def clear_cache(self) -> bool:
        try:
            self.browser.clear_site_data("mail.google.com")
            return True
        except Exception as e:
            logger.error(f"清除缓存失败: {str(e)}")
            return False

    def get_gmail_verification_url(
        self,
        sender_email: str,
        get_link_func: Callable,
        max_retries: int = 20,
        wait_time: int = 3,
    ) -> str:
        """
        从Gmail中获取验证链接的通用方法

        Args:
            page: 浏览器页面对象
            sender_email: 发件人邮箱
            get_link_func: 自定义获取验证链接的函数，接收 lasted_tab 参数
            max_retries: 最大重试次数
            wait_time: 点击邮件后等待时间(秒)

        Returns:
            str: 验证链接URL
        """
        lasted_tab = self.page.new_tab(self.home_url)
        try:
            # 等待验证邮件出现
            email_button = None
            for _ in range(max_retries):
                email_button = lasted_tab.ele(
                    f"x://span[@email='{sender_email}']", timeout=3
                )
                if email_button:
                    break
                lasted_tab.refresh()
                sleep(3)

            if not email_button:
                raise Exception("获取验证邮件失败")

            email_button.click(True)
            sleep(wait_time)

            # 使用自定义函数获取验证链接
            verification_url = get_link_func(lasted_tab)
            if not verification_url:
                raise Exception("获取验证链接失败")

            return verification_url

        except Exception as e:
            logger.error(f"获取Gmail验证链接失败: {e}")
            raise
        finally:
            lasted_tab.close()

    def login(self, email: str, password: str) -> bool:
        try:
            self.page.get(self.home_url)
            self.page.close_tabs(others=True)

            sign_out_button = self.page.ele(
                "x://a[contains(@href, 'accounts.google.com/SignOutOptions')]",
                timeout=5,
            )
            if sign_out_button:
                logger.success(f"{self.id} Gmail 已登录")
                return True

            logger.info(f"登录邮箱: {email}")
            logger.info(f"登录密码: {password}")

            if not email or not password:
                logger.error(f"{self.id} 邮箱或密码为空")
                return False

            sign_in_button = self.page.ele(
                "x:(//span[@class='button__label' and contains(text(), 'Sign in')])[2]",
                timeout=5,
            )
            if not sign_in_button:
                raise Exception("未找到登录按钮")

            sign_in_button.click(True)

            # //div[@class='header__aside']/a[contains(@href, 'signin')]
            # //input[@type="email"]
            self.page.ele(
                "x://input[@type='email']",
                timeout=5,
            ).input(email)

            self.page.ele(
                "x://span[text()='Next']",
                timeout=5,
            ).click(True)

            sleep(5)

            password_input = self.page.ele(
                "x://input[@type='password']",
                timeout=5,
            )
            password_input.clear(True)
            password_input.input(password)

            sleep(3)
            self.page.ele(
                "x://span[text()='Next']",
                timeout=5,
            ).click(True)

            # TODO :后续还有业务，等待开发

            logger.success(f"登录成功: {email}")
            return True
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            return False

    def logout(self) -> bool:
        try:
            if not self.is_logged_in:
                return True

            self.is_logged_in = False
            logger.info("登出成功")
            return True

        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
            return False

    def update_password(self, old_password: str, new_password: str) -> bool:
        try:
            if not self.is_logged_in:
                logger.error("未登录")
                return False

            logger.info("密码修改成功")
            return True

        except Exception as e:
            logger.error(f"修改密码失败: {str(e)}")
            return False

    def update_bind_email(self, email: str) -> bool:
        pass

    @property
    def home_url(self) -> str:
        return "https://mail.google.com/mail/u/0/#inbox"
