from .imap4.imap_client import <PERSON><PERSON>onfig, IMAPClient, SearchCriteria
from .imap4.hotmail_client import Hotmail<PERSON>lient
from .imap4.gmail_client import Gmail<PERSON>lient
from .imap4.icloud_client import ICloudClient
from .providers import MailProvider
from .web.base import <PERSON><PERSON>ailConfig, BaseWebMailClient
from .web.gmail_client import GmailWebClient
from .web.hotmail_client import HotmailWebClient
from .exceptions import (
    MailConnectionError,
    MailAuthError,
    MailOperationError,
)


__all__ = [
    "MailProvider",
    "MailConfig",
    "IMAPClient",
    "SearchCriteria",
    "WebMailConfig",
    "BaseWebMailClient",
    "MailConnectionError",
    "MailAuthError",
    "MailOperationError",
    "GmailWebClient",
    "HotmailWebClient",
    "GmailClient",
    "HotmailClient",
    "ICloudClient",
]
