from loguru import logger

from ..exceptions import MailAuthError
from .imap_client import IMAPClient, MailConfig


class ICloudClient(IMAPClient):
    """iCloud 邮件客户端

    专门处理 iCloud 邮箱的 IMAP 客户端，继承自基础的 IMAPClient。
    提供了 iCloud 特定的配置和功能。
    """

    SUPPORTED_DOMAINS = ("icloud.com", "me.com", "mac.com")
    IMAP_SERVER = "imap.mail.me.com"
    IMAP_PORT = 993

    def __init__(self, email: str, password: str):
        """初始化 iCloud 邮件客户端

        Args:
            email: iCloud 邮箱地址
            password: 应用专用密码（App-specific password）

        Note:
            iCloud 需要使用应用专用密码，而不是 Apple ID 的密码。
            获取方式：https://appleid.apple.com/ -> 安全性 -> 应用专用密码
        """
        if not email.endswith(("@icloud.com", "@me.com", "@mac.com")):
            raise ValueError("邮箱地址必须是 iCloud 邮箱（@icloud.com、@me.com 或 @mac.com）")

        config = MailConfig(
            email=email,
            password=password,
            imap_server=self.IMAP_SERVER,
            imap_port=self.IMAP_PORT,
        )
        super().__init__(config)

    def login(self) -> bool:
        """登录 iCloud 邮箱

        重写登录方法，添加 iCloud 特定的错误处理

        Returns
        -------
            bool: 登录是否成功

        Raises
        ------
            MailAuthError: 认证失败，通常是因为使用了 Apple ID 密码而不是应用专用密码
        """
        try:
            return super().login()
        except MailAuthError as e:
            logger.error("iCloud 认证失败，请确保使用的是应用专用密码而不是 Apple ID 密码")
            raise MailAuthError("iCloud 认证失败：请使用应用专用密码（可在 https://appleid.apple.com/ 生成）") from e
