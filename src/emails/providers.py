import re
from enum import Enum
from typing import Optional
from dataclasses import dataclass


class MailProvider(Enum):
    """邮件服务提供商"""

    GMAIL = "imap.gmail.com"
    HOTMAIL = "outlook.office365.com"
    OUTLOOK = "outlook.office365.com"
    GMX = "imap.gmx.com"
    RAMBLER = "imap.rambler.ru"
    ICLOUD = "imap.mail.me.com"

    @classmethod
    def get_provider(cls, email: str) -> Optional[str]:
        """根据邮箱获取IMAP服务器

        Args:
            email: 邮箱地址

        Returns:
            Optional[str]: IMAP服务器地址
        """
        match = re.search(r"@(.+?)\.", email)
        domain = match.group(1).upper() if match else None
        try:
            return cls[domain].value
        except KeyError:
            return None
