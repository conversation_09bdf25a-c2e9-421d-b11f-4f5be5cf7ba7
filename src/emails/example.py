from loguru import logger
from src.emails.imap4.icloud_client import ICloudClient
from src.emails.imap4.gmail_client import GmailClient
from src.emails.imap4.hotmail_client import HotmailClient
from config import (
    ICLOUD_EMAIL,
    ICLOUD_EMAIL_PASSWORD,
)


def icloud_mail_example():
    """iCloud邮箱示例"""
    if not ICLOUD_EMAIL or not ICLOUD_EMAIL_PASSWORD:
        raise ValueError("iCloud邮箱配置未找到, 请检查配置文件 .env")

    # 使用with语句自动登出
    with ICloudClient(ICLOUD_EMAIL, ICLOUD_EMAIL_PASSWORD) as client:
        result = client.login()
        if not result:
            raise ValueError("登录失败")

        # 获取最新邮件内容
        email_content = client.get_latest_email()
        logger.info(f"获取到最新邮件: {email_content}")

        # 获取最新10封邮件
        emails = client.search_emails(limit=10)
        logger.info(f"获取到最新10封邮件: {len(emails)}封")

        # 搜索2024年1月1日之后，来自特定地址的包含"报告"的邮件
        emails = client.search_emails(
            subject="报告",
            from_addr="<EMAIL>",
            since="1-Jan-2024",
        )
        logger.info(f"搜索到包含'报告'的邮件: {len(emails)}封")


def gmail_mail_example():
    """Gmail邮箱示例"""
    email = "<EMAIL>"
    password = "test123456"

    if not email and not password:
        raise ValueError("Gmail邮箱配置错误")

    with GmailClient(email, password) as client:
        result = client.login()
        if not result:
            raise ValueError("登录失败")

        # 获取未读邮件
        unread_emails = client.search_emails(criteria="UNSEEN")
        logger.info(f"未读邮件数量: {len(unread_emails)}封")

        # 搜索特定主题的邮件
        important_emails = client.search_emails(
            subject="重要",
            since="1-Jan-2024",
            limit=5,
        )
        logger.info(f"重要邮件: {important_emails}")

        # 获取带附件的邮件
        emails_with_attachments = [
            email
            for email in client.search_emails(limit=20)
            if email.get("has_attachments")
        ]
        logger.info(f"带附件的邮件数量: {len(emails_with_attachments)}封")


def hotmail_mail_example():
    """Hotmail/Outlook邮箱示例"""
    email = "<EMAIL>"
    password = "test123456"

    if not email and not password:
        raise ValueError("Hotmail邮箱配置错误")

    with HotmailClient(email, password) as client:
        result = client.login()
        if not result:
            raise ValueError("登录失败")

        # 获取收件箱中的最新邮件
        latest_email = client.get_latest_email()
        if latest_email:
            logger.info(f"最新邮件主题: {latest_email.get('subject')}")
            logger.info(f"发件人: {latest_email.get('from')}")
            logger.info(f"日期: {latest_email.get('date')}")

        # 搜索特定发件人的邮件
        sender_emails = client.search_emails(
            from_addr="<EMAIL>", limit=5, sort_order="DESC"
        )
        logger.info(f"特定发件人的邮件: {len(sender_emails)}封")

        # 搜索包含HTML内容的邮件
        html_emails = [
            email
            for email in client.search_emails(limit=10)
            if email.get("html_content")
        ]
        logger.info(f"包含HTML内容的邮件数量: {len(html_emails)}封")


def main():
    """主函数：运行所有邮件客户端示例"""
    try:
        logger.info("=== 开始 iCloud 邮箱测试 ===")
        icloud_mail_example()
    except Exception as e:
        logger.error(f"iCloud 邮箱测试失败: {str(e)}")

    try:
        logger.info("=== 开始 Gmail 邮箱测试 ===")
        gmail_mail_example()
    except Exception as e:
        logger.error(f"Gmail 邮箱测试失败: {str(e)}")

    try:
        logger.info("=== 开始 Hotmail 邮箱测试 ===")
        hotmail_mail_example()
    except Exception as e:
        logger.error(f"Hotmail 邮箱测试失败: {str(e)}")


def web_gmail_example():
    """Web Gmail邮箱示例"""
    from src.controllers import BrowserController
    from src.browsers import BrowserType
    from src.emails import GmailWebClient

    controller = BrowserController(browser_type=BrowserType.ADS, id="22")

    def get_colormp_verify_link(tab) -> str:
        verify_link = tab.ele(
            """x://a[contains(@href, 'sendgrid.net') and contains(text(),'Verify Email')]""",
            timeout=5,
        )
        return verify_link.attr("href") if verify_link else None

    gmail_client = GmailWebClient(controller)
    gmail_client.login(
        email="<EMAIL>",
        password="test123456",
    )
    url = gmail_client.get_gmail_verification_url(
        sender_email="<EMAIL>",
        get_link_func=get_colormp_verify_link,
    )

    logger.info(f"获取到验证链接: {url}")


if __name__ == "__main__":
    # main()
    pass
