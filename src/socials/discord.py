import random
import re
from time import sleep

import pyotp
from DrissionPage import Chromium
from loguru import logger

from src.browsers.operations import try_click
from src.emails.imap4.email_client import EmailClient, SearchCriteria
from src.emails.web.mixmail_client import MixMailWebClient
from src.socials.base import BaseSocial
from src.utils.common import generate_login_name
from src.utils.element_util import get_element, get_elements
from src.utils.password import generate_strong_password
from src.utils.yescaptcha import YesCaptcha


class Discord(BaseSocial):
    def __init__(
        self, id: str, page: Chromium, browser_type: object = None, invite_code: str = None, dc_name: str = None
    ) -> None:
        super().__init__(id, page)
        self._home_url = "https://discord.com/channels/@me"
        self._login_url = "https://discord.com/login"
        self.browser_type = browser_type
        self._invite_code = invite_code
        self.dc_name = dc_name or invite_code

    def get_token(self):
        last_tab = self._page.latest_tab
        last_tab.listen.start("https://discord.com/api/v9/science")
        last_tab.get("https://discord.com/channels/@me")

        token = None
        for packet in last_tab.listen.steps(count=2, timeout=60):
            headers = packet.request.headers
            token = headers.get("Authorization")
            if token:
                break
        return token

    def _get_captcha_token(self, page) -> str | None:
        return YesCaptcha.get_web_plugin_token_hcaptcha(page, self.id)

    # 获取用户名
    def get_email(self) -> str | None:
        try:
            tab = self._page.new_tab(self._home_url)

            logger.info(f"【{self.id}】获取email，等待元素加载中...")
            if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=90):
                logger.error(f"【{self.id}】获取username失败，元素加载超时")
                return ""

            get_element(tab, "x://button[@aria-label='User Settings']", 5).click()

            try_click(tab, "x://button[@aria-label='Reveal email address']")

            email_ele = get_element(tab, "x://h2[.='Email']/following-sibling::div//span", 5)
            email_text = email_ele.text.strip()

            if email_text.endswith("Hide"):
                email_text = email_text[: -len("Hide")]

            return email_text
        except Exception as e:
            logger.error(f"【{self.id}】获取username异常，error={str(e)}")
            return ""

    # 获取用户名
    def get_username(self) -> str:
        try:
            tab = self._page.latest_tab
            tab.get(self._home_url)

            logger.info(f"【{self.id}】获取username，等待元素加载中...")
            if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=90):
                logger.error(f"【{self.id}】获取username失败，元素加载超时")
                return ""

            tab.listen.start(
                targets="https://discord\\.com/api/v9/users/(\\d+)/profile\\?with_mutual_guilds=false&with_mutual_friends=false&with_mutual_friends_count=false",
                is_regex=True,
            )
            get_element(tab, "x://button[@aria-label='User Settings']", 5).click()
            res = tab.listen.wait(timeout=90)
            if res and res.response and res.response.body:
                username = res.response.body.get("user", {}).get("username", "")
                if username:
                    logger.success(f"【{self.id}】获取username成功，username={username}")
                return username
            return ""
        except Exception as e:
            logger.error(f"【{self.id}】获取username异常，error={str(e)}")
            return ""

    def get_uid(self) -> str:
        try:
            tab = self._page.latest_tab
            tab.get(self._home_url)

            logger.info(f"【{self.id}】获取uid，等待元素加载中...")
            if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=90):
                logger.error(f"【{self.id}】获取uid失败，请检查是否登录或网络异常")
                return ""

            tab.listen.start(
                targets="https://discord\\.com/api/v9/users/(\\d+)/profile\\?with_mutual_guilds=false&with_mutual_friends=false&with_mutual_friends_count=false",
                is_regex=True,
            )
            get_element(tab, "x://button[@aria-label='User Settings']", 5).click()
            res = tab.listen.wait(timeout=90)
            if res and res.response and res.response.body:
                uid = res.response.body.get("user", {}).get("id", "")
                if uid:
                    logger.success(f"【{self.id}】获取uid成功，uid={uid}")
                return uid
            return ""
        except Exception as e:
            logger.error(f"【{self.id}】获取uid异常，error={str(e)}")
            return ""

    def leave_guild(self, group_name: str):
        try:
            tab = self._page.new_tab(self._home_url)
            sleep(5)

            eles = get_elements(tab, "x://ul[@role='tree']//div[contains(@class, 'listItem__')]", 3)
            if len(eles) <= 5:
                logger.warning(f"【{self.id}】 您未加入任何群组, 请检查您的账号...")
                return False

            ele = get_element(
                tab,
                f"x://div[contains(@role, 'treeitem')]//span[contains(.,'{group_name}')]",
                3,
            )
            if not ele:
                logger.warning(f"【{self.id}】 退出群组失败，未找到群组")
                return False

            ele.click.right()
            sleep(1)

            leave_ele = get_element(tab, "x://div[@id='guild-context-leave-guild']", 3)
            if not leave_ele:
                logger.warning(f"【{self.id}】 退出群组失败，未找到退出按钮")
                return False

            leave_ele.click()
            sleep(1)

            confirm_ele = get_element(tab, "x://div[@role='dialog']//button[@type='submit']", 3)
            if not confirm_ele:
                logger.warning(f"【{self.id}】 退出群组失败，未找到确认按钮")
                return False

            confirm_ele.click()
            sleep(5)

            ele = get_element(
                tab,
                f"x://div[contains(@role, 'treeitem')]//span[contains(.,'{group_name}')]",
                3,
            )
            if not ele:
                logger.success(f"【{self.id}】 退出群组成功")
                return True

            return False
        except Exception:
            logger.warning(f"【{self.id}】 退出群组失败")

    def get_2fa_key(self, password: str) -> dict:
        for _ in range(3):
            tab = self._page.new_tab(self._home_url)

            try:
                get_element(tab, "x://button[@aria-label='User Settings']", 5).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 开启2FA失败，未找到Setting 按钮， retry...")

            try:
                tab.scroll.to_bottom()
                get_element(tab, "x://span[text()='Enable Authenticator App']/ancestor::button", 5).click()
                sleep(2)
            except Exception:
                if get_element(tab, "x://span[text()='Remove Authenticator App']/ancestor::button", 5):
                    logger.success(f"【{self.id}】 已开启了2FA验证")
                    return {"success": True, "2fa": "", "backup_code": ""}
                logger.warning(f"【{self.id}】 开启2FA失败，未找到Enable Authenticator App按钮， retry...")

            fa_key = None
            try:
                fa_key_div = get_element(
                    tab,
                    "x://h2[text()='2FA Key (Manual entry)']/following-sibling::div[1]",
                    timeout=5,
                )
                fa_key = fa_key_div.text.strip().replace(" ", "")
                code = pyotp.TOTP(fa_key).now()
                code_i = get_element(
                    tab,
                    "x://span[text()='Activate']/ancestor::button/preceding-sibling::div[1]/input",
                    3,
                )
                code_i.clear(True)
                code_i.input(code)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 开启2FA失败，未找到2FA Key， retry...")

            try:
                get_element(tab, "x://span[text()='Activate']/ancestor::button", timeout=3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 开启2FA失败，未找到Activate按钮， retry...")
            try:
                pwd_i = get_element(
                    tab,
                    "x://h2[text()='Password']/following-sibling::div[1]//input[@type='password']",
                    3,
                )

                pwd_i.clear(True)
                pwd_i.input(password)
                sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】 开启2FA失败，未找到开密码输入框， retry...")

            try:
                get_element(tab, "x://span[text()='Confirm']/ancestor::button", timeout=3).click()
                tab.wait.ele_displayed("x://h2[text()='2FA Key (Manual entry)']", 6)
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 开启2FA失败，未找到开启Continue按钮， retry...")

            if get_element(tab, "x://span[contains(text(),'2FA is activated')]", 15):
                logger.success(f"【{self.id}】 开启2FA成功, fa_key={fa_key}")
                # backup_code = self._get_backup_code(tab)
                return {
                    "success": True,
                    "2fa": fa_key,
                    # "backup_code": backup_code
                }

        return {"success": False, "2fa": "", "backup_code": ""}

    def _get_backup_code(self, tab):
        try:
            get_element(tab, "x://button[@aria-label='Close' and @type='button']", 3).click()
            sleep(2)
            get_element(tab, "x://div[contains(text(),'m sure')]/parent::button", 3).click()
            sleep(2)
            codes = get_elements(
                tab,
                "x://span[contains(@class, 'checkboxWrapper')]//span[contains(@class, 'code')]",
                3,
            )
            backup_codes = [code.text.strip() for code in codes]
            return "|".join(backup_codes)
        except Exception:
            logger.warning(f"【{self.id}】 获取备份码失败")
            return None

    def _fill_login_form(self, url: str, username: str, password: str) -> bool:
        """填写登录表单.

        Args:
            username: 用户名
            password: 密码

        Returns
        -------
            bool: 是否成功输入
        """
        try:
            tab = self._page.latest_tab
            tab.get(url)

            # 点击登录按钮
            login_btn = get_element(tab, "x://span[text()='Log in']/ancestor::button", 3)
            if login_btn:
                login_btn.click()
                sleep(2)

            # 输入用户名
            get_element(tab, "x://input[@name='email']", 5).input(username)
            sleep(0.5)

            # 输入密码
            get_element(tab, "x://input[@name='password']", 5).input(password)

            # 点击提交
            tab.listen.start("https://discord.com/api/v9/auth/login")
            get_element(tab, "x://button[@type='submit']", 5).click()
            return True
        except Exception as e:
            logger.error(f"【{self.id}】输入登录信息失败: {str(e)}")
            return False

    @staticmethod
    def _get_response(tab) -> dict | None:
        """处理登录响应.

        Returns
        -------
            Optional[dict]: 响应数据,失败返回None
        """
        res = tab.listen.wait(timeout=90)
        if not res or not res.response or not res.response.body:
            return None
        return res.response.body

    def _check_error_code(self, response: dict, error_code: str) -> bool:
        """检查响应中是否包含指定的错误码.

        Args:
            response: 响应数据
            error_code: 要检查的错误码

        Returns
        -------
            bool: 是否包含指定错误码
        """
        return error_code in str(response)

    def _process_response(
        self,
        response: dict,
        username: str,
        password: str,
        email: str | None,
        email_password: str | None,
        proxy_email: str | None,
        fa: str | None,
    ) -> dict:
        """处理登录结果.

        Args:
            response: 登录响应数据
            username: 用户名
            password: 密码
            email: 邮箱
            email_password: 邮箱密码
            proxy_email: 代理邮箱
            fa: 2FA密钥

        Returns
        -------
            bool: 是否登录成功
            str: 密码
            str: token
        """
        tab = self._page.latest_tab

        # 处理账号被锁定情况
        if self._check_error_code(response, "ACCOUNT_COMPROMISED_RESET_PASSWORD"):
            logger.info(f"【{self.id}】进行重置密码")
            return self._handle_reset_password(email, email_password, proxy_email, fa)

        # 处理需要验证码情况
        if self._check_error_code(response, "captcha-required"):
            return self._handle_captcha(username, password, email, email_password, proxy_email, fa)

        # 处理2FA情况
        if self._is_2fa_required(response):
            return self._handle_2fa(tab, fa)

        # 处理邮箱验证情况
        if self._check_error_code(response, "ACCOUNT_LOGIN_VERIFICATION_EMAIL"):
            return self._handle_email_verification(username, password, email, email_password, proxy_email, fa)

        # 账号密码错误
        if self._check_error_code(response, "INVALID_LOGIN"):
            logger.error(f"【{self.id}】账号或密码错误")
            return {"success": False, "token": "", "code": "USERNAME_OR_PASSWORD_ERROR", "message": "用户名或密码错误"}
        return {
            "success": "token" in response,
            "token": response.get("token"),
        }

    def _handle_reset_password(
        self,
        email: str,
        email_password: str,
        proxy_email: str | None,
        fa: str | None,
        subject="Discord Account Disabled for Suspicious Activity",
    ) -> dict:
        """处理账号被锁定的情况.

        Args:
            email: 邮箱地址
            email_password: 邮箱密码
            proxy_email: 代理邮箱
            fa: 2FA密钥

        Returns
        -------
            bool: 是否成功解锁并登录
        """
        try:
            if not email or not email_password:
                logger.error(f"【{self.id}】辅助邮箱账号或密码为空")
                return {
                    "success": False,
                    "token": "",
                    "code": "EMAIL_OR_EMAIL_PASSWORD_IS_NONE",
                    "message": "邮箱或邮箱密码为空",
                }
            # 获取重置密码链接

            pattern = r"Reset Password: (https://click\.discord\.com/ls/click\?[^\s]+)"

            reset_pwd_url = self._get_verify_link_from_email(
                email, email_password, proxy_email, subject=subject, pattern=pattern
            )
            if not reset_pwd_url:
                logger.error(f"【{self.id}】未收到修改密码邮件")
                return {"success": False, "token": "", "code": "NOT_FOUND_EMAIL", "message": "未收到邮件"}

            # 打开重置密码页面
            tab = self._page.latest_tab
            tab.get(reset_pwd_url)

            # 生成并输入新密码
            new_password = generate_strong_password()
            logger.info(f"【{self.id}】设置新密码：{new_password}")
            get_element(tab, "x://input[@name='password']", 5).input(new_password)

            # 提交新密码
            tab.listen.start("https://discord.com/api/v9/auth/reset")
            get_element(tab, "x://button[@type='submit']", 5).click()

            # 处理响应
            response = self._get_response(tab)
            if not response:
                sleep(8)
                if "channels/@me" in tab.url:
                    token = self.get_token()
                    return {"success": True, "token": token, "password": new_password}

                logger.error(f"【{self.id}】重置密码失败: 未获取到响应")
                return {"success": False, "token": "", "code": "NO_RESPONSE", "message": "未获取到响应"}

            # 处理2FA验证
            if self._is_2fa_required(response):
                result = self._handle_2fa(tab, fa)
                result["code"] = "RESET_PASSWORD" if result.get("success", False) else ""
                result["password"] = new_password if result.get("success", False) else ""
                return result

            # 检查是否重置成功
            return {"success": "token" in response, "token": response.get("token", "")}

        except Exception as e:
            logger.error(f"【{self.id}】重置密码失败: {str(e)}")
            return {
                "success": False,
                "token": "",
            }

    def _handle_captcha(
        self,
        username: str,
        password: str,
        email: str | None,
        email_password: str | None,
        proxy_email: str | None,
        fa: str | None,
    ) -> dict:
        """处理需要验证码的情况.

        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            email_password: 邮箱密码
            proxy_email: 代理邮箱
            fa: 2FA密钥

        Returns
        -------
            bool: 是否成功通过验证码并登录
        """
        try:
            tab = self._page.latest_tab

            logger.warning(f"【{self.id}】请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")

            # 监听登录请求
            tab.listen.start("https://discord.com/api/v9/auth/login")

            # 等待验证码元素消失
            if not tab.wait.ele_deleted("x://div[@aria-label='CAPTCHA']", 1200):
                return {
                    "success": False,
                    "token": "",
                }

            # 获取响应
            response = self._get_response(tab)

            if self._check_error_code(response, "ACCOUNT_COMPROMISED_RESET_PASSWORD"):
                logger.info(f"【{self.id}】进行重置密码")
                return self._handle_reset_password(email, email_password, proxy_email, fa)

            # 处理需要邮箱验证的情况
            if self._check_error_code(response, "ACCOUNT_LOGIN_VERIFICATION_EMAIL"):
                return self._handle_email_verification(username, password, email, email_password, proxy_email, fa)

            # 处理2FA验证
            if self._is_2fa_required(response):
                return self._handle_2fa(tab, fa)

            # 检查是否登录成功
            return {
                "success": "token" in response,
                "token": response.get("token", ""),
            }
        except Exception as e:
            logger.error(f"【{self.id}】处理验证码失败: {str(e)}")
            return {
                "success": False,
                "token": "",
            }

    def _handle_email_verification(
        self, username: str, password: str, email: str, email_password: str, proxy_email: str | None, fa: str | None
    ) -> dict:
        """处理需要邮箱验证的情况.

        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            email_password: 邮箱密码
            proxy_email: 代理邮箱
            fa: 2FA密钥

        Returns
        -------
            bool: 是否成功通过邮箱验证并登录
        """
        try:
            if not email or not email_password:
                logger.error(f"【{self.id}】辅助邮箱账号或密码为空")
                return {
                    "success": False,
                    "token": "",
                    "code": "EMAIL_OR_EMAIL_PASSWORD_IS_NONE",
                    "message": "辅助邮箱账号或密码为空",
                }

            # 获取验证链接
            subject = "Discord Account Disabled for Suspicious Activity"
            pattern = r"Reset Password: (https://click\.discord\.com/ls/click\?[^\s]+)"

            verify_url = self._get_verify_link_from_email(
                email, email_password, proxy_email, subject=subject, pattern=pattern
            )
            if not verify_url:
                logger.error(f"【{self.id}】未收到验证邮件")
                return {"success": False, "token": "", "code": "NOT_FOUND_EMAIL", "message": "未收到邮件"}

            # 重新输入登录信息
            if not self._fill_login_form(verify_url, username, password):
                return {
                    "success": False,
                    "token": "",
                }

            tab = self._page.latest_tab

            # 监听登录请求
            tab.listen.start("https://discord.com/api/v9/auth/login")

            # 点击提交
            get_element(tab, "x://button[@type='submit']", 5).click()

            # 获取响应
            response = self._get_response(tab)

            # 处理2FA验证
            if self._is_2fa_required(response):
                return self._handle_2fa(tab, fa)

            # 检查是否登录成功
            return {
                "success": "token" in response,
                "token": response.get("token", ""),
            }
        except Exception as e:
            logger.error(f"【{self.id}】处理邮箱验证失败: {str(e)}")
            return {
                "success": False,
                "token": "",
            }

    @staticmethod
    def _is_2fa_required(response: dict) -> bool:
        """检查是否需要2FA验证.

        Args:
            response: 登录响应数据

        Returns
        -------
            bool: 是否需要2FA
        """
        return "mfa" in response and "totp" in response and response["totp"]

    def _handle_2fa(self, tab, fa: str) -> dict:
        """处理2FA验证.

        Args:
            tab: 当前标签页
            fa: 2FA密钥

        Returns
        -------
            bool: 是否验证成功
        """
        try:
            if not fa:
                logger.error(f"【{self.id}】2FA key为空")
                return {"success": False, "token": "", "code": "2FA_KEY_IS_NONE", "message": "2FA key为空"}

            code = pyotp.TOTP(fa)
            get_element(tab, "x://input[@autocomplete='one-time-code']", 5).input(code.now())

            tab.listen.start([
                "https://discord.com/api/v9/auth/mfa/totp",
                "https://discord.com/api/v9/auth/reset",
                "https://discord.com/api/v9/science",
            ])
            get_element(tab, "x://button[@type='submit']", 5).click()
            reps = tab.listen.wait(count=2, timeout=90)
            for res in reps:
                url = res.url
                response = res.response.body
                if "auth" in url and response:
                    if self._check_error_code(response, "Invalid two-factor code"):
                        logger.error(f"【{self.id}】2FA Key错误")
                        return {"success": False, "token": "", "code": "2FA_KEY_ERROR"}
                    return {"success": "token" in response, "token": response.get("token", "")}
                if "science" in url:
                    headers = res.request.headers
                    return {"success": "Authorization" in headers, "token": headers.get("Authorization")}
            return {
                "success": False,
                "token": "",
            }
        except Exception as e:
            logger.error(f"【{self.id}】2FA验证失败: {str(e)}")
            return {
                "success": False,
                "token": "",
            }

    def login_by_password(
        self,
        username: str,
        password: str,
        email: str | None = None,
        email_password: str | None = None,
        proxy_email: str | None = None,
        fa: str | None = None,
    ) -> tuple[bool, str, str]:
        if not username or not password:
            logger.error(f"【{self.id}】 登录失败，账号密码必填")
            return False, password, ""
        t = self.get_token()
        if t:
            return True, password, t
        for retry in range(3):
            logger.info(f"【{self.id}】第 {retry + 1} 次尝试登录")
            try:
                # 1. 打开登录页并输入账号密码
                if not self._fill_login_form(self._login_url, username, password):
                    continue

                # 2. 监听登录请求并处理响应
                tab = self._page.latest_tab
                response = self._get_response(tab)

                # 3. 处理不同的登录情况
                result = self._process_response(response, username, password, email, email_password, proxy_email, fa)
                success = result.get("success", False)
                token = result.get("token", "")
                code = result.get("code", "")
                pwd = result.get("password", "") if code == "RESET_PASSWORD" else password
                message = result.get("message", "")
                if code in [
                    "EMAIL_OR_EMAIL_PASSWORD_IS_NONE",
                    "2FA_KEY_IS_NONE",
                    "2FA_KEY_ERROR",
                    "USERNAME_OR_PASSWORD_ERROR",
                ]:
                    logger.error(f"【{self.id}】登录出错, {message}")
                    return False, "", ""

                if success:
                    logger.success(f"【{self.id}】账号密码登录成功")
                    return True, pwd, token
            except Exception as e:
                logger.error(f"【{self.id}】账号密码登录出错: {str(e)}")
                continue

        return False, "", ""

    def login_by_token(self, token: str) -> bool:
        if not token:
            logger.warning("token 传入为空...")
            return False
        tab = self._page.new_tab(self._login_url)
        script = """
            function login(token) {
                setInterval(() => {
                    document.body.appendChild(document.createElement `iframe`).contentWindow.localStorage.token = `"${token}"`
                }, 50);
                setTimeout(() => {
                    location.reload();
                }, 2500);
            }
        """

        tab.run_js(script + f'\nlogin("{token}")', as_expr=True)
        if tab.wait.url_change("/channels/@me", timeout=30):
            logger.success(f"【{self.id}】登录成功")
            self.setting_language()
            return True
        else:
            logger.error(f"【{self.id}】登录失败")
            return False

    def logout(self) -> dict[str, str]:
        tab = self._page.new_tab(self._home_url)
        sleep(3)
        if "login" in tab.url:
            return {"success": True, "message": "已经在登录页面"}

        if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=90):
            logger.error(f"【{self.id}】获取User Settings失败，元素加载超时")
            return {"success": False, "message": "获取User Settings失败，元素加载超时"}

        get_element(tab, "x://button[@aria-label='User Settings']", 5).click()

        result = try_click(tab, "x://div[@aria-label='Log Out']")
        if not result:
            logger.error(f"{self.id} 点击Log Out 按钮失败")
            return {"success": False, "message": "点击Log Out 按钮失败"}

        result = try_click(tab, "x://button[@type='submit' and .='Log Out']")
        if not result:
            logger.error(f"{self.id} 点击确认 Log Out 按钮失败")
            return {"success": False, "message": "点击确认 Log Out 按钮失败"}

        result_tab = tab.wait.url_change("login", timeout=30)
        if result_tab == False:
            logger.error(f"{self.id} 登出后没有跳转到登录页面")
            return {"success": False, "message": "登出后没有跳转到登录页面"}

        return {"success": True, "message": "登出成功"}

    def update_password(self, current_password: str, new_password: str, fa: str | None = None) -> bool:
        tab = self._page.new_tab(self._home_url)
        if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=10):
            logger.error(f"【{self.id}】 修改密码失败，请检查是否登录或代理是否可用")
            return False
        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:
            try:
                get_element(tab, "x://button[@aria-label='User Settings']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改密码出错，未找到 Setting 按钮，retry...")

            try:
                get_element(tab, "x://span[text()='Change Password']/ancestor::button", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改密码出错，未找到 Change Password 按钮，retry...")

            try:
                inputs = get_elements(tab, "x://input[@type='password']", 3)
                inputs[0].input(current_password)
                sleep(1)
                inputs[1].input(new_password)
                sleep(1)
                inputs[2].input(new_password)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 修改密码出错，未找到密码输入框，retry...")

            try:
                get_element(tab, "x://span[text()='Done']/ancestor::button", timeout=3).click()
                tab.wait.ele_deleted("x://h2[text()='Current Password']", timeout=5)

                if not tab.wait.ele_displayed("x://input[@autocomplete='one-time-code']", timeout=5) and not fa:
                    # 未开启2fa时，修改密码这里结束
                    logger.success(f"【{self.id}】 密码修改成功（token已变更）, new_password={new_password}")
                    return True

            except Exception:
                logger.warning(f"【{self.id}】 修改密码出错，未找到确认按钮，retry...")

            try:
                if not fa:
                    logger.error(f"【{self.id}】 密码修改失败，2FA为空")
                    return False
                code = pyotp.TOTP(fa)
                get_element(tab, "x://input[@autocomplete='one-time-code']", timeout=3).input(code.now())
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 修改密码出错，未找到验证码输入框，retry...")

            try:
                get_element(tab, "x://span[text()='Confirm']/ancestor::button", timeout=3).click()
                if tab.wait.ele_deleted("x://div[@aria-label='Multi-Factor Authentication']", 10):
                    logger.success(f"【{self.id}】 密码修改成功（token已变更）, new_password={new_password}")
                    return True
            except Exception:
                logger.warning(f"【{self.id}】 修改密码出错，未找到确认按钮，retry...")

            logger.warning(f"【{self.id}】 第 {retry_count} 次修改密码失败，retry...")
            retry_count += 1

            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

        return False

    def update_recovery_mail(self, password, recovery_mail, recovery_mail_pwd, recovery_proxy_mail: str | None = ""):
        pass

    def __get_discord_email_verification_code_with_imap4(self, email: str, email_password: str):
        email_client = EmailClient(email, email_password)
        search_criteria = SearchCriteria(
            subject="Your Discord email verification code is ",
            from_addr="Discord",
            to=email,
            # is_read=False,
        )

        try:
            emails = email_client.search_emails_with_retry(search_criteria)
        except Exception as e:
            logger.error(f"【{self.id}】 修改邮箱出错, {email} 搜索邮件失败: {e}")
            emails = []

        if not emails:
            latest_email = email_client.get_latest_email()
            if not latest_email:
                logger.error(f"【{self.id}】 未找到验证邮件")
                return None

            emails.append(latest_email)

        email_info = emails[0]
        subject = email_info.get("subject")
        pattern = r"Your Discord email verification code is ([A-Za-z0-9]{6})"
        match = re.search(pattern, subject)
        if match:
            return match.group(1)
        return None

    def __get_discord_email_verification_code_with_web(self, email: str, email_password: str, tab):
        mix_mail = MixMailWebClient(self._page, self._id)
        if not mix_mail.login(email, email_password):
            logger.error(f"【{self.id}】 修改邮箱出错, {email} 登录失败")
            return None
        code = mix_mail.get_discord_email_verification_code()
        if not code:
            get_element(tab, "x://a[contains(text(), 'Resend it')]", 3).click()
            code = mix_mail.get_discord_email_verification_code()

        return code

    def _get_discord_email_verification_code(self, email: str, email_password: str, tab):
        is_supported = EmailClient.is_supported(email)
        if is_supported:
            return self.__get_discord_email_verification_code_with_imap4(email, email_password)

        return self.__get_discord_email_verification_code_with_web(email, email_password, tab)

    def update_mail(
        self,
        password,
        current_email,
        current_email_pwd,
        email,
        email_password,
        proxy_email: str | None = None,
    ):
        tab = self._page.new_tab(self._home_url)
        if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=10):
            logger.error(f"【{self.id}】 修改邮箱失败，请检查是否登录或代理是否可用")
            return False
        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:
            try:
                get_element(tab, "x://button[@aria-label='User Settings']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到 Setting 按钮，retry...")

            try:
                get_element(tab, "x://button[@aria-label='Edit email address']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到 Edit 按钮，retry...")

            try:
                get_element(tab, "x://span[text()='Send Verification Code']/ancestor::button", 3).click()
                tab.wait.ele_displayed("x://h2[text()='Verification Code']", timeout=10)
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到发送验证码按钮，retry...")

            # 判断邮箱是否可以用imap4
            code = self._get_discord_email_verification_code(current_email, current_email_pwd, tab)

            if not code:
                logger.error(f"【{self.id}】 修改邮箱出错, 邮箱验证码获取失败")
                return False

            try:
                code_input = get_element(
                    tab,
                    "x://h2[text()='Verification Code']/following-sibling::div[1]//input",
                    timeout=3,
                )
                code_input.clear(True)
                code_input.input(code)
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到验证码输入框，retry...")

            try:
                get_element(tab, "x://span[text()='Next']/ancestor::button", 3).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到Next按钮，retry...")

            try:
                get_element(
                    tab,
                    "x://div[text()='I recently created a new email']/ancestor::div[2]",
                    3,
                ).click()
                sleep(1)
                get_element(tab, "x://span[text()='Continue']/ancestor::button", 3).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到Continue按钮，retry...")

            try:
                email_input = get_element(tab, "x://h2[text()='Email']/following-sibling::div[1]/input", 3)
                email_input.clear(True)
                recovery_email = proxy_email or email

                email_input.clear(True)
                email_input.input(recovery_email)
                sleep(1)

                pwd_input = get_element(
                    tab,
                    "x://h2[text()='Current Password']/following-sibling::div[1]/input",
                    3,
                )
                pwd_input.clear(True)
                pwd_input.input(password)
                sleep(1)

                pwd_input.clear(True)
                pwd_input.input(password)
                sleep(1)

                get_element(tab, "x://span[text()='Done']/ancestor::button", 3).click()
                tab.wait.ele_displayed("x://h1[text()='Confirm your new email']", timeout=10)

                get_element(tab, "x://span[text()='Okay']/ancestor::button", 3).click()
            except Exception:
                logger.warning(f"【{self.id}】 修改邮箱出错，未找到邮箱或密码输入框，retry...")

            verify_link = self._get_verify_link_from_email(email, email_password, proxy_email or email)
            if not verify_link:
                logger.error(f"【{self.id}】修改邮箱失败，未从 {proxy_email or email} 中获取到验证链接")
                return False

            last_tab = self._page.new_tab(verify_link)
            sleep(2)
            if get_element(last_tab, "x://div[@aria-label='CAPTCHA']", timeout=3):
                logger.warning(f"【{self.id}】 请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")
                last_tab.wait.ele_deleted("x://div[@aria-label='CAPTCHA']", 600)

            if get_element(last_tab, "x://h1[text()='Email Verified!']", 5):
                logger.success(f"【{self.id}】邮箱修改成功")
                return True
            last_tab.close()

            logger.warning(f"【{self.id}】 第 {retry_count} 次修改邮箱失败，retry...")
            retry_count += 1

            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

        return False

    def update_nickname(self, nickname: str) -> bool:
        if not nickname:
            logger.error(f"【{self.id}】 修改昵称失败，传入nickname为空")
            return False
        tab = self._page.new_tab(self._home_url)
        if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=10):
            logger.error(f"【{self.id}】 修改昵称失败，请检查是否登录或代理是否可用")
            return False
        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:
            try:
                get_element(tab, "x://button[@aria-label='User Settings']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改昵称出错，未找到 Setting 按钮，retry...")

            try:
                get_element(tab, "x://button[@aria-label='Edit display name']", 3).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 修改昵称出错，未找到修改按钮，retry...")

            try:
                nickname_i = get_element(tab, "x://h3[text()='Display Name']/parent::div//input", 3)
                nickname_i.set.value("")
                nickname_i.input(nickname)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】 修改昵称出错，未找到输入框，retry...")

            try:
                get_element(tab, "x://div[text()='Save Changes']/parent::button", timeout=3).click()
                if tab.wait.ele_deleted("x://div[contains(@class,'noticeRegion']", timeout=10):
                    logger.success(f"【{self.id}】 修改昵称成功")
                    return True
            except Exception:
                logger.warning(f"【{self.id}】 修改昵称出错，未找到保存按钮，retry...")

            logger.warning(f"【{self.id}】 第 {retry_count} 次修改昵失败，retry...")
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass
        return False

    def update_username(self, name: str, password: str):
        if not name:
            logger.error(f"【{self.id}】 修改用户名失败，传入username为空")
            return False
        tab = self._page.new_tab(self._home_url)
        if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=10):
            logger.error(f"【{self.id}】 修改用户名失败，请检查是否登录或代理是否可用")
            return False
        max_retries = 3
        retry_count = 1
        username = None
        while retry_count <= max_retries:
            try:
                get_element(tab, "x://button[@aria-label='User Settings']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改用户名出错，未找到 Setting 按钮，retry...")

            try:
                get_element(tab, "x://button[@aria-label='Edit username']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 修改用户名出错，未找到修改按钮，retry...")

            try:
                username_i = get_element(tab, "x://input[@aria-label='Username']", 3)
                username = self._set_username(name, username_i, tab)
            except Exception:
                logger.warning(f"【{self.id}】 修改用户名出错，未找到输入框，retry...")

            try:
                password_i = get_element(tab, "x://input[@type='password']", 3)
                password_i.clear(True)
                password_i.input("")
                password_i.input(password)
            except Exception:
                logger.warning(f"【{self.id}】 修改用户名出错，未找到密码输入框，retry...")

            try:
                get_element(tab, "x://button[contains(., 'Done')]", 3).click()
                if get_element(tab, "x://div[@aria-label='CAPTCHA']", timeout=3):
                    logger.warning(f"【{self.id}】 请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")
                    tab.wait.ele_deleted("x://div[@aria-label='CAPTCHA']", 600)

                if tab.wait.ele_deleted(
                    "x://div[@role='dialog' and contains(@class, 'focusLock')]",
                    timeout=5,
                ):
                    logger.success(f"【{self.id}】用户名修改成功, username={username}")
                    return True, username
                else:
                    logger.warning(f"【{self.id}】 修改用户名失败，请检查密码是否错误，retry...")
            except Exception:
                logger.warning(f"【{self.id}】 修改用户名出错，未找到确认按钮，retry...")

            logger.warning(f"【{self.id}】 第 {retry_count} 次修改用户名失败，retry...")
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass
        return False, None

    def reset_password(self, username: str, email: str, email_password: str, proxy_email: str, fa: str):
        max_retries = 3
        retry_count = 1
        while retry_count <= max_retries:
            logger.info(f"【{self.id}】第 {retry_count} 次尝试重置密码")

            try:
                tab = self._page.new_tab(self._login_url)

                # 点击登录按钮
                login_btn = get_element(tab, "x://button[contains(@class, 'lookFilled') and @type='button']", 3)
                if login_btn:
                    login_btn.click()
                    sleep(2)

                # 输入用户名
                get_element(tab, "x://input[@name='email']", 5).input(username)
                sleep(0.5)

                # 点击提交
                tab.listen.start("https://discord.com/api/v9/auth/forgot")

                # 点击重置密码按钮
                get_element(tab, "x://div[contains(@class, 'block')]/button[1]", 5).click()

                # 获取响应
                res = tab.listen.wait(timeout=30)
                if not res or not res.response:
                    logger.error(f"【{self.id}】重置密码失败: 未获取到响应")
                    return {"success": False, "code": "FAILED", "message": "未获取到响应"}

                if res.response.status > 400:
                    logger.error(f"【{self.id}】重置密码失败: 响应状态码为 {res.response.status}")
                    return {"success": False, "code": "FAILED", "message": "响应状态码为 {res.response.status}"}

                response = res.response.body
                # 检查是否需要验证码
                if self._check_error_code(response, "captcha-required"):
                    logger.warning(f"【{self.id}】 请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")
                    if not tab.wait.ele_deleted("x://div[@aria-label='CAPTCHA']", 1200):
                        return {
                            "success": False,
                            "code": "CAPTCHA_REQUIRED",
                            "message": "请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行",
                        }

                # 检查是否已经发送邮件
                ele = tab.ele(f"x://div[contains(@class, 'focusLock')]//strong[.='{username}']", timeout=5)
                if ele:
                    result = self._handle_reset_password(
                        email, email_password, proxy_email, fa, subject="Password Reset Request for Discord"
                    )

                    success = result.get("success", False)
                    if success:
                        return {
                            "success": True,
                            "code": "SUCCESS",
                            "password": result.get("password", ""),
                            "message": "重置密码成功",
                        }
                    else:
                        logger.error(f"【{self.id}】重置密码失败: {result.get('message', '')}")
                        return {"success": False, "code": "FAILED", "message": "重置密码失败"}

            except Exception as e:
                logger.error(f"【{self.id}】重置密码失败: {str(e)}")
                return {"success": False, "code": "FAILED", "message": "重置密码失败"}

        return {"success": False, "code": "FAILED", "message": "重置密码失败"}

    @staticmethod
    def _set_username(name, username_input, tab):
        unique_name = generate_login_name(name).lower()
        username_input.clear(True)
        username_input.set.value("")
        username_input.input(unique_name)
        sleep(2)
        final_username = unique_name

        while get_element(
            tab,
            "x://form//div[@data-text-variant='text-sm/normal' and contains(@style, 'text-danger')]",
            2,
        ):
            year = random.randint(1980, 2005)
            final_username = f"{unique_name}{year}"
            username_input.clear(True)
            username_input.set.value("")
            username_input.input(final_username)
            sleep(2)

        return final_username

    def setting_language(self, language: str = ""):
        tab = self._page.new_tab(self._home_url)
        sleep(2)

        max_retries = 3
        retry_count = 1
        while retry_count <= max_retries:
            try:
                get_element(
                    tab,
                    'x://*[@id="app-mount"]/div[2]/div[1]/div[1]/div/div[2]/div/div/div/div/div/section/div[2]/div[2]/button[2]',
                    3,
                ).click()
                sleep(1)
            except Exception:
                pass

            try:
                get_element(
                    tab,
                    'x://*[@id="app-mount"]/div[2]/div[1]/div[1]/div/div[2]/div[2]/div/div[1]/div/nav/div/div[27]',
                    timeout=3,
                ).click()
                sleep(1)
            except Exception:
                pass

            try:
                get_element(
                    tab,
                    'x://*[@id="language-tab"]/div/div[2]/div/div/div/div/div[4]',
                    timeout=3,
                ).click()
                sleep(1)
            except Exception:
                pass

            try:
                if tab("My Account"):
                    logger.success(f"【{self.id}】 设置Discord语言成功")
                    return True
            except Exception:
                pass

            logger.warning(f"【{self.id}】 第 {retry_count} 次切换语言失败，retry...")
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass

        return False

    def remove_other_device(self):
        tab = self._page.new_tab(self._home_url)
        if not tab.wait.ele_displayed("x://button[@aria-label='User Settings']", timeout=10):
            logger.error(f"【{self.id}】 踢出其他设备失败，请检查是否登录或代理是否可用")
            return False
        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:
            try:
                get_element(tab, "x://button[@aria-label='User Settings']", 3).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 踢出其他设备出错，未找到 Setting 按钮，retry...")

            try:
                get_element(tab, "x://button[@aria-controls='sessions-tab']", 3).click()
            except Exception:
                logger.warning(f"【{self.id}】 踢出其他设备出错，未找到 Device 按钮，retry...")

            try:
                devices = get_elements(tab, "x://div[contains(@class, 'essionMoreButton')]", 3)
                if not devices or len(devices) == 0:
                    logger.warning(f"【{self.id}】 未发现其他登录设备")
                    return True
                logger.info(f"【{self.id}】 共找到 {len(devices)} 个其他登录设备")
                for device in devices:
                    device.child().click()
                    sleep(1)
                logger.success(f"【{self.id}】 共踢出 {len(devices)} 个设备")
                return True
            except Exception:
                logger.warning(f"【{self.id}】 踢出其他设备出错，查找其他设备，retry...")

            logger.warning(f"【{self.id}】 第 {retry_count} 踢出其他设备失败，retry...")
            retry_count += 1
            try:
                tab.refresh()
                tab.reconnect(3)
            except:
                pass
        return False

    def _get_verify_link_from_email(
        self,
        email,
        email_pwd,
        proxy_email,
        subject="Verify Email Address for Discord",
        pattern=r"Verify Email: (https://click\.discord\.com/ls/click\?[^\s]+)",
    ):
        try:
            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject=subject,
                to=proxy_email,
                # is_read=False,
            )
            try:
                emails = email_client.search_emails_with_retry(search_criteria, 15)
            except Exception as e:
                logger.error(f"【{self.id}】 获取验证码邮件出错, {email} 搜索邮件失败: {e}")
                emails = []

            if len(emails) == 0:
                latest_email = email_client.get_latest_email()
                if not latest_email:
                    logger.error(f"{self.id} 未找到验证码邮件")
                    return None

                emails.append(latest_email)

            email = emails[0]
            content = email["content"]
            # pattern = r"Verify Email: (https://click\.discord\.com/ls/click\?[^\s]+)"
            match = re.search(pattern, content)
            return match.group(1) if match else None
        except Exception as e:
            logger.error(f"{self.id} 获取验证码失败: {e}")
            return None

    def get_user_id(self, token: str) -> str:
        """从Discord token中获取用户ID.

        Returns
        -------
            str: 用户ID，获取失败返回空字符串
        """
        try:
            if not token:
                logger.error(f"{self.id} Discord token 为空")
                return ""

            # 解析token获取user id
            base64_url = token.split(".")[0]
            base64 = base64_url.replace("-", "+").replace("_", "/")

            # 添加padding
            padding = len(base64) % 4
            if padding:
                base64 += "=" * (4 - padding)

            try:
                from base64 import b64decode

                # 解码base64并转换为字符串
                decoded = b64decode(base64)
                user_id = decoded.decode("utf-8")
                logger.info(f"{self.id} 解析token成功: {user_id}")
                return user_id

            except Exception as e:
                logger.error(f"{self.id} 解析token失败: {e}")
                return ""

        except Exception as e:
            logger.error(f"{self.id} 获取Discord用户ID失败: {e}")
            return ""

    def chat(self, channel_id):
        pass

    def check_join_server(self, server_id: str):
        """
        检查是否已经加入群组.
        """
        url = f"https://discord.com/channels/{server_id}"
        last_tab = None
        try:
            last_tab = self._page.new_tab(url)
            # last_tab.listen.start(f"https://discord.com/api/v9/guilds/{serverId}/profile")
            # last_tab.get(url)
            sleep(5)

            if "login" in last_tab.url:
                logger.warning(f"【{self.id}】 加入 {self.dc_name} 失败，用户未登录")
                if last_tab:
                    last_tab.close()
                return {"code": -1, "message": "用户未登录"}

            if last_tab.ele("Choose an account", timeout=3):
                logger.warning(f"【{self.id}】 加入 {self.dc_name} 失败，用户未登录")
                if last_tab:
                    last_tab.close()
                return {"code": -1, "message": "用户未登录"}

            if last_tab.ele("No Text Channels", timeout=3):
                logger.info(f"【{self.id}】 未加入 {self.dc_name}")
                if last_tab:
                    last_tab.close()
                return {"code": 0, "message": "未加入dc群"}

            return {"code": 1, "message": "已加入dc群"}

            # res = last_tab.listen.wait(timeout=30)
            # if not res:
            #     logger.error(f"【{self.id}】 加入 {self.dc_name} 失败，未找到服务器名称")
            #     return False

            # response_data = res.response.body
            # if response_data.get("code") == 50001:
            #     logger.error(f"【{self.id}】 加入 {self.dc_name} 失败，{response_data.get('message')}")
            #     return False

            # name = response_data.get("name")
            # if name:
            #     logger.success(f"【{self.id}】 已经加入 {name} 服务器")
            #     return True

            # logger.error(f"【{self.id}】 加入 {self.dc_name} 失败，未找到服务器名称")
            # return False
        except Exception as e:
            logger.error(f"【{self.id}】 加入 {self.dc_name} 失败，{e}")
            if last_tab:
                last_tab.close()
            return {"code": -1, "message": "加入失败"}

    def join_server(self, need_wait_captcha: bool = True, server_id: str = None, again_flag: bool = False) -> dict:
        """
        加入Discord服务器的模板方法，定义了加群的基本流程.

        Args:
            need_wait_captcha: 是否等待验证码
            server_id: 服务器ID

        Returns
        -------
            bool: 是否成功加入服务器
        """
        # 1. 打开邀请链接并点击加入
        if again_flag:
            result = self._accept_invite_two()
        else:
            result = self._accept_invite(need_wait_captcha, server_id)
        is_success = result.get("success")
        if not is_success:
            return result

        # 2. 处理验证前置条件
        if not self._pre_verify():
            return {"success": False, "message": "处理验证前置条件失败", "code": "PRE_VERIFY_FAILED"}

        # 3. 处理验证
        if not self._verify():
            return {"success": False, "message": "处理验证失败", "code": "VERIFY_FAILED"}

        # 4. 获取角色
        if not self._get_roles():
            return {"success": False, "message": "获取角色失败", "code": "GET_ROLES_FAILED"}

        # 5. 发送GM
        if not self._gm():
            return {"success": False, "message": "发送GM失败", "code": "SEND_GM_FAILED"}

        logger.success(f"【{self.id}】加入 {self.dc_name} 服务器成功")
        return {"success": True, "message": "加入成功", "code": "SUCCESS"}

    def _accept_invite_two(self) -> bool:
        """基础的接受邀请流程."""

        tab = self._page.get_tab(url=("https://discord.com/invite/" + self._invite_code))
        self._page.activate_tab(tab)
        # 检查是否登录
        if get_element(tab, "x://div[text()='Already have an account?']", 5):
            return {"success": False, "message": "账号未登录", "code": "NO_LOGIN"}

        # 点击加群按钮
        join = get_element(tab, "x://button[@type='button']", 5)
        if not join:
            return {"success": False, "message": "未找到确认按钮", "code": "NO_FOUND_ELEMENT"}

        tab.listen.start("https://discord.com/api/v9/invites/" + self._invite_code)
        join.click()
        try_click(tab, "x://div[contains(text(), 'Accept Invite')]")
        ele = get_element(tab, "x://div[text()='Continue to Discord']/..", timeout=6)
        if ele:
            ele.click()
            sleep(2)

        res = tab.listen.wait(timeout=30)
        if not res:
            return {"success": False, "message": "未找到服务器名称", "code": "NO_FOUND_SERVER_NAME"}

        response_data = res.response.body
        captcha_service = response_data.get("captcha_service", {})
        if captcha_service:
            logger.warning(f"【{self.id}】加入 {self.dc_name} 失败，需要处理 {captcha_service} 验证码 ")

            # 如果需要等待验证码，则等待验证码手工处理完成
            # if True:
            #     logger.warning(f"【{self.id}】请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")
            #     captcha_token = self._get_captcha_token(tab)
            #     if not captcha_token:
            #         raise Exception(f"【{self.id}】 获取验证码失败")
            #     if tab.wait.url_change(text="onboarding", timeout=200):
            #         return {"success": True, "message": "加入成功", "code": "SUCCESS"}

            return {"success": False, "message": "需要处理验证码", "code": "NEED_CAPTCHA"}

        # 获取服务器信息
        code = response_data.get("code", "")
        message = response_data.get("message", "")
        if not code:
            return {"success": False, "message": "未找到服务器名称", "code": "NO_FOUND_SERVER_NAME"}

        if str(code).lower() == str(self._invite_code).lower():
            return {"success": True, "message": "加入成功", "code": "SUCCESS"}
        else:
            logger.error(f"【{self.id}】加入 {self.dc_name} 失败，{message}")
            return {"success": False, "message": message, "code": "JOIN_FAILED"}

    def _handle_option_selection(self, tab, select_count=0):
        """处理选项选择.

        Args:
            tab: 浏览器标签页
            select_count: 需要选择的选项数量，0表示全选
        """
        try:
            eles = get_elements(tab, "x://div[contains(@class, 'optionButtonWrapper')]", 5)
            if not eles:
                return

            if select_count == 0:
                # 全选
                for ele in eles:
                    ele.click()
                    sleep(0.5)
            else:
                # 随机选择指定数量
                selected = random.sample(eles, min(select_count, len(eles)))
                for ele in selected:
                    ele.click()

            try_click(tab, "x://button[contains(.,'Finish') or contains(.,'Next')]")
            sleep(2)
        except Exception as e:
            logger.error(f"{self.id} 处理选项选择时发生错误: {str(e)}")

    def _accept_invite(self, need_wait_captcha, serverId) -> dict:
        """基础的接受邀请流程."""
        if serverId:
            res = self.check_join_server(serverId)
            if res["code"] == 1:
                return {"success": True, "message": "已加入dc群"}
            elif res["code"] == -1:
                return {"success": False, "message": res["message"], "code": "NO_LOGIN"}

        if not self._invite_code:
            return {"success": False, "message": "invite_code 为空", "code": "NO_INVITE_CODE"}

        tab = self._page.new_tab("https://discord.com/invite/" + self._invite_code)

        # 检查是否登录
        if get_element(tab, "x://div[text()='Already have an account?']", 5):
            return {"success": False, "message": "账号未登录", "code": "NO_LOGIN"}

        # 点击加群按钮
        join = get_element(tab, "x://button[@type='button']", 5)
        if not join:
            return {"success": False, "message": "未找到确认按钮", "code": "NO_FOUND_ELEMENT"}

        tab.listen.start("https://discord.com/api/v9/invites/" + self._invite_code)
        join.click()

        ele = get_element(tab, "x://div[text()='Continue to Discord']/..", timeout=6)
        if ele:
            ele.click()
            sleep(2)

        res = tab.listen.wait(timeout=30)
        if not res:
            return {"success": False, "message": "未找到服务器名称", "code": "NO_FOUND_SERVER_NAME"}

        response_data = res.response.body
        captcha_service = response_data.get("captcha_service", {})
        if captcha_service:
            logger.warning(f"【{self.id}】加入 {self.dc_name} 失败，需要处理 {captcha_service} 验证码 ")

            # 如果需要等待验证码，则等待验证码手工处理完成
            if need_wait_captcha:
                logger.warning(f"【{self.id}】请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")
                if tab.wait.url_change(text="onboarding", timeout=600):
                    return {"success": True, "message": "加入成功", "code": "SUCCESS"}

            return {"success": False, "message": "需要处理验证码", "code": "NEED_CAPTCHA"}

        # 获取服务器信息
        code = response_data.get("code", "")
        message = response_data.get("message", "")
        if not code:
            return {"success": False, "message": "未找到服务器名称", "code": "NO_FOUND_SERVER_NAME"}

        if str(code).lower() == str(self._invite_code).lower():
            return {"success": True, "message": "加入成功", "code": "SUCCESS"}
        else:
            logger.error(f"【{self.id}】加入 {self.dc_name} 失败，{message}")
            code = "BANNED" if "banned" in message else "JOIN_FAILED"
            return {"success": False, "message": message, "code": code}

    def _pre_verify(self) -> bool:
        """
        验证前的准备工作，需要在子类中实现.

        比如：等待特定按钮出现、切换到特定频道等
        """
        return True

    def _verify(self) -> bool:
        """
        具体的验证流程，需要在子类中实现.

        比如：点击验证按钮、处理验证码等
        """
        return True

    def _get_roles(self) -> bool:
        """
        获取角色的流程，需要在子类中实现.

        比如：点击特定按钮获取角色等
        """
        return True

    def _gm(self) -> bool:
        """
        发送欢迎消息的流程，需要在子类中实现.

        比如：在特定频道发送GM等
        """
        return True
