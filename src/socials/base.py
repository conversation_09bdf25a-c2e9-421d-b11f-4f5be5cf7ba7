from abc import ABC, abstractmethod

from DrissionPage import Chromium


class BaseSocial(ABC):
    def __init__(self, id: str, page: Chromium):
        self._id = id
        self._page = page

    @property
    def id(self):
        """添加 id 属性访问器"""
        return self._id

    @abstractmethod
    def login_by_password(self, username, password):
        raise NotImplementedError("子类必须实现login方法")

    @abstractmethod
    def login_by_token(self, token):
        raise NotImplementedError("子类必须实现login方法")

    @abstractmethod
    def update_password(self, current_password, new_password):
        raise NotImplementedError("子类必须实现update_password方法")

    @abstractmethod
    def update_recovery_mail(
        self,
        password: str,
        recovery_mail: str,
        recovery_mail_pwd: str,
        recovery_proxy_mail="",
    ):
        raise NotImplementedError("子类必须实现update_recovery_mail方法")
