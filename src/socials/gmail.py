import random
import re
import sys
from time import sleep, time

import pyotp
from loguru import logger

from src.emails.imap4.email_client import EmailClient
from src.emails.imap4.imap_client import SearchCriteria
from src.socials.base import BaseSocial
from src.utils.element_util import get_element, get_elements, get_frame


class Gmail(BaseSocial):
    def __init__(self, id, page) -> None:
        super().__init__(id, page)
        self._home_url = "https://www.google.com"
        self._security_url = "https://myaccount.google.com/security"
        self._imap4_url = "https://myaccount.google.com/apppasswords"

    def change_language(self, language="english"):

        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:

            tab = self._page.new_tab("https://www.google.com/")

            ele = get_element(tab, "x://a[.='English']", 6)
            if not ele:
                logger.success(f"【{self.id}】 页面加载异常或者已经是{language}语言")
                return True

            try:
                ele.click()
                sleep(6)
            except Exception:
                logger.warning(f"【{self.id}】 点击设置语言失败")
                pass

            # 检查是否切换成功
            try:
                ele = get_element(tab, "x://a[.='English']", 6)
                if not ele:
                    logger.success(f"【{self.id}】 页面加载异常或者已经是{language}语言")
                    return True

                logger.error(f"【{self.id}】 切换语言失败")
                continue
            except Exception:
                logger.warning(f"【{self.id}】 未找到切换语言成功")
                pass

            retry_count += 1

            tab.close()
        return False

    def login_by_password(
        self,
        email: str,
        email_password: str,
        recovery_email: str | None = None,
        recovery_email_pwd: str | None = None,
        recovery_proxy_email: str | None = None,
        two_fa: str | None = None,
    ) -> bool:
        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:
            tab = self._page.new_tab(self._home_url)
            logger.debug(f"【{self.id}】等待页面加载...")
            wait_ele = tab.wait.ele_displayed('x://*[@id="gb"]/div[3]/a', timeout=3) or tab.wait.ele_displayed(
                'x://*[@id="gb"]/div/div[2]/div[2]/div/a', timeout=3
            )

            # todo 处理弹框
            try:
                get_element(tab, "x://button[@id='L2AGLb']", timeout=3).click()
                logger.debug(f"【{self.id}】正在点击接受协议按钮")
                sleep(2)
            except Exception:
                pass

            try:
                # is_login = (
                #     get_element(tab, 'x://*[@id="gb"]/div/div[1]/div[2]/div/a', 3)
                #     or
                #     get_element(tab, 'x://*[@id="gb"]/div/div[2]/div[2]/div/a', 3)
                # )
                login_btn = get_element(tab, 'x://*[@id="gb"]/div[3]/a', 3) or get_element(
                    tab, 'x://*[@id="gb"]/div[4]/a', 3
                )
                if login_btn and "@gmail.com" in login_btn.attr("aria-label"):
                    logger.success(f"【{self.id}】gmail已登录")
                    return True
            except Exception:
                pass

            # 点击登录按钮
            try:
                # get_element(tab, 'x://*[@id="gb"]/div/div[2]/a', 5).click.multi(3)
                # //*[@id="gb"]/div[3]/a
                login_btn = get_element(tab, 'x://*[@id="gb"]/div[3]/a', 5) or get_element(
                    tab, 'x://*[@id="gb"]/div[4]/a', 3
                )
                login_btn.click.multi(3)
                tab.wait.ele_displayed("x://input[@id='identifierId']", timeout=5)
                sleep(2)
                logger.debug(f"【{self.id}】正在点击登录按钮")
            except Exception:
                if get_element(tab, 'x://*[@id="gb"]/div/div[2]/div[2]/div/a', 3):
                    logger.success(f"【{self.id}】已经登录")
                    return True
                logger.warning(f"【{self.id}】登录失败，未找到登录按钮,retry...")

            # 移除过期账号
            try:
                logger.debug(f"【{self.id}】检查是否存在缓存账号")
                eles = get_elements(tab, "x://div[contains(@data-identifier, 'gmail')]", 5)
                count = len(eles)
                if count >= 1:
                    logger.debug(f"【{self.id}】存在缓存账号, 准备清理")
                    for _ in range(count):
                        remove_btn = get_element(
                            tab,
                            "x://*[@id='yDmH0d']/c-wiz/div/div[2]/div/div/div/form/span/section/div/div/div/div/ul/li[4]/div",
                        ) or get_element(
                            tab,
                            "x://*[@id='yDmH0d']/c-wiz/div/div[2]/div/div/div/form/span/section/div/div/div/div/ul/li[3]/div",
                        )

                        remove_btn.click()

                        ele = get_element(tab, "x://div[contains(@data-identifier, '@gmail.com')]")
                        ele.click()
                        sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】清理缓存账号失败")

            # 输入账号
            try:
                email_i = get_element(tab, "x://input[@type='email' and @id='identifierId']", 3)
                email_i.clear(True)
                email_i.input(email)
                logger.debug(f"【{self.id}】正在输入账号 {email}")
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 登录失败，未找到用户名输入框,retry...")

            # 下一步
            try:
                get_element(tab, "x://div[@id='identifierNext']/div/button", 3).click()
                logger.debug(f"【{self.id}】正在点击下一步")
                tab.wait.ele_displayed("x://*[@id='password']", timeout=5)
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 登录失败，未找到下一步按钮,retry...")

            # fixme 检查是否跳出验证码
            # if get_element(tab, "x://input[@id='ca']", 3):
            #     logger.warning(f"【{self.id}】 请手动处理验证码，验证码处理完请勿关闭浏览器，程序将继续执行")
            #     tab.wait.ele_displayed("x://input[@id='ca']",600)

            # 输入密码
            try:
                pwd_i = get_element(tab, "x://*[@id='password']/div[1]/div/div[1]/input", 3)
                pwd_i.clear(True)
                pwd_i.input(email_password)
                logger.debug(f"【{self.id}】正在输入密码")
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 登录失败，未找到密码输入框,retry...")

            # macOs会弹出保存密码
            if sys.platform.startswith("darwin"):
                try:
                    get_element(tab, "x://*[@id='yDmH0d']/c-wiz/div/div[3]/div/div[2]/div/div/button").click()
                    logger.debug(f"【{self.id}】跳过保存密码")
                    sleep(2)
                except Exception:
                    logger.warning(f"【{self.id}】未找到取消保存密码按钮, retry...")

            # 下一步
            try:
                get_element(tab, "x://*[@id='passwordNext']/div/button", 3).click()
                logger.debug(f"【{self.id}】正在点击下一步")
                tab.wait.ele_displayed("x://div[@data-challengeid='5']", timeout=5)
                sleep(3)
            except Exception:
                logger.warning(f"【{self.id}】登录失败，未找到下一步按钮,retry...")

            # try:
            #     logger.debug(f"【{self.id}】检查密码输入是否错误")
            #     if get_element(tab, "x://*[@id='password']/div[1]/div/div[1]/input", 3):
            #         logger.error(f"【{self.id}】 Gmail密码错误")
            #         return False
            # except Exception:
            #     pass

            # 检验2fa
            try:
                logger.debug(f"【{self.id}】检查是否需要2FA验证")
                totp_i = get_element(tab, "x://input[@id='totpPin']", 5)
                if totp_i:
                    if not two_fa:
                        logger.error(f"【{self.id}】 登录失败，需要2fa验证，但2fa key未配置")
                        return False

                    logger.debug(f"【{self.id}】正在输入2fa验证码")
                    sleep(1)

                    # if auth_ele := get_element(
                    #     tab, "x://strong[contains(text(), 'Google Authenticator')]", 3
                    # ):
                    #     if not two_fa:
                    #         logger.error(f"【{self.id}】 登录失败，需要2fa验证")
                    #         return False
                    #     logger.debug(f"【{self.id}】 正在输入2fa验证码")
                    #     sleep(1)
                    #
                    #     auth_ele.click()
                    #     sleep(1)
                    # 输入totp
                    # 如果有空格移除空格
                    two_fa = two_fa.replace(" ", "")
                    totp = pyotp.TOTP(two_fa)

                    # 输入totp
                    totp_i.input(totp.now())
                    sleep(1)

                    get_element(tab, "x://*[@id='totpNext']/div/button", 5).click(True)
                    logger.debug(f"【{self.id}】正在点击下一步")
                    sleep(5)

                    if "https://www.google.com" in tab.url:
                        logger.success(f"【{self.id}】登录成功")
                        return True

            except Exception:
                return False

            # 检查是否需要手机号验证
            logger.debug(f"【{self.id}】检查是否需要手机号验证")
            if get_element(tab, "x://input[@id='phoneNumberId']", 3):
                logger.error(f"【{self.id}】登录失败，需要手机号验证")
                return False

            # 跳过扫码验证
            try:
                logger.debug(f"【{self.id}】检查是否需要扫码验证")
                ele = get_element(tab, "x://*[@id='yDmH0d']/c-wiz/div/div[3]/div/div[2]/div/div/button")
                ele.click()
                sleep(2)
            except Exception as e:
                logger.warning(f"【{self.id}】跳过扫码验证失败， error={str(e)}")

            try:
                logger.debug(f"【{self.id}】检查是否需要辅助邮箱验证码")
                if get_element(tab, "x://input[@id='knowledgePreregisteredEmailInput']", 5):
                    logger.warning(f"【{self.id}】登录失败，需要辅助邮箱验证码")
                    return False
            except Exception as e:
                pass

            # 辅助邮箱
            try:
                # get_element(tab, "x://div[@data-challengeid='5']/parent::li", 3).click(True)
                get_element(tab, "x://div[@data-challengeid='5']", 3).click(True)
                logger.debug(f"【{self.id}】正在点击辅助邮箱验证")
                tab.wait.ele_displayed(
                    "x://input[@id='knowledge-preregistered-email-response']",
                    timeout=10,
                )
                sleep(0.5)
            except Exception:
                pass

            # 输入辅助邮箱
            try:
                logger.debug(f"【{self.id}】开始输入辅助邮箱")
                recovery = recovery_proxy_email or recovery_email
                e_i = get_element(tab, "x://input[@id='knowledge-preregistered-email-response']", 3)
                e_i.clear(True)
                e_i.input(recovery)
                sleep(1)
            except Exception as e:
                logger.warning(f"【{self.id}】辅助邮箱输入失败， error={str(e)}")

            # 下一步
            try:
                # 跳过指纹
                ele = get_element(tab, "With passkeys you can now use your fingerprint", 3)
                if ele:
                    get_element(tab, "Not now", 3).click()
                    logger.debug(f"【{self.id}】正在点击跳过指纹")
                    sleep(8)

                get_element(tab, "x://div[@data-is-consent='false']/div/div//button", 3).click()
                logger.debug(f"【{self.id}】正在点击下一步")
                sleep(3)

                try:
                    if get_element(tab, "x://input[@id='idvPinId']", 5):
                        logger.warning(f"【{self.id}】登录失败，需要辅助邮箱验证码")
                        return False
                except Exception:
                    pass

                if "https://www.google.com" in tab.url:
                    logger.success(f"【{self.id}】登录成功")
                    return True
            except Exception:
                pass

            # 绑定手机号提醒，可直接跳过
            try:
                get_element(
                    tab,
                    "x://input[@id='c13']/ancestor::*[8]/following-sibling::div/div//button",
                    3,
                ).click()
                logger.debug(f"【{self.id}】跳过绑定手机号提醒")
                sleep(3)

                self._handle_avatar(tab)
                self._handle_get_reminder(tab)

                done_btn = (
                    get_element(
                        tab,
                        "x://*[@id='ow16']/div/div/div/div/child::div[2]/child::div[2]/button",
                        3,
                    )
                    or get_element(
                        tab,
                        "x://*[@id='ow17']/div/div/div/div/child::div[2]/child::div[2]/button",
                        3,
                    )
                    or get_element(
                        tab,
                        "x://*[@data-item-type='49']/ancestor::div[5]/following-sibling::div[1]/child::div[2]//button",
                        3,
                    )
                )
                done_btn.click(True)
                logger.debug(f"【{self.id}】正在点击完成按钮")

                sleep(5)
                if "https://www.google.com" in tab.url:
                    logger.success(f"【{self.id}】登录成功")
                    return True
            except Exception:
                pass

            try:
                get_element(tab, "x://*[@id='yDmH0d']/c-wiz[2]/div/div/div/div/div[2]/button[2]").click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】点击保存按钮失败")

            # 设置家庭住址
            try:
                skip = get_element(tab, "x://*[@id='yDmH0d']/c-wiz[3]/div/div/div/div/div/div[2]/button[1]")
                skip.click()
                sleep(5)
                if "https://www.google.com" in tab.url:
                    logger.success(f"【{self.id}】登录成功")
                    return True
            except Exception:
                logger.warning(f"【{self.id}】跳过设置住址失败")

            logger.warning(f"【{self.id}】第 {retry_count} 次登录失败，retry...")
            retry_count += 1

            tab.close()

        return False

    def login_by_token(self, token: str) -> bool:
        pass

    def update_password(self, current_password: str, new_password: str, fa: str | None = None) -> bool:
        pass

    def update_recovery_mail(
        self, password: str, recovery_mail: str, recovery_mail_pwd: str, recovery_proxy_mail: str | None = ""
    ) -> bool:
        pass

    def update_pwd(
        self,
        password: str,
        new_password: str,
        current_recovery_email: str,
        fa: str | None = None,
    ) -> bool:

        max_retries = 3
        retry_count = 1

        while retry_count <= max_retries:
            tab = self._page.new_tab(self._security_url)
            tab.wait.ele_displayed("x://a[@data-rid='401']", timeout=10)

            try:
                get_element(tab, "x://a[@data-rid='401']", 3).click()
                tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
            except Exception:
                logger.warning(f"【{self.id}】修改密码失败，未找到修改密码按钮,retry...")

            try:
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                sleep(1)
                pwd_i.clear(True)
                pwd_i.input(password)
                logger.debug(f"【{self.id}】正在输入当前密码")
            except Exception:
                pass

            try:
                get_element(tab, "x://div[@id='passwordNext']//button", 3).click()
                logger.debug(f"【{self.id}】点击下一步")
                tab.wait.ele_displayed("x://input[@id='i6']", 5)
                sleep(1)
            except Exception:
                pass

            try:

                if get_element(tab, "x://input[@name='challengeListId']", 3):
                    logger.debug(f"【{self.id}】出现辅助邮箱验证")
                    self._handle_recovery_email(tab, current_recovery_email)

                logger.debug(f"【{self.id}】正在点击下一步")
                tab.wait.ele_displayed("x://input[@id='i5']", 5)
                sleep(1)
            except Exception:
                pass

            try:

                eles = get_elements(tab, "x://input[@type='password']", 3)

                if len(eles) < 2:
                    logger.error(f"【{self.id}】修改密码失败，未找到密码输入框")
                    return False

                eles[0].clear(True)
                eles[0].input(new_password)
                sleep(1)

                logger.debug(f"【{self.id}】正在输入新密码")

                eles[1].clear(True)
                eles[1].input(new_password)
                sleep(1)

                logger.debug(f"【{self.id}】正在确认新密码")
            except Exception:
                logger.warning(f"【{self.id}】修改密码失败，未找到密码输入框,retry...")

            try:
                get_element(tab, "x://button[@type='submit']", 3).click()
                logger.debug(f"【{self.id}】正在提交")
                logger.success(f"【{self.id}】密码修改成功， new_pwd={new_password}")
                sleep(3)
                return True
            except Exception:
                logger.warning(f"【{self.id}】 修改密码失败，未找到提交按钮,retry...")

            # todo 验证密码是否修改成功
            # content = self._get_email(username, new_password, "Security alert")
            # if not content:
            #     logger.warning(f"【{self.id}】 密码修改失败.retry...")
            #     pass
            # elif "Your password was changed" in content:
            #     logger.success(f"【{self.id}】备用邮箱修改成功")
            #     return True

            logger.warning(f"【{self.id}】 第 {retry_count} 次修改密码失败，retry...")
            retry_count += 1

            tab.close()

        return False

    def update_mail(
        self,
        email_password: str,
        current_recovery_email: str,
        new_recovery_email: str,
        new_recovery_email_pwd: str,
        new_recovery_proxy_email: str | None = None,
    ) -> bool:
        max_retries = 1
        retry_count = 1

        while retry_count <= max_retries:
            tab = self._page.new_tab(self._security_url)
            logger.debug(f"【{self.id}】等待元素加载...")
            tab.wait.ele_displayed("x://a[contains(@href, 'recovery/email')]", timeout=10)

            try:
                next_btn = get_element(tab, "x://span[text()='Next']/ancestor::button", 5)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Next按钮")
                    next_btn.click()
                    tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
                    sleep(1)
            except Exception:
                logger.success(f"【{self.id}】点击Next按钮出错")

            try:
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 5)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(email_password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错, retry...")

            try:
                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Mext")
                    next_btn.click()
                    tab.wait.ele_displayed("x://a[contains(@href, 'recovery/email')]", timeout=10)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错, retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Recovery email按钮")
                get_element(tab, "x://a[contains(@href, 'recovery/email')]", 3).click()
                tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Recovery email出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在输入密码")
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                if pwd_i:
                    pwd_i.clear(True)
                    pwd_i.input(email_password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错, retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Next")
                next_btn = get_element(tab, "x://span[text()='Next']/parent::button", 3)
                if next_btn:
                    next_btn.click()
                    sleep(3)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错, retry...")

            try:
                logger.debug(f"【{self.id}】检查是否需要辅助邮箱验证")
                if get_element(tab, "x://input[@name='challengeListId']", 5):
                    logger.debug(f"【{self.id}】出现辅助邮箱验证")
                    self._handle_recovery_email(tab, current_recovery_email)
                    sleep(1)
            except Exception:
                logger.debug(f"【{self.id}】辅助邮箱验证出错, retry...")

            recovery_email = new_recovery_proxy_email or new_recovery_email

            try:
                logger.debug(f"【{self.id}】检查是否存在辅助邮箱")
                edit_recovery_email_btn = get_element(tab, "x://button[@aria-label='Edit recovery email']", 5)
                if edit_recovery_email_btn:
                    logger.debug(f"【{self.id}】正在点击编辑备用邮箱按钮")
                    edit_recovery_email_btn.click()
                else:
                    add_recovery_email_btn = get_element(tab, "x://span[text()='Add recovery email']/parent::button", 5)
                    add_recovery_email_btn.click()
                tab.wait.ele_displayed("x://input[@aria-label='Your recovery email']", timeout=10)
                sleep(1)
            except Exception:
                logger.debug(f"【{self.id}】检查是否存在辅助邮箱出错, retry...")

            # todo 已经修改辅助邮箱，但是未验证

            try:
                email_i = get_element(tab, "x://input[@aria-label='Your recovery email']", 5)
                if email_i.value == recovery_email:
                    logger.debug(f"【{self.id}】辅助邮箱已经是{recovery_email}")
                    return True
                logger.debug(f"【{self.id}】正在输入辅助邮箱")
                email_i.clear(True)
                email_i.input(recovery_email)
                sleep(0.5)

                logger.debug(f"【{self.id}】正在点击Save")
                get_element(tab, "x://span[text()='Save']/parent::button", 5).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】输入辅助邮箱出错, retry...")

            code = None
            try:
                start_time = time()
                while time() - start_time < 30:
                    logger.debug("等待接收机邮件验证码...")
                    code = self._get_email_verify_code(
                        new_recovery_email, new_recovery_email_pwd, new_recovery_proxy_email
                    )
                    if code:
                        break
                    sleep(1)

                if not code:
                    logger.error(f"【{self.id}】修改备用邮箱失败，未接收到验证码")
                    continue
            except Exception:
                logger.warning(f"【{self.id}】接收邮件验证码出错, retry...")

            try:
                logger.debug(f"【{self.id}】正在输入邮件验证码: {code}")
                code_i = get_element(
                    tab, "x://h2[.//span[text()='Verify your recovery email']]/following-sibling::div//input", 5
                )
                code_i.clear(True)
                code_i.input(code)
                sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入验证码出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Verify按钮")
                get_element(tab, "x://span[text()='Verify']/parent::button", 5).click()
                if tab.wait.ele_displayed("x://div[contains(text(), 'Recovery email verified')]", timeout=10):
                    logger.success(f"【{self.id}】备用邮箱修改成功")
                    return True
            except Exception:
                logger.warning(f"【{self.id}】点击Verify出错,retry...")

            logger.warning(f"【{self.id}】 第 {retry_count} 次修改备用邮箱失败，retry...")
            retry_count += 1

            tab.close()
        return False

    def update_nickname(self, nickname: str) -> bool:
        pass

    def get_2fa_key(self, password: str, current_recovery_email: str):
        try:
            tab = self._page.new_tab(self._security_url)
            logger.debug(f"【{self.id}】等待Authenticator加载...")
            tab.wait.ele_displayed(
                "x://a[contains(@href, 'two-step-verification/authenticator')]",
                timeout=10,
            )

            try:
                logger.debug(f"【{self.id}】检查是否有Next按钮")
                next_btn = get_element(tab, "x://span[text()='Next']/ancestor::button", 5)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Next按钮")
                    next_btn.click()
                    tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
                    sleep(1)
            except Exception:
                logger.success(f"【{self.id}】点击Next按钮出错")

            try:
                logger.debug(f"【{self.id}】检查是否需要输入密码")
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 5)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错, retry...")

            try:
                logger.debug(f"【{self.id}】检查是否有Next按钮")
                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Mext")
                    next_btn.click()
                    sleep(5)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错, retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Authenticator按钮")
                get_element(
                    tab,
                    "x://span/a[contains(@href, 'two-step-verification/authenticator')]",
                    3,
                ).click()
                tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
                sleep(1)
            except Exception:
                logger.success(f"【{self.id}】已开启2FA")
                return True, None

            try:
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错, retry...")

            try:

                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击下一步")
                    next_btn.click()
                    tab.wait.ele_displayed("x://button[.='Set up authenticator']", timeout=10)
                    sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击下一步出错, retry...")

            try:
                logger.debug(f"【{self.id}】检查是否出现辅助邮箱")
                if get_element(tab, "x://input[@name='challengeListId']", 3):
                    logger.debug(f"【{self.id}】出现辅助邮箱验证")
                    self._handle_recovery_email(tab, current_recovery_email)

                tab.wait.ele_displayed("x://button[.='Set up authenticator']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】检查是否出现辅助邮箱出错, retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Set up authenticator")
                get_element(
                    tab,
                    "x://button[.='Set up authenticator']",
                    3,
                ).click()
                sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】 点击Set up authenticator出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Can’t scan it")
                get_element(tab, "x://div[@data-position='QqIugc']", 5).scroll.to_bottom()
                get_element(
                    tab,
                    "x://div[@data-position='QqIugc']/child::*[3]//center//button",
                    3,
                ).click()
                sleep(3)
            except Exception:
                logger.warning(f"【{self.id}】点击Can’t scan it出错,retry...")

            code = None
            fa_key = None
            try:
                logger.debug(f"【{self.id}】正在获取2FA Key")
                fa_key_div = get_element(
                    tab,
                    "x://div[@data-position='QqIugc']/child::*[3]//ol/li[2]//strong",
                    3,
                )
                fa_key = fa_key_div.text.strip().replace(" ", "")
                logger.success(f"【{self.id}】获取到2FA Key={fa_key}")
                code = pyotp.TOTP(fa_key).now()
            except Exception:
                logger.warning(f"【{self.id}】获取2FA Key时验证key错误,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Next按钮")
                get_element(
                    tab,
                    "x://div[@data-position='QqIugc']/child::*[4]//button[@data-id='OCpkoe']",
                    3,
                ).click()
                tab.wait.ele_displayed("x://input[@type='text']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Next按钮出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在输入code")
                code_i = get_element(tab, "x://input[@type='text']", 3)
                code_i.clear(True)
                code_i.input(code)
            except Exception:
                logger.warning(f"【{self.id}】输入code出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Verify")
                get_element(
                    tab,
                    "x://div[@data-position='QqIugc']/child::*[4]//button[@data-id='dtOep']",
                    3,
                ).click()
                if tab.wait.ele_displayed("x://div[text()='Authenticator app has been set up']", timeout=10):
                    logger.success(f"【{self.id}】验证2FA Key成功")
                    return True, fa_key
                else:
                    logger.error(f"【{self.id}】获取2FA Key失败，请手动处理")
                    return False, None
            except Exception:
                logger.warning(f"【{self.id}】点击Verify出错")

        except Exception as e:
            logger.error(f"【{self.id}】获取2FA Key发生异常，error={str(e)}")

        return False, None

    def enable_two_step_verification(self, password: str, phone: str):
        try:
            tab = self._page.new_tab(self._security_url)

            tab.wait.ele_displayed("x://div/a[contains(@href,'signinoptions/twosv')]", timeout=10)

            try:
                logger.debug(f"【{self.id}】检查是否已开启2FA,retry...")
                xpath = (
                    "x://img[@src='https://www.gstatic.com/identity/boq/accountsettingsmobile"
                    "/status_green_20x20_7cef6592217fb4dd703491c964211bef.png']"
                )
                if get_element(tab, xpath, 3):
                    logger.success(f"【{self.id}】已开启2FA")
                    return True

                logger.debug(f"【{self.id}】正在点击2-Step Verification,retry...")
                get_element(tab, "x://div/a[contains(@href,'signinoptions/twosv')]", 3).click()
                tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】开启2FA时未找到2-Step Verification按钮,retry...")

            try:
                logger.debug(f"【{self.id}】检查是否需要输入密码")
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错,retry...")

            try:
                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Next")
                    next_btn.click()
                    tab.wait.ele_displayed("x://span[text()='Turn on 2-Step Verification']/ancestor::button", timeout=5)
                    sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Turn on 2-Step Verification")
                get_element(
                    tab,
                    "x://span[text()='Turn on 2-Step Verification']/ancestor::button",
                    5,
                ).click()
                tab.wait.ele_displayed("x://span[text()='Add a phone number']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】开启2FA时未找到Turn on 2-Step Verification按钮")

            try:
                logger.debug(f"【{self.id}】正在点击选择国家码")
                get_element(
                    tab, "x://input[@aria-label='Phone input']/ancestor::div[3]/preceding-sibling::div", 3
                ).click()
                tab.wait.ele_displayed("x://div[@data-menu-uid='ucc-1']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】选择国家码出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击China (+86)")
                tab.scroll.to_see("x://span[text()='China (+86)']/parent::li")
                get_element(tab, "x://span[text()='China (+86)']/parent::li", 3).click()
                sleep(3)
            except Exception:
                logger.warning(f"【{self.id}】点击China (+86)出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在输入手机号")
                phone_i = get_element(tab, "x://input[@aria-label='Phone input']", 3)
                phone_i.clear(True)
                phone_i.input(phone)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】输入手机号出错")

            try:
                logger.debug(f"【{self.id}】正在点击Next")
                get_element(tab, "x://span[text()='Next']/parent::button", 3).click()
                tab.wait.ele_displayed("x://span[text()='Confirm your phone number']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错")

            try:
                logger.debug(f"【{self.id}】正在点击Save")
                get_element(tab, "x://span[text()='Save']/parent::button", 3).click()
                tab.wait.ele_displayed("x://span[text()='Confirm your phone number']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Save出错")

            if tab.wait.ele_displayed('x://span[text()="You’re now protected with 2-Step Verification"]', timeout=15):
                logger.success(f"【{self.id}】开启2FA成功")
                return True

        except Exception as e:
            logger.error(f"【{self.id}】开启2FA异常，error={str(e)}")
        return False

    def get_imap4_password(self, password: str, is_update: bool) -> str | None:
        imap4_password = None
        try:
            tab = self._page.new_tab(self._imap4_url)
            logger.debug(f"【{self.id}】等待元素加载")
            tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)

            try:
                logger.debug(f"【{self.id}】检查是否需要验证")
                next_btn = get_element(tab, "x://span[text()='Next']/parent::button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Next")
                    next_btn.click()
                    tab.wait.ele_displayed("x://input[@name='Passwd']", timeout=5)
                    sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错")

            try:
                logger.debug(f"【{self.id}】检查是否需要输入密码")
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(password)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错")

            try:
                logger.debug(f"【{self.id}】检查是否需要点击下一步")
                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击下一步")
                    next_btn.click()
                    tab.wait.ele_displayed("x://h1[text()='App passwords']", timeout=5)
            except Exception:
                logger.warning(f"【{self.id}】点击下一步出错")

            try:
                logger.debug(f"【{self.id}】检查是否已创建过IMAP4专用密码")
                if get_element(tab, "x://div[contains(text(), 'Created On')]", 5):
                    if not is_update:
                        logger.success(f"【{self.id}】已创建过IMAP4专用密码")
                        return None

                    logger.debug(f"【{self.id}】发现已有App，准备删除")
                    get_element(tab, "x://button[@aria-label='Revoke app password']", 3).click()
                    if tab.wait.ele_displayed("x://div[contains(text(), 'has been revoked']", timeout=10):
                        logger.success(f"【{self.id}】正在删除已创建App")
                        sleep(2)
            except Exception:
                logger.warning(f"【{self.id}】点击删除已有App出错")

            try:
                logger.debug(f"【{self.id}】正在输入App Name")
                app_name_i = get_element(tab, "x://span[text()='App name']/following-sibling::input", 3)
                app_name_i.clear(True)
                app_name_i.input("myapp")
            except Exception:
                logger.warning(f"【{self.id}】输入App Name出错")

            try:
                logger.debug(f"【{self.id}】正在点击Create")
                get_element(tab, "x://span[text()='Create']/parent::button", 5).click()
                tab.wait.ele_displayed("x://span[text()='Your app password for your device']", timeout=5)
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Create出错")

            try:
                logger.debug(f"【{self.id}】正在获取IMAP4专用密码")
                codes_ele = get_elements(tab, "x://div[@dir='ltr']/span", 5)
                imap4_password = "".join([code.text.strip().replace(" ", "") for code in codes_ele])
                if imap4_password:
                    logger.success(f"【{self.id}】获取IMAP4专用密码成功, IMAP4={imap4_password}")
                return imap4_password
            except Exception:
                logger.warning(f"【{self.id}】点击Create出错")

            try:
                logger.debug(f"【{self.id}】正在点击Done")
                get_element(tab, "x://span[text()='Done']", 3).click()
                sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Done出错")

        except Exception as e:
            logger.error(f"【{self.id}】获取IMAP4专用密码失败, error={str(e)}")

        return imap4_password

    def _get_email_verify_code(self, email, email_pwd, proxy_email):
        try:
            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Email verification code",
                to=proxy_email,
                is_read=False,
            )
            emails = email_client.search_emails_with_retry(search_criteria, 15)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            subject = email["subject"]
            code_match = re.search(r"(\d{6})", subject)
            if not code_match:
                logger.error(f"{self.id} 未找到验证码")
                return None

            return code_match.group(1)
        except Exception as e:
            logger.error(f"{self.id} 获取验证码失败: {e}")
            return None

    def _handle_avatar(self, tab):
        # 点击“添加个人资料按钮”
        try:
            get_element(tab, "x://div[@data-item-type='49']", 3).click()
            logger.debug(f"【{self.id}】正在点击“添加个人资料”")
            sleep(3)
        except Exception:
            pass

        # 点击“添加头像”
        try:
            iframe = get_frame(tab, 1, 3)

            avatar_btn = get_element(
                iframe,
                "x://*[@id='c3']/preceding-sibling::main[1]/div/child::div[2]//button",
                3,
            ) or get_element(
                iframe,
                "x://*[@id='c0']/preceding-sibling::main[1]/div/child::div[2]//button",
                3,
            )
            avatar_btn.click()
            logger.debug(f"【{self.id}】正在点击“添加头像”")
            sleep(3)
        except Exception:
            pass

        # 随机选取一张
        try:
            avatar_eles = get_elements(tab, "x://button[@class='AzaTwf V9VN3c Iq3YXe']", 3)
            avatar = random.choice(avatar_eles)
            avatar.click()
            logger.debug(f"【{self.id}】正在随机选头像")
            sleep(3)
        except Exception:
            pass

        # 点击下一步
        try:
            iframe = get_frame(tab, 1, 3)
            get_element(
                iframe,
                "x://button[@id='d4Qwgb']/ancestor::*[5]/following-sibling::div[1]//button",
                3,
            ).click(True)
            logger.debug(f"【{self.id}】正在点击下一步")
            sleep(3)
        except Exception:
            pass

        try:
            iframe = get_frame(tab, 1, 3)
            save_btn = (
                get_element(
                    iframe,
                    "x://*[@id='c81']/preceding-sibling::div[1]/child::div[2]/div/child::div[2]/div/child::div["
                    "1]/child::*[5]/child::div[2]//button",
                    3,
                )
                or get_element(
                    iframe,
                    "x://*[@id='c59']/preceding-sibling::div[1]/child::div[2]/div/child::div[2]/div/child::div["
                    "1]/child::*[5]/child::div[2]//button",
                    3,
                )
                or get_element(
                    iframe,
                    "x://*[@id='c68']/preceding-sibling::div[1]/child::div[2]/div/child::div[2]/div/child::div["
                    "1]/child::*[5]/child::div[2]//button",
                    3,
                )
                or get_element(
                    iframe,
                    "x://*[@id='c78']/preceding-sibling::div[1]/child::div[2]/div/child::div[2]/div/child::div["
                    "1]/child::*[5]/child::div[2]//button",
                    3,
                )
            )
            save_btn.click()
            logger.debug(f"【{self.id}】正在点击保存头像")
            tab.wait.ele_displayed("x://div[@id='tt-c115']/preceding-sibling::button", timeout=15)
        except Exception:
            pass

        # 关闭弹框
        try:
            iframe = get_frame(tab, 1, 3)
            close_btn = (
                get_element(iframe, "x://div[@id='tt-c108']/preceding-sibling::button", 3)
                or get_element(iframe, "x://div[@id='tt-c109']/preceding-sibling::button", 3)
                or get_element(iframe, "x://div[@id='tt-c115']/preceding-sibling::button", 3)
                or get_element(iframe, "x://div[@id='tt-c117']/preceding-sibling::button", 3)
            )
            close_btn.click()
            logger.success(f"【{self.id}】头像设置成功")
            sleep(2)
        except Exception:
            pass

    def _handle_get_reminder(self, tab):
        try:
            get_element(tab, "x://div[@data-item-type='43']", 3).click(True)
            logger.debug(f"【{self.id}】正在点击开启提醒按钮")
            sleep(3)
        except Exception:
            pass

        try:
            get_element(tab, "x://button[@data-mdc-dialog-action='ok']", 3)
            logger.debug(f"【{self.id}】正在点击Ok按钮")
            sleep(3)
            logger.success(f"【{self.id}】动态提醒设置成功")
        except Exception:
            pass

    def _handle_recovery_email(self, tab, recovery_email):
        try:
            get_element(
                tab,
                "x://input[@name='challengeListId']/preceding-sibling::div[1]/ul/li[3]/div",
                3,
            ).click()
            tab.wait.ele_displayed(
                "x://input[@id='knowledge-preregistered-email-response']",
                timeout=3,
            )
            recovery_email_i = get_element(
                tab,
                "x://input[@id='knowledge-preregistered-email-response']",
                timeout=3,
            )
            sleep(1)
            recovery_email_i.clear(True)
            recovery_email_i.input(recovery_email)

            get_element(
                tab,
                "x://input[@id='knowledge-preregistered-email-response']/ancestor::*[15]/following-sibling::div/div//button",
                3,
            ).click()
        except Exception as e:
            logger.error(f"【{self.id}】处理辅助邮箱失败， error={str(e)}")

    def check_status(self) -> dict:
        try:
            tab = self._page.new_tab("https://myaccount.google.com/")
            if tab.wait.url_change("https://accounts.google.com/v3/signin/confirmidentifie", timeout=5):
                return {"success": True, "is_login": False, "is_suspended": True, "message": "Account Is Suspended"}
            if get_element(tab, "x://a[contains(@href, 'https://accounts.google.com/SignOutOptions')]", 5):
                return {"success": True, "is_login": True, "is_suspended": False, "message": "Account Login Success"}
            if get_element(tab, "x://a[contains(@href, 'https://accounts.google.com/ServiceLogin')]", 5):
                return {"success": True, "is_login": False, "is_suspended": False, "message": "Account Not Login"}
            return {"success": False, "is_login": False, "is_suspended": False, "message": "网络异常或其他错误"}
        except Exception as e:
            return {"success": False, "is_login": False, "is_suspended": False, "message": str(e)}

    def get_backup_code(self, password: str):
        try:
            tab = self._page.new_tab(self._security_url)
            logger.debug(f"【{self.id}】等待元素加载")
            tab.wait.ele_displayed("x://a[contains(@href, 'two-step-verification/backup-codes')]", 10)

            try:
                logger.debug(f"【{self.id}】检查是否需要输入密码")
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错,retry...")

            try:
                logger.debug(f"【{self.id}】检查是否需要点击Next")
                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 3)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Next")
                    next_btn.click()
                    tab.wait.ele_displayed("x://a[contains(@href, 'two-step-verification/backup-codes')]", timeout=5)
                    sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在点击Backup code")
                tab.scroll.to_see("x://a[contains(@href, 'two-step-verification/backup-codes')]")
                get_element(
                    tab,
                    "x://a[contains(@href, 'two-step-verification/backup-codes')]",
                    5,
                ).click()
                sleep(3)
            except Exception:
                logger.warning(f"【{self.id}】开启2FA时未找到Turn on 2-Step Verification按钮")

            try:
                logger.debug(f"【{self.id}】正在检查是否需要输入密码")
                pwd_i = get_element(tab, "x://input[@name='Passwd']", 3)
                if pwd_i:
                    logger.debug(f"【{self.id}】正在输入密码")
                    pwd_i.clear(True)
                    pwd_i.input(password)
                    sleep(0.5)
            except Exception:
                logger.warning(f"【{self.id}】输入密码出错,retry...")

            try:
                next_btn = get_element(tab, "x://div[@id='passwordNext']//button", 5)
                if next_btn:
                    logger.debug(f"【{self.id}】正在点击Next")
                    next_btn.click()
                    tab.wait.ele_displayed("x://span[text()='Get backup codes']/parent::button", timeout=5)
                    sleep(1)
            except Exception:
                logger.warning(f"【{self.id}】点击Next出错,retry...")

            try:
                backup_btn = get_element(tab, "x://span[text()='Get backup codes']/parent::button", 5)
                if backup_btn:
                    logger.debug(f"【{self.id}】正在点击Get backup codes")
                    backup_btn.click()
                    sleep(5)
            except Exception:
                logger.warning(f"【{self.id}】点击Get backup codes出错,retry...")

            try:
                logger.debug(f"【{self.id}】正在获取备份码")
                codes_ele = get_elements(tab, "x://div[@dir='ltr']", 5)
                backup_codes = [code.text.strip().replace(" ", "") for code in codes_ele]
                return "|".join(backup_codes)
            except Exception:
                logger.warning(f"【{self.id}】获取备份码出错,retry...")

            return None
        except Exception as e:
            logger.error(f"【{self.id}】开启2FA异常，error={str(e)}")
        return None
