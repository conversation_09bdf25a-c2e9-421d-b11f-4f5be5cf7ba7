import os
import smtplib
from datetime import datetime
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from string import Template

from dotenv import load_dotenv

# 加载.env文件
load_dotenv()


class EmailSender:
    _instance = None

    @staticmethod
    def get_project_root():
        """获取项目根目录的绝对路径.

        Returns
        -------
            str: 项目根目录的绝对路径
        """
        # 获取当前文件的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 返回项目根目录（假设当前文件在 src/utils 下）
        return os.path.abspath(os.path.join(current_dir, "..", ".."))

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # 从环境变量加载配置
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.qq.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.sender = os.getenv("SENDER_EMAIL")
        self.password = os.getenv("EMAIL_PASSWORD")

        if not self.sender or not self.password:
            raise ValueError("邮箱配置不完整，请检查.env文件中的 SENDER_EMAIL 和 EMAIL_PASSWORD 配置")

        # 处理默认收件人列表
        recipients = os.getenv("DEFAULT_RECIPIENTS", "")
        self.default_recipients = [r.strip() for r in recipients.split(",") if r.strip()]

        # 获取模板路径
        alert_template_path = os.getenv("ALERT_TEMPLATE_PATH", "data/alert.html")
        report_template_path = os.getenv("REPORT_TEMPLATE_PATH", "data/report.html")

        # 转换为绝对路径
        self.alert_template_path = os.path.join(self.get_project_root(), alert_template_path)
        self.report_template_path = os.path.join(self.get_project_root(), report_template_path)

        # 加载模板
        self.templates = self._load_templates()

        self._initialized = True

        # 验证必要的配置
        if not all([self.sender, self.password]):
            raise ValueError("邮箱配置不完整，请检查.env文件中的SENDER_EMAIL和EMAIL_PASSWORD配置")

    def _load_templates(self) -> dict:
        """加载邮件模板."""
        templates = {}

        # 尝试加载告警模板
        try:
            with open(self.alert_template_path, encoding="utf-8") as f:
                templates["alert"] = f.read()
        except FileNotFoundError as e:
            raise ValueError(f"告警模板文件不存在: {self.alert_template_path}") from e

        # 尝试加载报告模板
        try:
            with open(self.report_template_path, encoding="utf-8") as f:
                templates["report"] = f.read()
        except FileNotFoundError as e:
            raise ValueError(f"报告模板文件不存在: {self.report_template_path}") from e

        return templates

    def send_alert(
        self,
        alert_type: str,
        message: str,
        recipients: str | list[str] | None = None,
        attachments: list[str] | None = None,
    ) -> bool:
        """
        发送告警邮件.

        Parameters
        ----------
        alert_type: str
            告警类型
        message: str
            告警消息
        recipients: str | list[str] | None
            指定收件人（可选）
        attachments: list[str] | None
            附件列表（可选）
        :return: 是否发送成功
        """
        template = Template(self.templates["alert"])
        content = template.safe_substitute(
            alert_type=alert_type, message=message, time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        subject = f"系统告警 - {alert_type}"
        return self._send(subject, content, recipients, attachments)

    def send_report(
        self,
        report_type: str,
        summary: str,
        content: str,
        recipients: str | list[str] | None = None,
        attachments: list[str] | None = None,
    ) -> bool:
        """
        发送报告邮件.

        Parameters
        ----------
        report_type: str
            报告类型
        summary: str
            报告概要
        content: str
            报告详细内容
        recipients: str | list[str] | None
            指定收件人（可选）
        :param attachments: 附件列表（可选）
        :return: 是否发送成功
        """
        template = Template(self.templates["report"])
        content = template.safe_substitute(
            report_type=report_type, summary=summary, content=content, time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        subject = f"系统报告 - {report_type}"
        return self._send(subject, content, recipients, attachments)

    def _send(
        self,
        subject: str,
        content: str,
        recipients: str | list[str] | None = None,
        attachments: list[str] | None = None,
    ) -> bool:
        """
        内部发送邮件方法.

        Parameters
        ----------
        subject: str
            邮件主题
        content: str
            邮件内容
        recipients: str | list[str] | None
            收件人列表
        attachments: list[str] | None
            附件列表
        :return: 是否发送成功
        """
        recipients = recipients or self.default_recipients
        if isinstance(recipients, str):
            recipients = [recipients]

        msg = MIMEMultipart()
        msg["From"] = self.sender
        msg["To"] = ", ".join(recipients)
        msg["Subject"] = subject

        msg.attach(MIMEText(content, "html", "utf-8"))

        if attachments:
            for attachment in attachments:
                if os.path.exists(attachment):
                    with open(attachment, "rb") as f:
                        part = MIMEApplication(f.read())
                        part.add_header("Content-Disposition", "attachment", filename=os.path.basename(attachment))
                        msg.attach(part)

        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender, self.password)
            server.sendmail(self.sender, recipients, msg.as_string())
            server.quit()
            return True
        except Exception as e:
            print(f"发送邮件时出错: {str(e)}")
            return False

    def send_mail(
        self,
        subject: str,
        content: str,
        recipients: str | list[str] | None = None,
        attachments: list[str] | None = None,
        is_html: bool = False,
    ) -> bool:
        """
        直接发送邮件，不使用模板.

        Parameters
        ----------
        subject: str
            邮件主题
        content: str
            邮件内容
        recipients: str | list[str] | None
            收件人列表或单个收件人
        attachments: list[str] | None
            附件文件路径列表
        is_html: bool = False
            内容是否为HTML格式，默认为False（纯文本）
        :return: 是否发送成功

        示例:
            # 发送纯文本邮件
            sender.send_mail(
                subject="测试邮件",
                content="这是一封测试邮件",
                recipients="<EMAIL>"
            )

            # 发送HTML邮件
            sender.send_mail(
                subject="HTML测试邮件",
                content="<h1>测试标题</h1><p>这是一封HTML测试邮件</p>",
                recipients=["<EMAIL>", "<EMAIL>"],
                is_html=True,
                attachments=["file1.pdf", "file2.txt"]
            )
        """
        recipients = recipients or self.default_recipients
        if isinstance(recipients, str):
            recipients = [recipients]

        msg = MIMEMultipart()
        msg["From"] = self.sender
        msg["To"] = ", ".join(recipients)
        msg["Subject"] = subject

        # 根据是否是HTML内容设置不同的内容类型
        content_type = "html" if is_html else "plain"
        msg.attach(MIMEText(content, content_type, "utf-8"))

        # 处理附件
        if attachments:
            for attachment in attachments:
                if os.path.exists(attachment):
                    try:
                        with open(attachment, "rb") as f:
                            part = MIMEApplication(f.read())
                            part.add_header("Content-Disposition", "attachment", filename=os.path.basename(attachment))
                            msg.attach(part)
                    except Exception as e:
                        print(f"添加附件 {attachment} 时出错: {str(e)}")
                        continue

        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender, self.password)
            server.sendmail(self.sender, recipients, msg.as_string())
            server.quit()
            return True
        except Exception as e:
            print(f"发送邮件时出错: {str(e)}")
            return False


if __name__ == "__main__":
    pass
    # sender = EmailSender()
    # sender.send_mail(subject="测试邮件", content="这是一封测试邮件")
