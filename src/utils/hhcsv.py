import csv
import os
import threading
from contextlib import contextmanager


class HHCSV:
    def __init__(self, file_path: str, headers: list[str] | None = None):
        """
        Initialize HHCSV with file path and optional headers.

        Args:
            file_path (str): Path to the CSV file.
            headers (Optional[List[str]]): Headers for the CSV file if creating a new one.

        Raises
        ------
            ValueError: If headers are invalid or file path is not accessible.
        """
        self.file_path = os.path.abspath(file_path)  # Use absolute path for clarity
        self.lock = threading.RLock()
        self.headers = headers if headers else []
        self.data: list[dict[str, str]] = []
        self._validate_file_path()
        self._ensure_file_exists()
        self.load()

    def _validate_file_path(self) -> None:
        """Validate that the file path is usable."""
        if not self.file_path:
            raise ValueError("File path cannot be empty.")
        try:
            directory = os.path.dirname(self.file_path) or "."
            if not os.access(directory, os.W_OK):
                raise ValueError(f"Directory {directory} is not writable.")
        except OSError as e:
            raise ValueError(f"Invalid file path: {str(e)}")  # noqa: B904

    def _ensure_file_exists(self) -> None:
        """Ensure the file exists; create it with headers if it doesn't."""
        if not os.path.exists(self.file_path):
            if not self.headers or not all(isinstance(h, str) and h for h in self.headers):
                raise ValueError("Valid headers must be provided to create a new file.")
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)  # Create directory if needed  # noqa: PTH103
            with self._file_writer() as writer:
                writer.writeheader()
        else:
            # if file exists, without headers
            self.headers = None

    @contextmanager
    def _file_reader(self):
        """Context manager for reading CSV file with proper encoding and error handling."""
        try:
            with open(self.file_path, newline="", encoding="utf-8-sig") as file:
                yield csv.DictReader(file)
        except (OSError, csv.Error, UnicodeDecodeError) as e:
            raise ValueError(f"Error reading CSV file: {str(e)}")  # noqa: B904

    @contextmanager
    def _file_writer(self):
        try:
            with open(self.file_path, mode="w", newline="", encoding="utf-8-sig") as file:
                writer = csv.DictWriter(file, fieldnames=self.headers)
                yield writer
                file.flush()
                os.fsync(file.fileno())
        except (OSError, csv.Error) as e:
            raise ValueError(f"Error writing CSV file: {str(e)}") # noqa: B904

    def load(self) -> None:
        """Load CSV file data into memory."""
        with self.lock, self._file_reader() as reader:
            if not reader.fieldnames:
                raise ValueError("CSV file has no headers.")
            self.headers = list(reader.fieldnames) if not self.headers else self.headers
            if set(self.headers) != set(reader.fieldnames):
                raise ValueError("Headers in file do not match provided headers.")
            self.data = [row for row in reader if all(k in self.headers for k in row)]

    def _save(self) -> None:
        """Save data to CSV file."""
        with self.lock, self._file_writer() as writer:
            writer.writeheader()
            writer.writerows(self.data)

    def _check_and_update_header(self, datas: dict[str, str]) -> None:
        """Update headers if new keys are present in the data."""
        new_keys = [key for key in datas if key not in self.headers and isinstance(key, str)]
        if new_keys:
            print(f"Adding new headers: {', '.join(new_keys)}")
            self.headers.extend(new_keys)
            for row in self.data:
                for key in new_keys:
                    row[key] = ""

    def add_row(self, row: dict[str, str]) -> None:
        """Add a new row to the CSV file."""
        with self.lock:
            if not isinstance(row, dict):
                raise TypeError("Row must be a dictionary.")
            if not all(isinstance(k, str) for k in row):
                raise ValueError("All row keys must be strings.")
            self._check_and_update_header(row)
            # Ensure row contains all headers, filling missing ones with empty strings
            complete_row = {h: row.get(h, "") for h in self.headers}
            self.data.append(complete_row)
            self._save()

    def delete_row(self, criteria: dict[str, str]) -> int:
        """Delete rows matching the criteria. Returns the number of deleted rows."""
        with self.lock:
            if not isinstance(criteria, dict):
                raise TypeError("Criteria must be a dictionary.")
            original_len = len(self.data)
            self.data = [row for row in self.data if not self.matches_criteria(row, criteria)]
            deleted_count = original_len - len(self.data)
            if deleted_count > 0:
                self._save()
            return deleted_count

    def update_row(self, criteria: dict[str, str], updates: dict[str, str]) -> int:
        """Update rows matching criteria with new values. Returns the number of updated rows."""
        with self.lock:
            if not isinstance(criteria, dict) or not isinstance(updates, dict):
                raise TypeError("Criteria and updates must be dictionaries.")
            if not all(isinstance(k, str) for k in updates):
                raise ValueError("All update keys must be strings.")
            self._check_and_update_header(updates)
            updated_count = 0
            for row in self.data:
                if self.matches_criteria(row, criteria):
                    row.update(updates)
                    updated_count += 1
            if updated_count > 0:
                self._save()
            return updated_count

    def query(self, criteria: dict[str, str] = None) -> list[dict[str, str]]:
        """Query rows matching the criteria."""
        with self.lock:
            if criteria is None:
                criteria = {}
            if not isinstance(criteria, dict):
                raise TypeError("Criteria must be a dictionary.")
            return [row for row in self.data if self.matches_criteria(row, criteria)]

    @staticmethod
    def matches_criteria(row: dict[str, str], criteria: dict[str, str]) -> bool:
        """Check if a row matches the given criteria."""
        return all(row.get(key) == value for key, value in criteria.items())

    def query_pro(self, criteria: dict[str, str | tuple[str, str]] = None) -> list[dict[str, str]]:
        """Query rows with advanced criteria supporting comparison operators."""
        with self.lock:
            if criteria is None:
                criteria = {}
            if not isinstance(criteria, dict):
                raise TypeError("Criteria must be a dictionary.")
            return [row for row in self.data if self.matches_criteria_pro(row, criteria)]

    @staticmethod
    def matches_criteria_pro(row: dict[str, str], criteria: dict[str, str | tuple[str, str]]) -> bool:
        """Check if a row matches advanced criteria with comparison operators."""

        def compare(value: str, operator: str, target: str) -> bool:
            try:
                # Attempt numeric comparison if possible
                value_num = float(value) if value.replace(".", "").isdigit() else value
                target_num = float(target) if target.replace(".", "").isdigit() else target
            except (ValueError, TypeError):
                value_num, target_num = str(value), str(target)

            if operator == "==":
                return value_num == target_num
            elif operator == ">":
                return value_num > target_num if isinstance(value_num, int | float) else False
            elif operator == "<":
                return value_num < target_num if isinstance(value_num, int | float) else False
            elif operator == ">=":
                return value_num >= target_num if isinstance(value_num, int | float) else False
            elif operator == "<=":
                return value_num <= target_num if isinstance(value_num, int | float) else False
            else:
                raise ValueError(f"Unsupported operator: {operator}")

        for key, condition in criteria.items():
            row_value = row.get(key, "")
            if isinstance(condition, tuple) and len(condition) == 2:
                operator, target_value = condition
                if not compare(row_value, operator, target_value):
                    return False
            else:
                if row_value != str(condition):
                    return False
        return True
