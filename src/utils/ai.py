import os
from typing import List, Dict, Optional
from openai import OpenAI
import time
import random
from loguru import logger

class AI:
    def __init__(self, 
                api_key: str = None, 
                model: str = "deepseek-chat",
                base_url: str = "https://api.deepseek.com"):
        """
        初始化AI对话工具
        
        Args:
            api_key: OpenAI API密钥，如果不提供则从环境变量获取
            model: 使用的模型名称，默认为deepseek-chat
            base_url: API基础地址，默认为DeepSeek的API地址
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("未提供API密钥")
            
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=base_url
        )
        self.model = model
        self.conversation_history: List[Dict] = []
        
    def chat(self, 
            message: str, 
            system_prompt: str = None,
            temperature: float = 0.7,
            max_tokens: int = 1000,
            keep_history: bool = False,
            history_length: int = 5) -> Optional[str]:
        """
        发送消息并获取回复
        
        Args:
            message: 用户消息
            system_prompt: 系统提示词
            temperature: 温度参数，控制随机性，0-2之间
            max_tokens: 最大token数
            keep_history: 是否保留对话历史
            history_length: 保留的历史对话轮数
            
        Returns:
            模型的回复，如果出错则返回None
        """
        try:
            # 准备消息列表
            messages = []
            
            # 添加system prompt
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
                
            # 添加历史对话
            if keep_history:
                messages.extend(self.conversation_history[-history_length:])
                
            # 添加当前消息
            messages.append({
                "role": "user",
                "content": message
            })
            
            # 调用API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # 获取回复
            reply = response.choices[0].message.content
            
            # 更新对话历史
            if keep_history:
                self.conversation_history.append({
                    "role": "user",
                    "content": message
                })
                self.conversation_history.append({
                    "role": "assistant",
                    "content": reply
                })
                
                # 保持历史长度
                if len(self.conversation_history) > history_length * 2:
                    self.conversation_history = self.conversation_history[-history_length * 2:]
            
            return reply
            
        except Exception as e:
            logger.error(f"AI对话出错: {e}")
            return None
            
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
        
    def get_history(self) -> List[Dict]:
        """获取对话历史"""
        return self.conversation_history.copy() 