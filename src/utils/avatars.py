from time import sleep

from DrissionPage import Chromium
from loguru import logger


class Avatar:
    def download(self, save_dir, count=100):
        url = "https://www.pexels.com/zh-cn/new-photos/"
        page = Chromium()
        tab = page.new_tab(url)

        downloaded = 0
        max_scroll_attempts = 100
        downloaded_srcs = set()

        # 获取图片
        for scroll_attempt in range(max_scroll_attempts):
            tab.run_js("window.scrollTo(0, document.body.scrollHeight);")
            sleep(3)  # 增加等待时间确保图片加载

            try:
                tab.ele("加载更多", timeout=10).click()
            except:
                pass

            imgs = tab('x://*[@id="-"]/div[1]/div').eles("t:img")

            # 下载新获取的图片
            for img in imgs:
                if downloaded >= count:
                    logger.success(f"已完成目标数量 {count} 张图片的下载")
                    return

                try:
                    src = img.attr("src")
                    if not src or src in downloaded_srcs:
                        continue

                    file_name = f"{downloaded + 1}"
                    tab.download(src, save_dir, rename=file_name)
                    downloaded_srcs.add(src)
                    downloaded += 1

                    logger.info(f"已下载 {downloaded}/{count} 张图片")

                except Exception as e:
                    logger.warning(f"下载图片失败: {e}")
                    continue

            if downloaded >= count:
                break

            if len(imgs) == 0:
                logger.warning("未找到更多图片，可能已到达页面底部")
                break

        logger.success(f"实际下载完成 {downloaded} 张图片")


def main():
    count = input("请输入要下载的图片数量: ") or 100
    save_dir = input("请输入图片保存路径: ")
    if not save_dir:
        return

    save_dir = save_dir.strip("'\"")

    Avatar().download(save_dir, int(count))


if __name__ == "__main__":
    main()
