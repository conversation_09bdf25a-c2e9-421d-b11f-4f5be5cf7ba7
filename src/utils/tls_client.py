import platform
import random
import warnings
from time import sleep

from curl_cffi.requests import BrowserType, Session
from fake_useragent import UserAgent

DISABLE_SSL = False
MAX_TRIES = 3

user_agent = UserAgent(browsers=["Chrome"], os=["Windows", "Mac OS X"])

USER_AGENT = user_agent.random
SEC_CH_UA = '"Google Chrome";v="133", "Chromium";v="133", "Not_A Brand";v="24"'
SEC_CH_UA_PLATFORM = '"macOS"'
IMPERSONATE = BrowserType.chrome133a

if platform.system() == "Windows":
    IMPERSONATE = BrowserType.chrome124
    USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
    SEC_CH_UA = '"Chromium";v="124", "Google Chrome";v="124", "Not-A.Brand";v="99"'
else:
    IMPERSONATE = BrowserType.chrome133a

warnings.filterwarnings("ignore", module="curl_cffi")


def retry(func):
    def wrapper(*args, **kwargs):
        tries, delay = MAX_TRIES, 1.5
        while tries > 0:
            try:
                return func(*args, **kwargs)
            except Exception:
                tries -= 1
                if tries <= 0:
                    raise
                sleep(delay)

                delay *= 2
                delay += random.uniform(0, 1)
                delay = min(delay, 10)

    return wrapper


def get_default_headers():
    return {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        "sec-ch-ua": SEC_CH_UA,
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": SEC_CH_UA_PLATFORM,
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": USER_AGENT,
    }


class TLSClient:
    def __init__(self, proxy: str, custom_headers: dict = None, custom_cookies: dict = None, debug=False):
        self._headers = {}
        self.proxy = proxy
        self.proxies = {"http": self.proxy, "https": self.proxy} if self.proxy else {}
        headers = get_default_headers()
        if custom_headers is not None:
            headers.update(custom_headers)
        self.sess = Session(
            proxies=self.proxies,
            headers=headers,
            cookies=custom_cookies,
            impersonate=IMPERSONATE,
        )
        self.debug = debug

    def close(self):
        self.sess.close()

    @classmethod
    def _handle_response(cls, resp_raw, acceptable_statuses=None, resp_handler=None, with_text=False):
        if acceptable_statuses and len(acceptable_statuses) > 0 and resp_raw.status_code not in acceptable_statuses:
            raise Exception(f"Bad status code [{resp_raw.status_code}]: Response = {resp_raw.text}")
        try:
            if with_text:
                return resp_raw.text if resp_handler is None else resp_handler(resp_raw.text)
            else:
                return resp_raw.json() if resp_handler is None else resp_handler(resp_raw.json())
        except Exception as e:
            raise Exception(
                f"{str(e)}: Status = {resp_raw.status_code}. Response saved in logs/errors.txt\n{resp_raw.text}"
            ) from e

    def update_headers(self, new_headers: dict):
        self.sess.headers.update(new_headers)

    @retry
    def _raw_request(self, method, url, debug=False, **kwargs):
        if debug:
            print("tls cookies", self.sess.cookies)
            print("new cookies", kwargs.get("cookies"))
            print("tls headers", self.sess.headers)
            print("new headers", kwargs.get("headers"))
        match method.lower():
            case "get":
                resp = self.sess.get(url, **kwargs)
            case "post":
                resp = self.sess.post(url, **kwargs)
            case unexpected:
                raise Exception(f"Wrong request method: {unexpected}")
        return resp

    def request(self, method, url, acceptable_statuses=None, resp_handler=None, with_text=False, raw=False, **kwargs):
        if "timeout" not in kwargs:
            kwargs.update({"timeout": 60})
        if DISABLE_SSL:
            kwargs.update({"verify": False})
        resp = self._raw_request(method, url, **kwargs)
        if raw:
            return resp
        return self._handle_response(resp, acceptable_statuses, resp_handler, with_text)

    def get(self, url, acceptable_statuses=None, resp_handler=None, with_text=False, **kwargs):
        return self.request("GET", url, acceptable_statuses, resp_handler, with_text, **kwargs)

    def post(self, url, acceptable_statuses=None, resp_handler=None, with_text=False, **kwargs):
        return self.request("POST", url, acceptable_statuses, resp_handler, with_text, **kwargs)
