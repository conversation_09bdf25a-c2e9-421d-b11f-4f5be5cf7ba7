import keyring


class Keychain:
    def get_password(self, service: str, username: str) -> str:
        """获取密码

        Args:
            service: 服务名
            username: 用户名

        Returns:
            str: 密码

        """
        return keyring.get_password(service, username)

    def set_password(self, service: str, username: str, password: str) -> bool:
        """设置密码

        Args:
            service: 服务名
            username: 用户名
            password: 密码

        Returns:
            bool: 是否设置成功
        """
        return keyring.set_password(service, username, password)
