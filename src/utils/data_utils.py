from threading import Lock

from loguru import logger

from src.utils.hhcsv import HHCSV


class DataUtil:
    DEFAULT_HEADERS = ["index", "type", "address"]
    _csv_lock = Lock()

    def __init__(self, csv_path: str, headers: list[str] = None):
        if not csv_path:
            raise ValueError("csv 路径不能为空")
        self.headers = headers if headers is not None else self.DEFAULT_HEADERS.copy()
        self._init_csv(csv_path, self.headers)

    def _init_csv(self, csv_path: str, headers: list[str]):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            self._csv = HHCSV(csv_path, headers)
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def list(self):
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部数据失败: {str(e)}")
            return []

    def get(self, address):
        try:
            result = self._csv.query({"address": address})
            return result[0] if result else {}
        except Exception as e:
            logger.error(f"查询 {address} 数据失败: {str(e)}")
            return {}

    def add(self, data):
        with self._csv_lock:
            try:
                self._csv.add_row(data)
                logger.info(f"添加 CSV 记录: {data}")
            except Exception as e:
                logger.error(f"新增数据失败, data={data}, error={str(e)}")
                raise

    def update(self, address, data):
        with self._csv_lock:
            try:
                criteria = {"address": address}
                result = self._csv.update_row(criteria, data)
                logger.info(f"更新 CSV 记录: address={address}, data={data}, result={result}")
                return result > 0
            except Exception as e:
                logger.error(f"更新数据失败, address={address}, data={data}, error={str(e)}")
                return False

    def flush(self):
        try:
            self._csv.load()
        except Exception as e:
            logger.error(f"刷新数据失败, error={str(e)}")
