from typing import Optional
from loguru import logger
from time import sleep


class YesCaptcha:
    """YesCaptcha 验证码服务"""

    def __init__(self, api_key: str):
        self.api_key = api_key

    @classmethod
    def get_web_plugin_token_hcaptcha(cls, page, browser_id: str) -> Optional[str]:
        """获取验证码token"""
        return cls.get_web_plugin_token(page, browser_id, "hcaptcha")

    @classmethod
    def get_web_plugin_token_recaptcha(cls, page, browser_id: str) -> Optional[str]:
        """获取验证码token"""
        return cls.get_web_plugin_token(page, browser_id, "recaptcha")

    @classmethod
    def is_plugin_exists(self, tab) -> bool:
        """检查 yesCaptcha 插件是否存在"""
        return tab.run_js("return typeof yesCaptcha !== 'undefined'")

    @classmethod
    def get_web_plugin_token(
        cls, page, browser_id: str, captcha_type: str
    ) -> Optional[str]:
        """获取验证码token"""
        try:
            # 等待验证码加载
            sleep(5)

            # 检查 yesCaptcha 对象是否存在
            is_plugin_exists = page.run_js("return typeof yesCaptcha !== 'undefined'")
            if not is_plugin_exists:
                logger.error(f"{browser_id} YesCaptcha 插件未安装")
                return None

            # 注入验证码处理脚本
            js_script = f"""
                document.body.removeAttribute('data-captcha-token');
                yesCaptcha.{captcha_type}Start((token) => {{
                    console.log('token', token);
                    document.body.setAttribute('data-captcha-token', token || 'captcha_success');
                }});
            """
            page.run_async_js(js_script)
            # page.run_async_js(
            #     """
            #     document.body.removeAttribute('data-captcha-token');
            #     yesCaptcha.hcaptchaStart((token) => {
            #         console.log('token', token);
            #         document.body.setAttribute('data-captcha-token', token || 'captcha_success');
            #     });
            #     """
            # )

            # 轮询获取验证码结果
            for _ in range(120):  # 最多等待120秒
                token = page.run_js(
                    "return document.body.getAttribute('data-captcha-token')"
                )
                if token:
                    logger.success(f"{browser_id} 验证码处理成功")
                    return token
                sleep(1)

            logger.error(f"{browser_id} 验证码处理超时")
            return None

        except Exception as e:
            logger.error(f"{browser_id} 验证码处理异常: {e}")
            return None
