from typing import Union, List, Optional, Callable
from DrissionPage import Chromium
from loguru import logger
from enum import Enum
import requests

class ResourceType(Enum):
    """资源类型枚举"""
    IMAGE = "Image"
    SCRIPT = "Script"
    VIDEO = "Video"

class Blocker:
    """
    网页请求拦截器
    用于拦截和控制浏览器的网络请求，支持资源拦截和API拦截
    """
    
    def __init__(self, tab: Chromium) -> None:
        """
        初始化拦截器
        Args:
            tab: Chromium 的标签页对象
        """
        self.tab = tab
        self.call_back = None
        self.target_type: Optional[str] = None
        self.target_url: Optional[str] = None
        self.api_patterns: List[str] = []
        self.debug = False
        self.is_active = False
        self.on_intercept: Optional[Callable] = None 

    def _handle_request(self, **args) -> None:
        """
        资源请求拦截处理
        Args:
            args: 请求的详细信息
        """
        if self.debug:
            logger.debug(args)

        r_id = args["requestId"]
        r_type = args["resourceType"]
        r_url = args["request"]["url"]

        # 根据API模式进行拦截
        for pattern in self.api_patterns:
            if pattern in r_url:
                logger.info(f"拦截API请求: {r_url}")
                self.tab.run_cdp("Fetch.failRequest", 
                               requestId=r_id, 
                               errorReason="BlockedByClient")
                if self.on_intercept:
                    self.on_intercept(args)
                return

        # 根据资源类型进行拦截
        if r_type == self.target_type:
            self.tab.run_cdp("Fetch.failRequest", 
                           requestId=r_id, 
                           errorReason="BlockedByClient")
            return

        # 默认继续请求
        self.tab.run_cdp("Fetch.continueRequest", requestId=r_id)


    def start(self) -> 'Blocker':
        """启用请求拦截功能"""
        if not self.is_active:
            self.tab.run_cdp("Fetch.enable")
            self.tab.driver.set_callback("Fetch.requestPaused", self.call_back)
            self.is_active = True
        return self

    def stop(self) -> 'Blocker':
        """停止请求拦截功能"""
        if self.is_active:
            self.tab.run_cdp("Fetch.disable")
            self.tab.driver.set_callback("Fetch.requestPaused", None)
            self.is_active = False
            self.call_back = None
            self.target_type = None
            self.api_patterns = []
            self.on_intercept = None
            logger.info("拦截器已停止")
        return self

    def block(self, resource_type: str) -> 'Blocker':
        """
        设置要拦截的资源类型
        Args:
            resource_type: 资源类型 ("img"/"js"/"video")
        """
        resource_map = {
            "img": ResourceType.IMAGE.value,
            "js": ResourceType.SCRIPT.value,
            "video": ResourceType.VIDEO.value
        }
        
        self.target_type = resource_map.get(resource_type)
        self.call_back = self._handle_request
        return self.start()

    def block_api(self, patterns: Union[str, List[str]], on_intercept: Optional[Callable] = None) -> 'Blocker':
        """
        设置要拦截的API模式
        Args:
            patterns: API URL关键字，可以是字符串或字符串列表
            on_intercept: 可选的拦截回调函数，接收请求信息作为参数
        """
        self.api_patterns = [patterns] if isinstance(patterns, str) else patterns
        self.call_back = self._handle_request
        self.on_intercept = on_intercept
        return self.start()

    def get(self, url: str) -> None:
        """
        访问指定URL
        Args:
            url: 目标URL
        """
        self.tab.get(url)

def make_request(request_info, proxy=None):
    """
    发起请求，支持代理
    Args:
        request_info: 请求信息
        proxy: 代理地址，如 "http://127.0.0.1:7890"
    """
    url = request_info['request']['url']
    method = request_info['request']['method']
    headers = request_info['request']['headers']

    # 设置代理
    proxies = None
    if proxy:
        proxies = {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }
    
    try:
        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            proxies=proxies,
            timeout=10  # 添加超时设置
        )
        logger.info(f"请求成功: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
        return response
    except Exception as e:
        logger.error(f"请求失败: {e}")
        return None


def main():
    """测试代码"""
    # 创建页面对象
    tab = Chromium().latest_tab

    # 创建拦截器实例
    blocker = Blocker(tab)
    blocker.debug = False

    # 定义回调函数
    def on_api_intercept(request_info):
        logger.info(f"API被拦截，请求信息: {request_info['request']['url']}")
        make_request(request_info, proxy="127.0.0.1:42060")

    # 示例1：拦截特定API，并使用回调函数
    blocker.block(ResourceType.IMAGE.value)
    blocker.block_api("/ipv6", on_intercept=on_api_intercept)
    
    # 访问测试页面
    blocker.get("https://ip.im")

    # 停止拦截
    input("按回车停止拦截...")
    blocker.stop()

    # # 再次访问页面，验证拦截已停止
    # blocker.get("https://ip.im")


if __name__ == "__main__":
    main()
