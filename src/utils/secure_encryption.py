import os
import base64
from loguru import logger
from typing import Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from src.utils.keychain import Keychain


class SecureEncryption:
    """基于密码的加密工具类"""

    def __init__(self, password: Optional[str] = None):
        """初始化加密工具

        Args:
            password: 用户输入的密码，如果为空，则使用已设置的密码
        """

        # 获取或生成salt
        salt = self._get_salt_bytes()
        # 如果是新生成的salt，保存它
        if not salt:
            salt = os.urandom(16)
            self._set_salt_str(base64.b64encode(salt).decode())

        # 从密码生成密钥
        if not password:
            password = self._get_password()

        if not password:
            raise ValueError("密码不能为空")

        if len(password) < 8:  # 添加密码强度检查
            raise ValueError("密码长度必须大于8位")

        key = self._generate_key(password, salt)
        self.fernet = Fernet(key)

    @staticmethod
    def set_password(password: str) -> bool:
        """设置密码

        Args:
            password: 要设置的密码，必须至少8位

        Returns:
            bool: 设置成功返回True，失败返回False

        Raises:
            ValueError: 当密码长度小于8位时抛出
        """
        if not password:
            raise ValueError("密码不能为空")
        if len(password) < 8:
            raise ValueError("密码长度必须大于8位")
        return Keychain().set_password("system", "AES_KEY", password)

    @staticmethod
    def set_salt_str(salt_str: str) -> bool:
        """设置salt"""
        try:
            return SecureEncryption()._set_salt_str(salt_str)
        except Exception as e:
            logger.error(f"设置salt失败: {e}")
            return False

    @staticmethod
    def decrypt(ciphertext: str, password: Optional[str] = None) -> Optional[str]:
        """解密数据

        Args:
            ciphertext: 要解密的密文
            password: 要解密的密码

        Returns:
            str: 解密后的数据

        """
        if not ciphertext:
            raise ValueError("密文不能为空")

        return SecureEncryption(password).decrypt_data(ciphertext)

    @staticmethod
    def encrypt(data: str, password: Optional[str] = None) -> Optional[str]:
        """加密数据

        Args:
            data: 要加密的数据
            password: 要加密的密码，如果为空，则使用已设置的密码

        Returns:
            str: 加密后的数据
        """
        return SecureEncryption(password).encrypt_data(data)

    def _set_salt_str(self, salt_str: str) -> bool:
        """将salt保存到keychain

        Args:
            salt_str: 要保存的salt值

        Returns:
            bool: 保存成功返回True，失败返回False
        """
        try:
            return Keychain().set_password("system", "ENCRYPTION_SALT", salt_str)
        except Exception as e:
            logger.error(f"保存salt失败: {e}")
            return False

    def _get_salt_str(self) -> Optional[str]:
        """从keychain获取salt值

        Returns:
            Optional[str]: 存储的salt值，如果没有找到则返回None
        """
        return Keychain().get_password("system", "ENCRYPTION_SALT")

    def _get_salt_bytes(self) -> Optional[bytes]:
        """从keychain获取salt值

        Returns:
            Optional[bytes]: 存储的salt值，如果没有找到则返回None
        """
        salt_str = self._get_salt_str()
        if salt_str:
            try:
                return base64.b64decode(salt_str)
            except Exception as e:
                logger.error(f"解析salt失败: {e}")
        return None

    def _get_password(self) -> str:
        """获取密码

        Returns:
            str: 存储的密码，如果没有找到则返回空字符串
        """
        password = Keychain().get_password("system", "AES_KEY")
        return password if password else ""

    def _generate_key(self, password: str, salt: bytes) -> bytes:
        """从密码生成Fernet密钥"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key

    def encrypt_data(self, data: str) -> Optional[str]:
        """加密数据"""
        if not data:
            logger.error("加密数据为空")
            return None

        if SecureEncryption.is_encrypted(data):
            logger.warning("数据已经是加密的")
            return data
        try:
            return self.fernet.encrypt(data.encode()).decode()
        except Exception as e:
            logger.error(f"加密失败: {e}")
            return None

    def decrypt_data(self, encrypted_data: str) -> Optional[str]:
        """解密数据"""
        if not encrypted_data:
            logger.error("解密数据为空")
            return None

        if not SecureEncryption.is_encrypted(encrypted_data):
            logger.error("数据格式不是有效的加密格式")
            return None

        try:
            return self.fernet.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"解密失败: {e}")
            return None

    @staticmethod
    def is_encrypted(data: str) -> bool:
        """检查数据是否已经被加密

        通过验证数据是否符合 Fernet 令牌格式来判断是否为加密数据。
        Fernet 令牌以 'gAAAAA' 开头，并且可以被 base64 解码。

        Args:
            data: 要检查的数据字符串

        Returns:
            bool: 如果数据是加密格式返回 True，否则返回 False
        """
        try:
            # 检查基本特征：Fernet 加密数据通常以 'gAAAAA' 开头
            if not data.startswith("gAAAAA"):
                return False

            # 尝试进行 base64 解码
            try:
                base64.urlsafe_b64decode(data)
                return True
            except Exception as e:
                return False

        except Exception as e:
            return False

    def export_password_and_salt(self, file_path: Optional[str] = None) -> bool:
        """导出密码和salt到文件

        Args:
            file_path: 导出文件的路径，默认为'encryption_backup.txt'

        Returns:
            bool: 导出成功返回True，失败返回False
        """
        try:
            if file_path is None:
                file_path = "encryption_backup.txt"

            password = self._get_password()
            salt_str = self._get_salt_str()
            export_data = f"PASSWORD={password}\nSALT={salt_str}"

            with open(file_path, "w", encoding="utf-8") as f:
                f.write(export_data)

            logger.info(f"成功导出加密信息到: {file_path}")
            return True

        except Exception as e:
            logger.error(f"导出加密信息失败: {e}")
            return False
