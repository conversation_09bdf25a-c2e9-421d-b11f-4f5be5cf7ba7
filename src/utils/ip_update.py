import os
import random
from typing import Dict, List, Optional, Tuple

import requests

from src.fingerprints.bit import updateBrowserProxy
from src.utils.hhcsv import HHCSV


class IPManager:
    """IP和代理管理类，提供IP池管理和代理更新功能"""

    def __init__(self, ip_pools_file: str = r"data/ip_pools.csv", config_csv: str = r"data/bit.csv"):
        """
        初始化IP管理器

        Args:
            ip_pools_file: IP池文件路径
            csv_tool: CSV操作工具对象
        """
        self.ip_pools_file = ip_pools_file
        self.csv_tool = HHCSV(
            config_csv,
            headers=[
                "id",
                "browser_id",
                "user_agent",
                "proxy",
                "region",
                "evm_address",
            ],
        )

    def update_proxy(self, configs: list[dict]):
        """更新配置文件中的代理设置"""
        if not configs:
            raise ValueError("配置列表不能为空")

        try:
            for config in configs:
                current_proxy = config.get("proxy", "")
                current_country = config.get("region", "")

                if not current_proxy:
                    # 如果proxy为空，随机获取一个可用IP
                    ip_port, country = self.get_random_available_ip(self.ip_pools_file, 1)
                    if ip_port:
                        config["proxy"] = ip_port
                        config["region"] = country
                        self.update_ip_usage(self.ip_pools_file, ip_port, 1)
                elif current_country:  # 使用config中的region而不是重新读取文件
                    # 根据country随机选择一个新的IP（排除当前IP）
                    with open(self.ip_pools_file) as f:
                        lines = f.readlines()
                        available_ips = []
                        for line in lines[1:]:
                            parts = line.strip().split(",")
                            if (
                                len(parts) > 2
                                and parts[2] == current_country
                                and parts[0] != current_proxy
                                and (not parts[3].strip() or int(parts[3]) < 2)
                            ):
                                available_ips.append((parts[0], parts[2]))

                        if available_ips:
                            while available_ips:  # 循环直到没有可用的IP
                                new_ip, country = random.choice(available_ips)
                                config["proxy"] = new_ip
                                config["region"] = country
                                result = self.test_proxy(new_ip)
                                if result:
                                    self.update_ip_usage(self.ip_pools_file, current_proxy, -1)
                                    self.update_ip_usage(self.ip_pools_file, new_ip, 1)
                                    break  # 成功后退出循环
                                else:
                                    available_ips.remove((new_ip, country))  # 移除失败的IP
                        else:
                            print("改区域没有可用IP,随机取一个")
                            ip_port, country = self.get_random_available_ip(self.ip_pools_file, 1)
                            if ip_port:
                                config["proxy"] = ip_port
                                config["region"] = country
                                result = self.test_proxy(ip_port)
                                if result:
                                    self.update_ip_usage(self.ip_pools_file, current_proxy, -1)
                                else:
                                    available_ips.remove((ip_port, country))

                                # raise ValueError("没有可用的IP地址")  # 如果没有可用的IP，抛出错误

                # Update only the region and proxy fields
                if self.csv_tool:
                    criteria = {"id": config["id"]}
                    updates = {"region": config.get("region", ""), "proxy": config.get("proxy", "")}
                    self.csv_tool.update_row(criteria=criteria, updates=updates)

                    filename = os.path.basename(self.csv_tool.file_path)
                    if filename == "bit.csv":
                        if not updateBrowserProxy(
                            config.get("browser_id", ""),
                            config.get("proxy", "").split(":")[2],
                            config.get("region", ""),
                        ):  # 调用 updateBrowserProxy
                            print(f"error:{config.get('proxy', '')},端口更新失败")
                    print(f"success:{config.get('proxy', '')},端口更新成功")
                    return True
        except Exception as e:
            raise Exception(f"更新代理设置失败: {str(e)}")

    def get_random_available_ip(self, input_file: str = None, occupy_num: int = 1) -> tuple[str | None, str | None]:
        """
        从IP池随机获取一个使用次数小于2的可用IP，并更新其使用次数

        Args:
            input_file: 输入文件路径，如果为None则使用默认路径
            occupy_num: 要设置的使用次数

        Returns
        -------
            Tuple[str, str]: (ip_port, country) 如果找到可用IP，否则返回 (None, None)
        """
        if input_file is None:
            input_file = self.ip_pools_file

        try:
            # 读取所有行
            with open(input_file) as f:
                lines = f.readlines()

            header = lines[0]  # 保存标题行
            available_indices = []  # 存储可用IP的行索引

            # 查找可用IP
            for i, line in enumerate(lines[1:], 1):  # 从1开始，跳过标题行
                parts = line.strip().split(",")
                if len(parts) >= 3:
                    ip_port = parts[0]
                    used = parts[3] if len(parts) > 3 else ""

                    # 检查使用次数是否小于2
                    if not used or used.strip() == "" or int(used) < 2:
                        available_indices.append(i)

            # 如果有可用IP，随机选择一个并更新使用次数
            if available_indices:
                chosen_index = random.choice(available_indices)
                parts = lines[chosen_index].strip().split(",")
                ip_port = parts[0]
                country = parts[2]
                # 更新使用次数
                if len(parts) > 3:
                    current_used = int(parts[3]) if parts[3].strip() else 0
                    parts[3] = str(current_used + occupy_num)  # 加上新的使用次数
                else:
                    parts.append(str(occupy_num))

                # 更新该行内容
                lines[chosen_index] = ",".join(parts) + "\n"

                # 写回文件
                with open(input_file, "w") as f:
                    f.writelines(lines)

                return ip_port, country

            return None, None

        except Exception as e:
            print(f"Error processing IP file: {e}")
            return None, None

    def update_ip_usage(self, input_file: str = None, target_ip_port: str = None, occupy_num: int = 0) -> bool:
        """
        更新指定IP端口的使用次数

        Args:
            input_file: 输入文件路径，如果为None则使用默认路径
            target_ip_port: 目标IP端口 (格式: 'socks5://127.0.0.1:port')
            occupy_num: 要增加的使用次数

        Returns
        -------
            bool: 更新成功返回True，否则返回False
        """
        if input_file is None:
            input_file = self.ip_pools_file

        if target_ip_port is None:
            return False

        try:
            # 读取所有行
            with open(input_file) as f:
                lines = f.readlines()

            found = False
            # 查找并更新指定IP的使用次数
            for i, line in enumerate(lines):
                if i == 0:  # 跳过标题行
                    continue

                parts = line.strip().split(",")
                if len(parts) >= 1 and parts[0].strip() == target_ip_port:
                    # 更新使用次数
                    current_used = int(parts[3]) if len(parts) > 3 and parts[3].strip() else 0
                    new_used = current_used + occupy_num

                    if len(parts) > 3:
                        parts[3] = str(new_used)
                    else:
                        while len(parts) < 4:  # 确保有足够的字段
                            parts.append("")
                        parts[3] = str(new_used)

                    # 更新该行内容
                    lines[i] = ",".join(parts) + "\n"
                    found = True
                    break

            if found:
                # 写回文件
                with open(input_file, "w") as f:
                    f.writelines(lines)
                return True

            return False

        except Exception as e:
            print(f"Error updating IP usage: {e}")
            return False

    def test_proxy(self, proxy: str) -> bool:
        proxy = proxy.replace("socks5", "http")
        try:
            response = requests.get(
                "https://www.okx.com/",
                proxies={"http": proxy, "https": proxy},
                timeout=10,
            )

            return response.status_code == 200
        except:
            return False

    def check_and_update_proxy_by_id(self, id: str) -> bool:
        """
        根据ID检查代理是否可用，如果不可用则更新代理

        Args:
            id: 配置ID

        Returns
        -------
            bool: 代理更新是否成功
        """
        configs = self.csv_tool.query({"id": str(id)})

        if configs and not self.test_proxy(proxy=configs[0]["proxy"]):
            if not self.update_proxy(configs):
                return False
            print("代理不可用，已更新")
            return True
        return True

    def _update_proxy_by_id(self, id: str) -> None:
        """
        根据ID更新代理,不检测

        Args:
            id: 配置ID
        """
        configs = self.csv_tool.query({"id": str(id)})

        if configs:
            self.update_proxy(configs)
            print("代理已更新")


# # 使用示例:
# ip_manager = IPManager(config_csv=r"data/bit.csv")
# #ip_manager.check_and_update_proxy_by_id(str(13))
# ip_manager._update_proxy_by_id(str(9))
