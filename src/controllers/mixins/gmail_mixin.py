from loguru import logger

from src.socials.gmail import Gmail
from src.utils.password import generate_pwd

from ..decorators import require_page_and_config
from ..exceptions import BrowserControllerError


class GmailMixin:
    @require_page_and_config
    def login_gmail(self):
        config = self.browser_config
        if config.email_is_suspended == "1":
            logger.error(f"【{self.id}】Gmail Is Suspended")
            return False
        gmail = Gmail(self.id, self.page)
        email = config.email
        email_password = config.email_password
        if not email or not email_password:
            raise BrowserControllerError(f"【{self.id}】Gmail账号未配置")

        recovery_email = config.recovery_email
        recovery_email_pwd = config.recovery_email_pwd
        recovery_proxy_email = config.recovery_proxy_email
        recovery = recovery_email or recovery_proxy_email
        if not recovery:
            raise BrowserControllerError(f"【{self.id}】Gmail辅助邮箱未配置")

        email_two_fa = config.email_two_fa
        return gmail.login_by_password(
            email, email_password, recovery_email, recovery_email_pwd, recovery_proxy_email, email_two_fa
        )

    @require_page_and_config
    def update_gmail_recovery_email(
        self,
        email_password: str,
        current_recovery_email: str,
        new_recovery_email: str,
        new_recovery_email_pwd: str,
        new_recovery_proxy_email: str | None = None,
    ):
        if not email_password or not current_recovery_email or not new_recovery_email or not new_recovery_email_pwd:
            raise BrowserControllerError(f"【{self.id}】Gmail邮箱信息不全")

        gmail = Gmail(self.id, self.page)
        result = gmail.update_mail(
            email_password, current_recovery_email, new_recovery_email, new_recovery_email_pwd, new_recovery_proxy_email
        )

        if result:
            logger.success(f"【{self.id}】辅助邮箱更新成功")
            self.browser_config.recovery_email = new_recovery_email
            self.browser_config.recovery_email_pwd = new_recovery_email_pwd
            self.browser_config.recovery_proxy_email = new_recovery_proxy_email
            self._repository.update(self.browser_config)
        return result

    @require_page_and_config
    def update_gmail_password(self):
        config = self.browser_config
        current_password = config.email_password
        if not current_password:
            raise BrowserControllerError(f"【{self.id}】Gmail登录密码为空")

        recovery_email = config.recovery_email
        recovery_proxy_email = config.recovery_proxy_email
        recovery = recovery_proxy_email or recovery_email
        if not current_password or not recovery:
            raise BrowserControllerError(f"【{self.id}】Gmail辅助邮箱为空")

        new_password = generate_pwd()
        email_two_fa = config.email_two_fa
        gmail = Gmail(self.id, self.page)
        result = gmail.update_pwd(current_password, new_password, recovery, email_two_fa)
        if result:
            logger.success(f"【{self.id}】Gmail密码更新成功, 新密码: {new_password}")
            self.browser_config.email_password = new_password
            self._repository.update(self.browser_config)

        return result

    @require_page_and_config
    def get_gmail_2fa(self):
        config = self.browser_config
        password = config.email_password
        if not password:
            raise BrowserControllerError(f"【{self.id}】Gmail登录密码为空")

        recovery_email = config.recovery_email
        recovery_proxy_email = config.recovery_proxy_email
        recovery = recovery_proxy_email or recovery_email
        if not recovery:
            raise BrowserControllerError(f"【{self.id}】Gmail辅助邮箱为空")

        gmail = Gmail(self.id, self.page)
        success, email_two_fa = gmail.get_2fa_key(password, recovery)
        if success:
            logger.success(f"【{self.id}】Gmail获取2fa成功")

        if email_two_fa:
            logger.success(f"【{self.id}】2fa key={email_two_fa}")
            self.browser_config.email_two_fa = email_two_fa
            self._repository.update(self.browser_config)
        return success

    @require_page_and_config
    def gmail_enable_2fa(self):
        config = self.browser_config
        password = config.email_password
        phone = config.email_verification_phone
        if not password:
            raise BrowserControllerError(f"【{self.id}】Gmail登录密码为空")

        if not phone:
            raise BrowserControllerError(f"【{self.id}】Gmail 2-Step Verification phonew为空")

        gmail = Gmail(self.id, self.page)
        return gmail.enable_two_step_verification(password, phone)

    @require_page_and_config
    def get_gmail_imap4_password(self, is_update: bool = False):
        config = self.browser_config
        password = config.email_password
        if not password:
            raise BrowserControllerError(f"【{self.id}】Gmail登录密码为空")
        if not is_update and config.email_imap4_pwd:
            logger.success(f"【{self.id}】Gmail已获取IMAP4密码")
            return True

        gmail = Gmail(self.id, self.page)
        imap4_password = gmail.get_imap4_password(password, is_update)
        if imap4_password:
            logger.success(f"【{self.id}】获取IMAP4密码成功,imap4_password={imap4_password}")
            self.browser_config.email_imap4_pwd = imap4_password
            self._repository.update(self.browser_config)
        return imap4_password is not None

    def change_gmail_language(self):
        gmail = Gmail(self.id, self.page)
        return gmail.change_language()

    @require_page_and_config
    def gmail_get_backup_code(self):
        gmail = Gmail(self.id, self.page)
        if self.browser_config.email_backup_code:
            logger.success(f"【{self.id}】Gmail 已获取备份码")
            return True

        password = self.browser_config.email_password
        if not password:
            raise BrowserControllerError(f"【{self.id}】Gmail登录密码为空")
        backup_code = gmail.get_backup_code(password)
        if backup_code:
            self.browser_config.email_backup_code = backup_code
            self._repository.update(self.browser_config)
            logger.success(f"【{self.id}】获取备份码成功,backup_code={backup_code}")
            return True
        return False

    @require_page_and_config
    def get_gmail_status(self, login: bool = False) -> bool:
        gmail = Gmail(self.id, self.page)
        status_dict = gmail.check_status()
        if status_dict.get("success", False) and status_dict.get("is_suspended", False):
            logger.error(f"【{self.id}】Gmail Is Suspended")
            self.browser_config.email_is_suspended = 1
            self._repository.update(self.browser_config)
            return True
        if status_dict.get("success", False) and status_dict.get("is_login", False):
            logger.success(f"【{self.id}】Gmail已登录")
            return True
        if status_dict.get("success", False) and not status_dict.get("is_login", False):
            if login:
                return self.login_gmail()
            logger.success(f"【{self.id}】Gmail未登录")
            return True
        message = status_dict.get("message", "网络异常")
        logger.error(f"【{self.id}】{message}")
        return False
