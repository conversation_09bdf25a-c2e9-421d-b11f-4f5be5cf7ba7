import os
from time import sleep

from loguru import logger

from src.browsers.config import get_browser_extension_id

from ..decorators import require_page


class BrowserMixin:
    @require_page
    def open_url(self, url: str, new_tab: bool = False) -> bool:
        """打开指定URL."""
        try:
            if new_tab:
                return self.page.new_tab(url)
            self.page.latest_tab.get(url)
            return self.page.latest_tab
        except Exception as e:
            logger.error(f"{self.id} 打开URL失败: {e}")
            return False

    @require_page
    def close_page(self):
        """关闭浏览器."""
        if self.browser:
            self.browser.close()

    def close_other_tabs(self, tab):
        """关闭其他标签页."""
        if self.page:
            self.page.close_tabs(tab, others=True)

    @require_page
    def clear_site_data(self, domain: str) -> bool:
        """清除站点数据."""
        lasted_page = None
        try:
            # 打开设置页面
            lasted_page = self.page.new_tab(f"chrome://settings/content/all?searchSubpage={domain}")

            # 1. 找到 settings-ui
            settings_ui = lasted_page.ele("x://settings-ui", timeout=10)
            if not settings_ui:
                logger.error(f"{self.id} 未找到 settings-ui")
                return False

            # 2. 找到 main
            main = settings_ui.sr("x://*[@id='main']", timeout=3)
            if not main:
                logger.error(f"{self.id} 未找到 main")
                return False

            # 3. 找到 settings-basic-page
            basic_page = main.sr("x://settings-basic-page", timeout=3)
            if not basic_page:
                logger.error(f"{self.id} 未找到 settings-basic-page")
                return False

            # 4. 找到 settings-privacy-page
            privacy_page = basic_page.sr(
                "x://settings-section[contains(@class, 'expanded')]/settings-privacy-page",
                timeout=3,
            )
            if not privacy_page:
                logger.error(f"{self.id} 未找到 settings-privacy-page")
                return False

            # 5. 找到 settings-subpage 下的 all-sites
            all_sites = privacy_page.sr("x://*[@id='pages']/settings-subpage/all-sites", timeout=3)
            if not all_sites:
                logger.error(f"{self.id} 未找到 all-sites")
                return False

            # 6. 找到 frb0
            frb0 = all_sites.sr("x://*[@id='frb0']", timeout=3)
            if not frb0:
                logger.error(f"{self.id} 未找到数据, 可能没有缓存")
                return True

            # 7. 找到并点击删除按钮
            remove_button = frb0.sr("x://*[@id='removeSiteButton']", timeout=3)
            if not remove_button:
                logger.error(f"{self.id} 未找到删除按钮")
                return False

            remove_button.click()
            logger.success(f"{self.id} 成功点击删除按钮")

            # 8. 找到并点击确认对话框中的确认按钮
            confirm_button = all_sites.sr(
                "x://cr-dialog//cr-button[contains(@class, 'action-button')]",
                timeout=10,
            )
            if not confirm_button:
                logger.error(f"{self.id} 未找到确认按钮")
                return False

            confirm_button.click()

            frb0 = all_sites.sr("x://*[@id='frb0']", timeout=3)
            if frb0 and not frb0.states.has_rect:
                logger.success(f"{self.id} 成功清除站点数据")
                return True

            logger.error(f"{self.id} 清除站点数据失败")
            return False

        except Exception as e:
            logger.error(f"{self.id} 清除站点数据失败: {e}")
            return False
        finally:
            if lasted_page:
                lasted_page.close()

    def yesCaptcha_status(
        self,
        status: int = None,
        hcap_drop_flag: int = None,
        host: str = None,
        extension_id: str = None,
    ) -> bool:
        """
        改变扩展插件的状态.

        Args:
            url: chrome://extensions/?id=gleekbfjekiniecknbkamfmkohkpodhe
            status: True：开启，False：关闭
            hcap_drop_flag:跳过拖动功能

        Returns
        -------
            bool: 是否改变扩展插件的状态
        """
        tab = None
        try:
            tab = self.page.new_tab(f"chrome-extension://{extension_id}/option/index.html")
            if status:
                btn = tab.ele("x://*[@id='app']/div/div[2]/div/div[2]/div/div[2]/span/input")

                status_str = btn.attr("checked")
                if status == 1:
                    if status_str is None:
                        btn.click()
                        sleep(2)
                        tab.ele("x://button[normalize-space()='save']").click()
                        sleep(1)
                        tab.handle_alert(accept=True, timeout=3)
                        return True
                    else:
                        return True
                if status == 0:
                    if status_str is None:
                        return True
                    else:
                        btn.click()
                        sleep(2)
                        tab.ele("x://button[normalize-space()='save']").click()
                        sleep(1)
                        tab.handle_alert(accept=True, timeout=3)
                        return True
            elif host:
                tab.ele("x://*[@id='app']/div/div[2]/div/div[4]/div/div[2]/input").input(host, clear=True)
                sleep(1)
                tab.ele("x://button[normalize-space()='save']").click()
                sleep(1)
                tab.handle_alert(accept=True, timeout=3)

            elif hcap_drop_flag:
                tab.ele("x://span[contains(text(), 'hcaptcha')]").click()
                sleep(0.3)
                btn = tab.ele("x://*[@id='app']/div/div[2]/div/div[5]/div/div[2]/span/input")
                status_str = btn.attr("checked")
                if hcap_drop_flag == 1:
                    if status_str is None:
                        btn.click()
                        sleep(2)
                        tab.ele("x://button[normalize-space()='save']").click()
                        sleep(1)
                        tab.handle_alert(accept=True, timeout=3)

                        return True
                    else:
                        return True
                if hcap_drop_flag == 0:
                    if status_str is None:
                        return True
                    else:
                        btn.click()
                        sleep(2)
                        tab.ele("x://button[normalize-space()='save']").click()
                        tab.handle_alert(accept=True, timeout=3)
                        return True

        except Exception as e:
            logger.warning(f"Error during change extension status, error={str(e)}")
            return False
        finally:
            if tab:
                tab.close()

    def chrome_extension_status(self, extension_id: str, status: bool) -> bool:
        """
        改变扩展插件的状态.

        Args:
            url: chrome://extensions/?id=gleekbfjekiniecknbkamfmkohkpodhe
            status: True：开启，False：关闭

        Returns
        -------
            bool: 是否改变扩展插件的状态
        """
        tab = None

        try:
            tab = self.page.new_tab(f"chrome://extensions/?id={extension_id}")
            btn = (
                tab("t:extensions-manager")
                .sr("t:cr-view-manager")
                .ele("t:extensions-detail-view")
                .sr("@id=container")
                .ele("@id=enableToggle")
            )
            logger.info(f"{self.id} 扩展插件状态: {btn.attr('aria-pressed')}")
            status_str = btn.attr("aria-pressed")
            current_status = status_str.lower() == "true"
            if status != current_status:
                btn.click()
                sleep(1)
            return True
        except Exception as e:
            logger.warning(f"Error during change extension status, error={str(e)}")
            return False
        finally:
            if tab:
                tab.close()
    def brave_extension_status(self, extension_id: str, status: bool) -> bool:
        """
        改变扩展插件的状态.

        Args:
            url: chrome://extensions/?id=gleekbfjekiniecknbkamfmkohkpodhe
            status: True：开启，False：关闭

        Returns
        -------
            bool: 是否改变扩展插件的状态
        """
        tab = None
        type = self.browser_type.value
        try:
            tab = self.page.new_tab(f"{type}://extensions/?id={extension_id}")
            btn = (
                tab("t:extensions-manager")
                .sr("t:cr-view-manager")
                .ele("t:extensions-detail-view")
                .sr("@id=container")
                .ele("@id=enableToggle")
            )
            logger.info(f"{self.id} 扩展插件状态: {btn.attr('aria-pressed')}")
            status_str = btn.attr("aria-pressed")
            current_status = status_str.lower() == "true"
            if status != current_status:
                btn.click()
                sleep(1)
            return True
        except Exception as e:
            logger.warning(f"Error during change extension status, error={str(e)}")
            return False
        finally:
            if tab:
                tab.close()
    def add_extension(self, extension_path: str, extension_id: str) -> bool:
        """添加扩展插件."""
        try:
            # 判断浏览器类型
            from src.browsers import BrowserType

            if self.browser_type not in [BrowserType.CHROME, BrowserType.BRAVE, BrowserType.LOCAL]:
                # if self.browser_type != BrowserType.CHROME and self.browser_type != BrowserType.BRAVE:
                logger.error(f"{self.id} 浏览器类型不支持添加扩展插件")
                return False

            # 判断扩展插件是否存在
            if not os.path.exists(extension_path):
                logger.error(f"{self.id} 扩展插件不存在: {extension_path}")
                return False

            # 添加扩展插件
            self.browser.add_extension({"path": extension_path, "id": extension_id})
            sleep(1)
            return True
        except Exception as e:
            logger.error(f"{self.id} 添加扩展插件失败: {e}")
            return False

    def window_max(self):
        """最大化窗口."""
        try:
            self.page.latest_tab.set.window.max()
        except Exception as _:
            pass
