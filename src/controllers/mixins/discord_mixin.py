from typing import Optional

from loguru import logger

from src.models.browser_config import BrowserConfig
from src.socials import Discord
from src.utils.password import generate_pwd
from src.utils.secure_encryption import SecureEncryption

from ..decorators import require_page_and_config
from ..exceptions import Browser<PERSON>ontroller<PERSON>rror, XLoginError


class DiscordMixin:
    # 登录Discord

    @require_page_and_config
    def login_discord(self) -> bool:
        discord = Discord(self.id, self.page)

        config: BrowserConfig = self.browser_config
        token = config.dc_token
        if token:
            if discord.login_by_token(token):
                logger.success(f"{self.id} Discord 已登录")
                return True
            else:
                # 登录失败，删除 token
                config.dc_token = ""
                logger.error(f"{self.id} token失效，删除缓存token")
                self._repository.update(config)

        user = config.dc_user
        password = config.dc_password
        if not user or not password:
            raise BrowserControllerError(f"{self.id} Discord 账号未配置")

        # 解密密码
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        email = config.dc_email
        email_password = config.dc_email_password
        email_proxy = config.dc_proxy_email
        if SecureEncryption.is_encrypted(email_password):
            email_password = SecureEncryption.decrypt(email_password)
        two_fa = config.dc_two_fa

        success, pwd, token = discord.login_by_password(user, password, email, email_password, email_proxy, two_fa)
        if success and token and password != pwd:
            config.dc_password = pwd
            self._repository.update(config)

        return success

    # 更新Discord密码
    @require_page_and_config
    def update_password_discord(self, encrypt: bool = True) -> bool:
        discord = Discord(self.id, self.page)
        password = self.browser_config.dc_password
        two_fa = self.browser_config.dc_two_fa
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        if not password:
            raise BrowserControllerError(f"{self.id} Discord 密码未配置")

        new_password = generate_pwd()

        if not self.login_discord():
            raise XLoginError(f"{self.id} Discord 未登录")

        result = discord.update_password(password, new_password, two_fa)
        if result:
            logger.success(f"{self.id} Discord 密码更新成功, 新密码: {new_password}")
            if encrypt:
                self.browser_config.dc_password = SecureEncryption.encrypt(new_password)
            else:
                self.browser_config.dc_password = new_password
            self._repository.update(self.browser_config)
        return result

    # 更新Discord邮箱
    @require_page_and_config
    def update_email_discord(
        self,
        dc_password: str,
        current_email: str,
        current_email_password: str,
        new_email: str,
        new_email_password: str,
        new_email_proxy: str | None = None,
    ) -> bool:
        if not current_email or not current_email_password or not new_email or not new_email_password:
            raise BrowserControllerError(f"{self.id} 邮箱信息不能为空")

        password = dc_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        discord = Discord(self.id, self.page)
        result = discord.update_mail(
            password,
            current_email,
            current_email_password,
            new_email,
            new_email_password,
            new_email_proxy,
        )
        if result:
            logger.success(f"{self.id} Discord 邮箱更新成功, 新邮箱: {new_email_proxy or new_email}")
            self.browser_config.dc_email = new_email
            self.browser_config.dc_user = new_email_proxy or new_email
            self.browser_config.dc_email_password = new_email_password
            if new_email_proxy:
                self.browser_config.dc_proxy_email = new_email_proxy
            self._repository.update(self.browser_config)
        return result

    # 更新Discord token
    @require_page_and_config
    def update_dc_token(self) -> bool:
        discord = Discord(self.id, self.page)
        token = discord.get_token()
        if token:
            self.browser_config.dc_token = token
            self._repository.update(self.browser_config)
            logger.success(f"{self.id} Discord token 更新成功")
            return True

        # token失效，用账号密码登录
        logger.info(f"【{self.id}】token获取失败，重新登录账号")
        config: BrowserConfig = self.browser_config
        user = config.dc_user
        password = config.dc_password
        if not user or not password:
            raise BrowserControllerError(f"{self.id} Discord 账号未配置")

        # 解密密码
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        email = config.dc_email
        email_password = config.dc_email_password
        email_proxy = config.dc_proxy_email
        if SecureEncryption.is_encrypted(email_password):
            email_password = SecureEncryption.decrypt(email_password)
        two_fa = config.dc_two_fa

        success, pwd, token = discord.login_by_password(user, password, email, email_password, email_proxy, two_fa)

        if success and token:
            if password != pwd:
                config.dc_password = pwd
            config.dc_token = token
            self._repository.update(config)
            logger.success(f"{self.id} Discord token 更新成功")
            return True
        else:
            config.dc_token = ""
            self._repository.update(config)
            logger.error(f"{self.id} Discord token 获取失败")
            return False

    # 更新Discord id
    @require_page_and_config
    def update_dc_id(self) -> bool:
        discord = Discord(self.id, self.page)
        user_id = discord.get_user_id(self.browser_config.dc_token)

        if not user_id:
            logger.error(f"{self.id} Discord id 获取失败")
            return False

        if user_id != self.browser_config.dc_id:
            self.browser_config.dc_id = f"'{user_id}"
            self._repository.update(self.browser_config)
            logger.success(f"{self.id} Discord id 已更新: {user_id}")
        else:
            logger.info(f"{self.id} Discord id 未变化，无需更新")

        return True

    # 获取Discord用户名并更新
    @require_page_and_config
    def get_dc_user_name(self) -> bool:
        discord = Discord(self.id, self.page)
        before_username = self.browser_config.dc_username
        username = discord.get_username()
        if not username:
            logger.error(f"【{self.id}】Discord用户名获取失败")
            return False
        if username and before_username != username:
            self.browser_config.dc_username = username
            self._repository.update(self.browser_config)
            logger.success(f"【{self.id}】Discord用户名已更新, 更新前={before_username}, 更新后={username}")
        else:
            logger.success(f"【{self.id}】Discord用户名无变化")
        return True

    # 获取Discord邮箱
    @require_page_and_config
    def get_dc_email(self) -> bool:
        discord = Discord(self.id, self.page)
        before_main_email = self.browser_config.dc_email
        before_proxy_email = self.browser_config.dc_proxy_email
        email = discord.get_email()
        before_email = before_proxy_email or before_main_email
        if email and before_email != email:
            # FIXME: 这里需求写死，icloud 邮箱直接写入代理邮箱字段
            if email.endswith("icloud.com"):
                self.browser_config.dc_proxy_email = email
            else:
                self.browser_config.dc_email = email

            self._repository.update(self.browser_config)
            logger.success(f"【{self.id}】Discord邮箱已更新, 更新前={before_email}, 更新后={email}")
        else:
            logger.success(f"【{self.id}】Discord邮箱无变化")
        return True

    @require_page_and_config
    def get_dc_2fa_key(self) -> bool:
        discord = Discord(self.id, self.page)
        if self.browser_config.dc_two_fa:
            logger.success(f"【{self.id}】 Discord 已经开启2FA")
            return self.browser_config.dc_two_fa
        result = discord.get_2fa_key(self.browser_config.dc_password)
        success = result.get("success", False)
        two_fa = result.get("2fa", "")
        backup_code = result.get("backup_code", "")
        if success and len(two_fa) > 0:
            self.browser_config.dc_two_fa = two_fa
            if backup_code:
                self.browser_config.dc_backup_code = backup_code

            self._repository.update(self.browser_config)
            logger.success(f"【{self.id}】 Discord 2FA Key开启成功: 2fa:{two_fa}, backup_code:{backup_code}")
        else:
            logger.error(f"【{self.id}】 Discord 2FA Key开启失败")
        return success

    # 加入Discord群
    @require_page_and_config
    def join_dc_server(
        self,
        invite_code: str,
        need_wait_captcha: bool = True,
        server_id: str = None,
        dc_name: str = None,
    ) -> bool:
        discord = Discord(self.id, self.page, self.browser_type, invite_code, dc_name)
        return discord.join_server(need_wait_captcha, server_id)

    @require_page_and_config
    def leave_guild(
        self,
        dc_name: str = None,
    ) -> bool:
        discord = Discord(self.id, self.page, self.browser_type)
        return discord.leave_guild(dc_name)

    # 重置Discord密码
    @require_page_and_config
    def reset_password(self) -> bool:
        discord = Discord(self.id, self.page)
        config: BrowserConfig = self.browser_config
        user = config.dc_user
        email = config.dc_email
        email_password = config.dc_email_password
        email_proxy = config.dc_proxy_email
        if SecureEncryption.is_encrypted(email_password):
            email_password = SecureEncryption.decrypt(email_password)
        two_fa = config.dc_two_fa

        result = discord.reset_password(user, email, email_password, email_proxy, two_fa)
        if result.get("success", False):
            try:
                token = discord.get_token()
                if token:
                    config.dc_token = token
            except Exception:
                pass
            password = result.get("password", "")
            if password:
                config.dc_password = password

                self._repository.update(config)
                logger.success(f"【{self.id}】 Discord 密码重置成功, 新密码: {password}")
                return True

        return False

    # dc 登出
    @require_page_and_config
    def logout_dc(self) -> bool:
        discord = Discord(self.id, self.page)
        config: BrowserConfig = self.browser_config
        result = discord.logout()
        if result.get("success", False):
            config.dc_token = ""
            self._repository.update(config)
            return True

        return False
