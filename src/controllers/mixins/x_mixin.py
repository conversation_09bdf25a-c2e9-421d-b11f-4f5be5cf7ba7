import os
import re
from time import sleep

from loguru import logger

from src.api.twitter import Twitter
from src.emails.imap4.email_client import EmailClient, SearchCriteria
from src.socials import X
from src.utils import get_element
from src.utils.password import generate_pwd
from src.utils.secure_encryption import SecureEncryption

from ..decorators import require_page_and_config
from ..exceptions import BrowserControllerError, XLoginError

PROXY_URL = os.getenv("PROXY_URL")


class XMixin:
    # 登录推特
    @require_page_and_config
    def login_x(self, is_follow_check=True) -> bool:
        """登录推特."""
        x = X(self.id, self.page)

        for i in range(3):
            result = x.is_login()
            is_login = result.get("is_login", False)
            is_lock = result.get("is_lock", False)

            if not is_follow_check and is_login:
                logger.success(f"{self.id} 推特登录正常")
                return True

            if is_login and is_follow_check:
                # 这里需要真实follow下才能真正判断是否登录正常
                result = x.check_login_by_follow()
                if result.get("success"):
                    logger.success(f"{self.id} 推特登录正常")
                    token = x.get_token()
                    self.browser_config.x_token = token
                    self._repository.update(self.browser_config)

                    logger.success(f"{self.id} 推特登录成功, 并且更新了token")

                    return True
                message = result.get("message") or ""
                if "suspended" in message:
                    logger.error(f"{self.id} 【{self.browser_config.x_user}】 用户被ban了...")
                    self.browser_config.x_token = ""
                    self.browser_config.x_is_suspended = "1"
                    self._repository.update(self.browser_config)
                    return False

                logger.warning(f"{self.id} 关注推特失败,账号登录异常...")

            # twitter帐号被锁定
            if is_lock:
                logger.warning(f"{self.id} 推特账号被锁定, 准备解锁")

                result = x.unlock(
                    self.browser_config.x_email,
                    self.browser_config.x_email_password,
                    self.browser_config.x_proxy_email,
                    self.browser_config.x_two_fa,
                )
                if not result:
                    # 解锁失败, 直接返回登录失败，不重试，否则会让账号被长期锁定
                    logger.error(f"{self.id} 推特账号解锁失败")
                    return False

                logger.success(f"{self.id} 推特账号解锁成功")
                self.browser_config.x_token = ""
                self._repository.update(self.browser_config)

                if is_follow_check:
                    result = x.check_login_by_follow()
                    if result.get("success"):
                        logger.success(f"{self.id} 推特登录正常")
                        return True

                    message = result.get("message") or ""
                    if "suspended" in message:
                        logger.error(f"{self.id} 【{self.browser_config.x_user}】 用户被ban了...")
                        return False

                logger.warning(f"{self.id} 关注推特失败,账号登录异常...")

            # 如果有token, 则使用token登录
            token = self.browser_config.x_token
            if token:
                result = x.login_by_token(token)
                if result:
                    return True

                # twitter 登录失败 清除token, 然后通过用户名密码登录
                self.browser_config.x_token = ""
                self._repository.update(self.browser_config)

            # 使用用户名密码登录
            username = self.browser_config.x_user
            password = self.browser_config.x_password
            if SecureEncryption.is_encrypted(password):
                password = SecureEncryption.decrypt(password)
            if not username or not password:
                raise BrowserControllerError(f"{self.id} 推特账号未配置")

            logger.info(f"{self.id} 准备使用用户名密码登录推特")
            result = x.login_by_user(
                username,
                password,
                self.browser_config.x_two_fa,
                self.browser_config.x_email,
                self.browser_config.x_email_password,
                self.browser_config.x_proxy_email,
            )

            # 登录成功后, 更新token
            is_success = result.get("success")
            is_lock = result.get("is_lock")
            if is_success and not is_lock:
                token = x.get_token()
                self.browser_config.x_token = token
                self._repository.update(self.browser_config)

                logger.success(f"{self.id} 推特登录成功, 并且更新了token")
                return True
            logger.info(f"{self.id} 第{i+1}次登录失败, 准备重试...")
        return False

    def api_follow(self, name="elonmusk"):
        """关注推特用户."""
        try:
            x_token = self.browser_config.x_token
            proxy = self.browser_config.proxy or PROXY_URL

            twitter_api = Twitter(int(self.id), x_token, proxy)
            twitter_api.start()
            twitter_api.follow(name)
            return True
        except Exception as e:
            logger.error(f"{self.id} 关注elonmusk失败: {e}")
            return False

    # 获取邮箱验证码
    def _get_email_verify_code(self, email, email_pwd):
        try:
            email_client = EmailClient(email, email_pwd)
            # 构建搜索条件：搜索过去7天内主题包含 "Verify" 的邮件

            search_criteria = SearchCriteria(
                subject="access all of X’s features",
                from_addr="<EMAIL>",
                # is_read=False
            )
            # 搜索邮件
            for i in range(3):
                logger.info(f"正在搜索符合条件的邮件: {search_criteria}")
                emails = email_client.search_emails(search_criteria)

                # 输出搜索结果
                logger.info(f"找到 {len(emails)} 封符合条件的邮件")

                # 处理找到的邮件
                for i, email in enumerate(emails):
                    logger.info(f"=== 邮件 {i + 1} ===")
                    logger.info(f"主题: {email.get('subject', 'N/A')}")
                    logger.info(f"发件人: {email.get('from', 'N/A')}")
                    logger.info(f"日期: {email.get('date', 'N/A')}")

                    # 提取验证链接示例 (假设验证链接包含在邮件内容中)
                    content = email.get("content", "")
                    # 尝试不同的链接匹配模式
                    verification_patterns = [
                        r"^(\d{6})\s+is your login code for Wallet",  # 匹配 "数字 is your login code for Wallet" 格式
                        r"\b(\d{6})\b",
                        r"^(\d{6})",  # 匹配开头的6位数字
                        r"Email verification code:\s*(\d+)",  # 匹配 Email verification code 格式
                        r"Your one-time code is\s*:\s*(\d+)",  # 匹配 one-time code 格式
                        r"Your one-time code is\s*:\s*(\d+)",  # 匹配其他格式
                        r"Your verification code is:\s*(\d{4})",
                    ]

                    for pattern in verification_patterns:
                        match = re.search(pattern, content)
                        if match:
                            verification_link = match.group(1)
                            logger.info(f"找到验证码: {verification_link}")
                            return verification_link
                    else:
                        logger.warning("未找到验证链接")
                sleep(10)
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def unlock_x(self, latest_tab):
        logger.info(f"【{self.id}解封x")
        if get_element(latest_tab, "x://input[@value='Start']", timeout=10):
            get_element(latest_tab, "x://input[@value='Start']", timeout=10).click()
        if get_element(latest_tab, "x://input[@value='Send email']", timeout=10):
            get_element(latest_tab, "x://input[@value='Send email']", timeout=10).click()
        x_email = self.browser_config.x_email
        x_email_password = self.browser_config.x_email_password
        verify_code = self._get_email_verify_code(x_email, x_email_password)

        verify_ele = get_element(latest_tab, "x://input[@placeholder='Enter Verification Code']", timeout=10)
        verify_ele.input(verify_code)

        get_element(latest_tab, "x://input[@value='Verify']", timeout=10).click()
        get_element(latest_tab, "x://input[@value='Continue to X']", timeout=10).click()

    # 授权页面登录推特
    @require_page_and_config
    def login_x_with_auth(self) -> bool:
        username = self.browser_config.x_user
        password = self.browser_config.x_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)
        if not username or not password:
            raise BrowserControllerError(f"{self.id} 推特账号未配置")

        x = X(self.id, self.page)
        return x.login_with_auth(
            username,
            password,
            self.browser_config.x_two_fa,
            self.browser_config.x_email,
            self.browser_config.x_email_password,
            self.browser_config.x_proxy_email,
        )

    # 删除推文
    @require_page_and_config
    def delete_tweet(self, keyword: str) -> bool:
        x = X(self.id, self.page)
        return x.delete_tweet(keyword)

    # 获取最后一条推文链接
    @require_page_and_config
    def get_last_post_url(self) -> str:
        x = X(self.id, self.page)
        return x.get_last_post_url()

    # 更新推特密码
    @require_page_and_config
    def update_password_x(self, encrypt: bool = True) -> bool:
        x = X(self.id, self.page)
        password = self.browser_config.x_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        if not password:
            raise BrowserControllerError(f"{self.id} 推特密码未配置")

        # TODO: 新密码规则 随机在原先密码处插入 1～n 个 #
        new_password = generate_pwd()

        if not self.login_x():
            raise XLoginError(f"{self.id} 推特未登录")

        result = x.update_password(password, new_password)
        if result:
            logger.success(f"{self.id} 更新推特密码成功, 新密码: {new_password}")
            if encrypt:
                self.browser_config.x_password = SecureEncryption.encrypt(new_password)
            else:
                self.browser_config.x_password = new_password
            self._repository.update(self.browser_config)
        return result

    # 更新推特邮箱
    @require_page_and_config
    def update_email_x(
        self,
    ) -> bool:
        email = self.browser_config.x_email

        email_password = self.browser_config.x_email_password
        if SecureEncryption.is_encrypted(email_password):
            email_password = SecureEncryption.decrypt(email_password)

        password = self.browser_config.x_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        proxy_email = self.browser_config.x_proxy_email
        if not email or not email_password:
            raise BrowserControllerError(f"{self.id} 推特邮箱未配置")

        if not password:
            raise BrowserControllerError(f"{self.id} 推特密码未配置")

        if not self.login_x():
            raise XLoginError(f"{self.id} 推特未登录")
        return X(self.id, self.page).update_email(password, email, email_password, proxy_email)

    # 获取邮箱
    @require_page_and_config
    def get_emails_x(self):
        x = X(self.id, self.page)
        return x.get_emails()

    # 添加推特用户名后缀
    @require_page_and_config
    def add_nickname_suffix_x(self, suffix: str) -> bool:
        if not suffix:
            raise BrowserControllerError(f"{self.id} 推特昵称后缀未配置")

        x = X(self.id, self.page)
        if not self.login_x():
            raise XLoginError(f"{self.id} 推特未登录")
        return x.add_nickname_suffix(suffix)

    # 移除推特昵称后缀
    @require_page_and_config
    def remove_nickname_suffix_x(self, suffix: str) -> bool:
        if not suffix:
            raise BrowserControllerError(f"{self.id} 推特昵称后缀未配置")

        x = X(self.id, self.page)
        if not self.login_x():
            raise XLoginError(f"{self.id} 推特未登录")
        return x.remove_nickname_suffix(suffix)

    # 更新推特昵称
    @require_page_and_config
    def update_nickname_x(self, nickname: str) -> bool:
        if not nickname:
            raise BrowserControllerError(f"{self.id} 推特昵称未配置")

        x = X(self.id, self.page)
        if not self.login_x():
            raise XLoginError(f"{self.id} 推特未登录")

        return x.update_nickname(nickname)

    # 切换推特语言到英语
    @require_page_and_config
    def change_language_to_english_x(self) -> bool:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录")
        return x.change_language_to_english()

    # 设置生日
    @require_page_and_config
    def set_birthday(self):
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录")
        return x.set_birthday()

    @require_page_and_config
    def setup_profile_x(self, avatar_path, header_image_path, bio, location):
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录")
        return x.setup_profile(avatar_path, header_image_path, bio, location)

    # 更新token
    @require_page_and_config
    def update_token(self):
        x = X(self.id, self.page)
        if not self.login_x():
            raise XLoginError(f"{self.id} 推特登录异常或未登录")
        token = x.get_token()
        if token:
            self.browser_config.x_token = token
            self._repository.update(self.browser_config)
        return token

    @require_page_and_config
    def two_fa_x(self):
        password = self.browser_config.x_password
        if SecureEncryption.is_encrypted(password):
            password = SecureEncryption.decrypt(password)

        if not password:
            raise BrowserControllerError(f"{self.id} 推特密码未配置")

        x = X(self.id, self.page)
        if not self.login_x():
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        fa_key = x.enable_2fa(password)
        if fa_key:
            self.browser_config.x_two_fa = fa_key
            self._repository.update(self.browser_config)
        return fa_key

    # 关注用户
    @require_page_and_config
    def follow(self, username: str) -> bool:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        return x.follow(username)

    @require_page_and_config
    def batch_follow(self, usernames: list[str]) -> dict[str, bool]:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        return x.batch_follow(usernames)

    @require_page_and_config
    def batch_follow_for_mac(self, usernames: list[str]) -> dict[str, bool]:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        return x.batch_follow_for_mac(usernames)

    # 点赞推文
    @require_page_and_config
    def favorite(self, url: str, force=False) -> bool:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        return x.favorite(url, force)

    # 转发推文
    @require_page_and_config
    def retweet(self, url: str, force=False) -> bool:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        return x.retweet(url, force)

    # 点赞并转发推文
    @require_page_and_config
    def favorite_and_retweet(self, url: str, force=False) -> bool:
        x = X(self.id, self.page)
        if not self.login_x(is_follow_check=False):
            raise XLoginError(f"{self.id} 推特未登录或登录异常")
        return x.favorite_and_retweet(url, force)
