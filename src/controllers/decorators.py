from collections.abc import Callable
from functools import wraps
from typing import Any, TypeVar

from loguru import logger

from .exceptions import BrowserControllerError

T = TypeVar("T")


def require_page(func: Callable[..., T]) -> Callable[..., T]:
    """验证页面是否存在的装饰器"""

    @wraps(func)
    def wrapper(self: Any, *args, **kwargs) -> T:
        if not self._page:
            return False

        try:
            return func(self, *args, **kwargs)
        except BrowserControllerError as e:
            logger.warning(f"{self.id} {func.__name__} 失败: {e}")
            return False
        except Exception as e:
            logger.error(f"{self.id} {func.__name__} 异常: {e}")
            return False

    return wrapper


def require_wallet_config(func: Callable[..., T]) -> Callable[..., T]:
    """验证钱包配置是否存在的装饰器"""

    @wraps(func)
    def wrapper(self: Any, *args, **kwargs) -> T:
        if not hasattr(self, "wallet_config") or not self.browser_config:
            raise BrowserControllerError("钱包配置未找到")
        try:
            return func(self, *args, **kwargs)
        except BrowserControllerError as e:
            logger.warning(f"{self.id} {func.__name__} 失败: {e}")
            return False
        except Exception as e:
            logger.error(f"{self.id} {func.__name__} 异常: {e}")
            return False

    return wrapper


def require_page_and_config(func: Callable[..., T]) -> Callable[..., T]:
    """验证页面和钱包配置的装饰器."""

    @wraps(func)
    def wrapper(self: Any, *args, **kwargs) -> T:
        if not hasattr(self, "page") or not self.page:
            raise BrowserControllerError("页面未初始化")
        if not hasattr(self, "browser_config") or not self.browser_config:
            raise BrowserControllerError("钱包配置未找到")
        try:
            return func(self, *args, **kwargs)
        except BrowserControllerError as e:
            logger.warning(f"{getattr(self, 'id', None)} {func.__name__} 失败: {e}")
            return False
        except Exception as e:
            logger.error(f"{getattr(self, 'id', None)} {func.__name__} 异常: {e}")
            return False

    return wrapper
