import os
from abc import ABC, abstractmethod
from collections.abc import Callable
from functools import wraps
from typing import Any, TypeVar

from loguru import logger

from src.models.browser_config import BrowserConfig
from src.utils.common import get_project_root_path
from src.utils.hhcsv import HHCSV

T = TypeVar("T")


class BrowserRepository(ABC):
    """浏览器配置仓储接口.

    Attributes
    ----------
        browser_type: str
        config_path: str
    """

    @abstractmethod
    def get_by_id(self, id: str) -> BrowserConfig | None:
        """根据ID获取浏览器配置."""
        pass

    @abstractmethod
    def get_all(self) -> list[BrowserConfig]:
        """获取所有浏览器配置."""
        pass

    @abstractmethod
    def save(self, config: BrowserConfig) -> bool:
        """保存浏览器配置."""
        pass

    @abstractmethod
    def update(self, config: BrowserConfig) -> bool:
        """更新浏览器配置."""
        pass


def require_config_file(func: Callable[..., T]) -> Callable[..., T]:
    """验证配置文件是否存在的装饰器."""

    @wraps(func)
    def wrapper(self: Any, *args, **kwargs) -> T:
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"配置文件不存在: {self.config_path}")
                return func.__annotations__.get("return")()
            return func(self, *args, **kwargs)
        except Exception as e:
            logger.error(f"{func.__name__} 失败: {e}")
            return func.__annotations__.get("return")()

    return wrapper


class CSVBrowserRepository(BrowserRepository):
    """CSV实现的浏览器配置仓储."""

    def __init__(self, browser_type: str):
        self.browser_type = browser_type
        self.config_path = self._get_config_path()

    def _get_config_path(self) -> str:
        """获取配置文件完整路径."""
        from src.browsers.config import get_browser_data_path

        path = get_browser_data_path(self.browser_type)
        if not os.path.isabs(path):  # noqa: PTH117
            path = os.path.join(get_project_root_path(), path)
        return path

    @require_config_file
    def get_by_id(self, id: str) -> BrowserConfig | None:
        """根据ID获取浏览器配置."""
        hhcsv = HHCSV(self.config_path)
        results = hhcsv.query(criteria={"id": str(id)})

        if not results:
            logger.warning(f"{id} 未找到对应的配置")
            return None

        if len(results) > 1:
            logger.warning(f"{id} 找到多个配置项，使用第一个")

        return BrowserConfig(**results[0])

    @require_config_file
    def get_all(self) -> list[BrowserConfig]:
        """获取所有浏览器配置."""
        hhcsv = HHCSV(self.config_path)
        results = hhcsv.query()
        return [BrowserConfig(**row) for row in results]

    @require_config_file
    def save(self, config: BrowserConfig) -> bool:
        """保存浏览器配置."""
        hhcsv = HHCSV(self.config_path)
        # 先删除已存在的配置
        hhcsv.delete_row(criteria={"id": config.id})
        # 保存新配置
        hhcsv.add_row(config.__dict__)
        return True

    @require_config_file
    def update(self, config: BrowserConfig) -> bool:
        """更新浏览器配置."""
        hhcsv = HHCSV(self.config_path)
        updated = hhcsv.update_row(criteria={"id": config.id}, updates=config.__dict__)
        if not updated:
            logger.warning(f"未找到ID为 {config.id} 的配置，更新失败")
            return False
        return True

    @require_config_file
    def encrypt_column(self, column: str) -> int:
        """加密指定列的数据."""
        from src.utils.secure_encryption import SecureEncryption

        hhcsv = HHCSV(self.config_path)
        results = hhcsv.query()
        encrypted_count = 0
        for row in results:
            try:
                data = row[column]
                if not data:
                    continue

                if SecureEncryption.is_encrypted(data):
                    logger.warning(f"数据已经是加密的: {data}, 跳过")
                    continue

                encrypted_data = SecureEncryption.encrypt(data)
                hhcsv.update_row(criteria={"id": row["id"]}, updates={column: encrypted_data})
                encrypted_count += 1
            except Exception as e:
                logger.error(f"加密失败: {e}")
                continue

        return encrypted_count

    @require_config_file
    def decrypt_column(self, column: str) -> int:
        """解密指定列的数据."""
        from src.utils.secure_encryption import SecureEncryption

        hhcsv = HHCSV(self.config_path)
        results = hhcsv.query()
        decrypted_count = 0
        for row in results:
            try:
                data = row[column]
                if not data:
                    continue

                if not SecureEncryption.is_encrypted(data):
                    logger.warning(f"数据已经是解密的: {data}, 跳过")
                    continue

                decrypted_data = SecureEncryption.decrypt(data)
                hhcsv.update_row(criteria={"id": row["id"]}, updates={column: decrypted_data})
                decrypted_count += 1
            except Exception as e:
                logger.error(f"解密失败: {e}")
                continue

        return decrypted_count
