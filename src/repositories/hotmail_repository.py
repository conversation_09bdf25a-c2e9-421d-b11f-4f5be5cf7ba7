import os
from abc import ABC, abstractmethod
from datetime import datetime
from functools import wraps
from typing import Any, Callable, List, TypeVar

from loguru import logger

from src.models.email import Email
from src.utils.common import get_project_root_path
from src.utils.hhcsv import HHCSV

T = TypeVar("T")


class HotmailRepository(ABC):
    """邮箱配置仓储接口"""

    @abstractmethod
    def get_by_browser_id(self, browser_id: str) -> Email | None:
        """根据浏览器ID获取配置"""
        pass

    @abstractmethod
    def get_all(self) -> list[Email]:
        """获取所有邮箱配置"""
        pass

    @abstractmethod
    def update(self, config: Email) -> bool:
        """更新邮箱配置"""
        pass


def require_config_file[T](func: Callable[..., T]) -> Callable[..., T]:
    """验证配置文件是否存在的装饰器"""

    @wraps(func)
    def wrapper(self: Any, *args, **kwargs) -> T:
        try:
            if not os.path.exists(self.config_path):
                logger.error(f"配置文件不存在: {self.config_path}")
                return func.__annotations__.get("return")()
            return func(self, *args, **kwargs)
        except Exception as e:
            logger.error(f"{func.__name__} 失败: {e}")
            return func.__annotations__.get("return")()

    return wrapper


class CSVHotmailRepository(HotmailRepository):
    """CSV实现的邮箱配置仓储"""

    def __init__(self, browser_type: str):
        self.browser_type = browser_type
        self.config_path = os.path.join(get_project_root_path(), f"data/hotmail_{browser_type}.csv")

    @require_config_file
    def get_by_browser_id(self, browser_id: str) -> Email | None:
        """根据浏览器ID获取配置"""
        hhcsv = HHCSV(self.config_path)
        results = hhcsv.query(criteria={"id": browser_id})

        if not results:
            logger.warning(f"未找到邮箱配置: {browser_id}")
            return None

        if len(results) > 1:
            logger.warning(f"邮箱 {browser_id} 存在多个配置，使用第一个")

        return Email(**results[0])

    @require_config_file
    def get_all(self) -> List[Email]:
        """获取所有邮箱配置"""
        hhcsv = HHCSV(self.config_path)
        results = hhcsv.query()
        return [Email(**row) for row in results]

    @require_config_file
    def update(self, config: Email) -> bool:
        """更新邮箱配置"""
        hhcsv = HHCSV(self.config_path)
        config.updated_at = datetime.now().isoformat()

        updated = hhcsv.update_row(criteria={"email": config.email}, updates=config.__dict__)
        if not updated:
            logger.warning(f"未找到邮箱 {config.email} 的配置，更新失败")
            return False
        return True

    @require_config_file
    def update_password(self, browser_id: str, new_password: str) -> bool:
        """更新密码"""
        config = self.get_by_browser_id(browser_id)
        if not config:
            return False

        config.password = new_password
        return self.update(config)
