from typing import Optional, Dict
from web3 import Web3
from decimal import Decimal
import os
from loguru import logger
from web3.types import TxParams
from src.evm.erc20_utils import get_transaction, send_transaction
from web3.constants import MAX_INT


class ERC20:

    # ERC20 标准 ABI - 包含 balanceOf , decimals , approve 方法
    ABI = [
        {
            "constant": True,
            "inputs": [{"name": "owner", "type": "address"}],
            "name": "balanceOf",
            "outputs": [{"name": "", "type": "uint256"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function",
        },
        {
            "constant": True,
            "inputs": [],
            "name": "decimals",
            "outputs": [{"name": "", "type": "uint8"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function",
        },
        {
            "constant": True,
            "inputs": [
                {"name": "owner", "type": "address"},
                {"name": "spender", "type": "address"},
            ],
            "name": "allowance",
            "outputs": [{"name": "", "type": "uint256"}],
            "payable": False,
            "stateMutability": "view",
            "type": "function",
        },
        {
            "constant": False,
            "inputs": [
                {"name": "spender", "type": "address"},
                {"name": "value", "type": "uint256"},
            ],
            "name": "approve",
            "outputs": [{"name": "", "type": "bool"}],
            "payable": False,
            "stateMutability": "nonpayable",
            "type": "function",
        },
    ]

    # 初始化, 从外部传入provider实例
    def __init__(
        self,
        contract_address: str,
        provider: Web3,
        abi: Optional[list] = None,
    ):
        """
        初始化 ERC20 合约实例

        Args:
            contract_address: 合约地址
            provider: Web3实例
            abi: 可选的自定义 ABI
        """
        self.web3 = provider
        self.contract_address = Web3.to_checksum_address(contract_address)
        self.contract = self.web3.eth.contract(
            address=self.contract_address, abi=abi if abi else self.ABI
        )
        # 获取代币精度
        self.decimals = self.get_decimals()

    def get_decimals(self) -> int:
        """
        获取代币精度

        Returns:
            int: 代币精度 (例如: 18)
        """
        try:
            return self.contract.functions.decimals().call()
        except Exception:
            return 18  # 默认返回18，因为大多数ERC20代币��用18位精度

    def balance_of(self, address: str, formatted: bool = True) -> Decimal:
        """
        查询指定地址的代币余额

        Args:
            address: 要查询的钱包地址
            formatted: 是否返回格式化后的余额(考虑精度)

        Returns:
            Decimal: 代币余额
                    如果 formatted=True, 返回考虑精度后的实际余额
                    如果 formatted=False, 返回原始余额
        """
        address = Web3.to_checksum_address(address)
        balance = self.contract.functions.balanceOf(address).call()

        if formatted:
            return Decimal(balance) / Decimal(10**self.decimals)
        return Decimal(balance)

    def format_amount(self, amount: int) -> Decimal:
        """
        格式化代币金额(考虑精度)

        Args:
            amount: 原始金额

        Returns:
            Decimal: 格式化后的金额
        """
        return Decimal(amount) / Decimal(10**self.decimals)

    def parse_amount(self, amount: Decimal) -> int:
        """
        将实际金额转换为链上金额(考虑精度)

        Args:
            amount: 实际金额

        Returns:
            int: 链上金额
        """
        return int(Decimal(amount) * Decimal(10**self.decimals))

    # 批准指定地址的代币转账
    def approve(
        self, owner: str, spender: str, private_key: str, amount: int = 2**256 - 1
    ) -> bool:
        """
        批准指定地址的代币转账权限

        Args:
            owner: 授权方地址
            spender: 被授权的地址
            private_key: 发送者私钥
            amount: 授权金额，默认为最大值

        Returns:
            bool: 交易是否成功
        """
        spender = Web3.to_checksum_address(spender)
        owner = Web3.to_checksum_address(owner)

        # 构建基础交易参数
        tx_params: TxParams = {
            "from": owner,
            "nonce": self.web3.eth.get_transaction_count(owner),
        }

        # 获取合约方法并传入参数
        approve_function = self.contract.functions.approve(spender, amount)

        transaction = get_transaction(self.web3, tx_params, approve_function)
        if not transaction:
            return False

        return send_transaction(self.web3, transaction, private_key)

    def get_allowance(self, owner: str, spender: str) -> int:
        """
        获取授权额度

        Args:
            owner: 授权方地址
            spender: 被授权方地址

        Returns:
            int: 当前授权额度
        """
        owner = Web3.to_checksum_address(owner)
        spender = Web3.to_checksum_address(spender)
        return self.contract.functions.allowance(owner, spender).call()

    def approval(
        self, owner: str, spender: str, private_key: str, amount: int = MAX_INT
    ) -> bool:
        """
        智能授权函数 - 只在必要时才发起新的授权

        Args:
            spender: 被授权的地址
            private_key: 发送者私钥
            amount: 目标授权金额

        Returns:
            bool: 授权是否成功
        """
        spender = Web3.to_checksum_address(spender)
        owner = Web3.to_checksum_address(owner)

        # 检查当前授权额度
        current_allowance = self.get_allowance(owner, spender)

        # 如果当前授权额度已经足够，直接返回成功
        if current_allowance >= amount:
            logger.info(
                f"Current allowance {current_allowance} is sufficient for required amount {amount}"
            )
            return True

        # 如果授权额度不足，发起新的授权交易
        return self.approve(owner, spender, private_key, amount)
