from loguru import logger
from typing import Dict, Optional, Any
from web3 import Web3
from datetime import datetime
from fake_useragent import UserAgent
from ..utils.proxies import Proxies
from src.evm.wallet_manager import WalletManager

# ERC20代币标准ABI
ERC20_ABI = [
    {
        "constant": True,
        "inputs": [{"name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"name": "", "type": "uint256"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function",
    },
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function",
    },
]

nft_abi = [
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    }
]


class Web3Manager:
    """
    基础区块链交易管理类
    这个类只包含最基本的连接和交易功能
    """

    def __init__(self, max_gas_gwei: int = 30000, private_key: str = None):
        """
        初始化交易管理器

        Args:
            max_gas_gwei: 最大允许的gas价格（单位：gwei）
        """
        self.max_gas_gwei = max_gas_gwei
        self.web3 = None
        self.private_key = private_key
        self.account = None
        self.from_address = None

        if not private_key:
            return

        if not WalletManager().validate_private_key(private_key):
            logger.error(f"私钥格式不正确: {private_key}")
            raise ValueError("私钥格式不正确")

    def _get_request_kwargs(
        self,
        proxy: Optional[str] = None,
        user_agent: Optional[str] = None,
        timeout: int = 60,
    ) -> Dict[str, Any]:
        """
        设置请求配置

        Args:
            proxy: 代理地址
            user_agent: 自定义User-Agent
            timeout: 超时时间(秒)

        Returns:
            Dict: 请求配置参数
        """
        request_kwargs = {
            "timeout": timeout,
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": user_agent or UserAgent().chrome,
            },
        }

        if proxy:
            try:
                request_kwargs["proxies"] = Proxies(proxy).get_proxies()
            except Exception as e:
                logger.error(f"代理设置失败: {str(e)}")

        return request_kwargs

    def connect_to_network(
        self,
        rpc_url: str,
        proxy: Optional[str] = None,
        user_agent: Optional[str] = None,
        timeout: int = 30,
    ) -> Optional[Web3]:
        """
        连接到区块链网络

        Args:
            rpc_url: 区块链网络的RPC地址
            proxy: 代理设置，格式如 'http://127.0.0.1:7890'
            user_agent: 自定义User-Agent字符串
            timeout: 连接超时时间（秒）

        Returns:
            Web3: 如果连接成功返回Web3实例，失败返回None
        """
        try:
            request_kwargs = self._get_request_kwargs(proxy, user_agent, timeout)
            web3 = Web3(Web3.HTTPProvider(rpc_url, request_kwargs=request_kwargs))

            # 检查连接是否成功
            if web3.is_connected():
                self.web3 = web3
                if self.private_key:
                    self.account = web3.eth.account.from_key(self.private_key)
                    self.from_address = self.account.address
                return web3
            else:
                logger.error(f"无法连接到网络: {rpc_url}")
                return None

        except Exception as e:
            logger.error(f"连接网络时发生错误: {str(e)}")
            return None

    def check_balance(self, address: str) -> float:
        """
        检查账户余额

        Args:
            web3: Web3实例
            address: 要检查的钱包地址

        Returns:
            float: 账户余额(单位: ETH)
        """
        try:
            # 确保地址格式正确
            checksum_address = self.web3.to_checksum_address(address)

            # 获取余额（单位：Wei）
            balance_wei = self.web3.eth.get_balance(checksum_address)

            # 转换为ETH
            balance_eth = self.web3.from_wei(balance_wei, "ether")

            logger.info(f"地址 {address} 的余额为: {balance_eth} ETH")
            return float(balance_eth)

        except Exception as e:
            logger.error(f"检查余额时发生错误: {str(e)}")
            return 0.0

    def _get_raw_transaction(self, signed_txn) -> bytes:
        """
        从签名交易中获取raw transaction

        Args:
            signed_txn: 已签名的交易对象

        Returns:
            bytes: raw transaction数据
        """
        if hasattr(signed_txn, "rawTransaction"):
            return signed_txn.rawTransaction
        elif hasattr(signed_txn, "raw_transaction"):
            return signed_txn.raw_transaction
        return signed_txn.raw

    def _send_raw_transaction(self, transaction: Dict) -> str:
        # 签名交易
        try:
            signed_txn = self.web3.eth.account.sign_transaction(
                transaction, self.private_key
            )
            return self._get_raw_transaction(signed_txn)
        except Exception as e:
            logger.error(f"交易签名失败: {str(e)}")
            return None

    def _retry_send_transaction(
        self, transaction: Dict, max_retries: int = 3
    ) -> Dict[str, Any]:
        """
        带重试机制的发送交易

        Args:
            transaction: 交易参数字典
            max_retries: 最大重试次数

        Returns:
            Dict: 包含交易结果的字典
        """
        current_try = 0
        while current_try < max_retries:
            try:
                # 签名交易
                raw_tx = self._send_raw_transaction(transaction)
                if not raw_tx:
                    return {"status": "error", "message": "交易签名失败"}

                # 发送交易
                tx_hash = self.web3.eth.send_raw_transaction(raw_tx)
                logger.success(f"交易发送成功，交易哈希: {tx_hash.hex()}")
                return {"status": "success", "tx_hash": tx_hash}

            except Exception as e:
                error_msg = str(e)
                if "nonce too low" in error_msg.lower():
                    current_try += 1
                    if current_try >= max_retries:
                        raise e
                    # 增加nonce并重新签名交易
                    transaction["nonce"] += 1
                    logger.info(
                        f"Nonce too low, 重试第{current_try}次，新nonce: {transaction['nonce']}"
                    )
                else:
                    raise

    def send_transaction(
        self,
        to_address: str,
        value_in_wei: int,
        data: str = "",
        gas_limit: int = None,
        timeout: int = 120,
        priority_fee_gwei: int = None,
        transaction_nonce: int = None,
    ) -> Dict[str, Any]:
        """发送以太坊交易"""
        try:

            # 获取发送方账户
            account = self.web3.eth.account.from_key(self.private_key)
            from_address = account.address

            # 如果没有提供 nonce，获取当前 nonce
            if not transaction_nonce:
                transaction_nonce = self.web3.eth.get_transaction_count(
                    from_address, "pending"
                )

            gas_price = int(self.web3.eth.gas_price * 1.2)  # 提高20%

            # 准备交易参数
            transaction = {
                "from": from_address,
                "to": self.web3.to_checksum_address(to_address),
                "value": value_in_wei,
                "nonce": transaction_nonce,
                "gasPrice": gas_price,
                "chainId": self.web3.eth.chain_id,
            }

            # 如果有数据，添加到交易中
            if data:
                transaction["data"] = data

            # 预估gas
            if not gas_limit:
                try:
                    estimated_gas = self.web3.eth.estimate_gas(transaction)
                    gas_limit = int(estimated_gas * 1.3)  # 添加20%的缓冲
                    logger.success(
                        f"预估gas成功: {estimated_gas}, 设置gas限制为: {gas_limit}"
                    )
                except Exception as e:
                    logger.warning(f"预估gas失败: {str(e)}, 使用默认值300000")
                    gas_limit = 300000

            transaction["gas"] = gas_limit
            logger.info(f"准备发送的交易参数: {transaction}")

            # 发送交易
            result = self._retry_send_transaction(transaction)
            if result["status"] == "error":
                return result

            # 等待交易确认
            try:
                tx_hash = result["tx_hash"]
                tx_receipt = self.web3.eth.wait_for_transaction_receipt(
                    tx_hash, timeout=timeout
                )
            except Exception as e:
                logger.error(f"等待交易确认超时: {str(e)}")
                return {"status": "error", "message": f"等待交易确认超时: {str(e)}"}

            result = {
                "status": "success" if tx_receipt["status"] == 1 else "failed",
                "transaction_hash": tx_receipt["transactionHash"].hex(),
                "from": tx_receipt["from"],
                "to": tx_receipt["to"],
                "value_wei": value_in_wei,
                "value_eth": self.web3.from_wei(value_in_wei, "ether"),
                "gas_used": tx_receipt["gasUsed"],
                "gas_price": transaction["gasPrice"],
                "block_number": tx_receipt["blockNumber"],
            }

            return result

        except Exception as e:
            error_msg = str(e)
            logger.error(f"交易发送异常: {error_msg}")
            return {"status": "error", "message": f"交易发送失败: {error_msg}"}

    def adjust_gas_price(
        self,
        base_gas_price: int = None,
        multiplier: float = 1.1,
        max_gwei: int = 300,
    ) -> int:
        """
        调整gas价格，支持自定义倍数和最大值限制

        Args:
            base_gas_price: 基础gas价格（如果为None则使用当前网络gas价格）
            multiplier: gas价格倍数，默认1.1倍
            max_gwei: 最大允许的gas价格（单位：gwei），默认300 gwei

        Returns:
            int: 调整后的gas价格（单位：wei）
        """
        try:
            if not self.web3:
                logger.error("Web3未连接")
                return 0

            # 如果没有提供基础gas价格，则获取当前网络gas价格
            if not base_gas_price:
                base_gas_price = self.web3.eth.gas_price

            # 计算调整后的gas价格
            adjusted_price = int(base_gas_price * multiplier)

            # 将最大gwei限制转换为wei
            max_price_wei = self.web3.to_wei(max_gwei, "gwei")

            # 确保不超过最大限制
            final_price = min(adjusted_price, max_price_wei)

            logger.info(
                f"Gas价格调整: 基础价格 {self.web3.from_wei(base_gas_price, 'gwei')} gwei, "
                f"调整后 {self.web3.from_wei(final_price, 'gwei')} gwei"
            )
            return final_price

        except Exception as e:
            logger.error(f"调整gas价格时发生错误: {str(e)}")
            return base_gas_price

    def get_detailed_network_info(self) -> Dict:
        """
        获取更详细的网络信息

        Returns:
            Dict: 包含详细网络信息的字典
        """
        try:
            latest_block = self.web3.eth.get_block("latest")
            info = {
                "chain_id": self.web3.eth.chain_id,
                "gas_price": self.web3.from_wei(self.web3.eth.gas_price, "gwei"),
                "block_number": self.web3.eth.block_number,
                "latest_block_hash": latest_block["hash"].hex(),
                "latest_block_timestamp": datetime.fromtimestamp(
                    latest_block["timestamp"]
                ),
                "network_status": (
                    "connected" if self.web3.is_connected() else "disconnected"
                ),
                "peer_count": (
                    self.web3.net.peer_count
                    if hasattr(self.web3.net, "peer_count")
                    else None
                ),
            }
            return info
        except Exception as e:
            logger.error(f"获取详细网络信息时发生错误: {str(e)}")
            return {}

    def get_transaction_details(self, web3: Web3, tx_hash: str) -> Dict:
        """
        获取交易详情

        Args:
            web3: Web3实例
            tx_hash: 交易哈希

        Returns:
            Dict: 交易详情
        """
        try:
            # 获取交易信息
            tx = web3.eth.get_transaction(tx_hash)
            # 获取交易收据
            receipt = web3.eth.get_transaction_receipt(tx_hash)

            details = {
                "hash": tx_hash,
                "from": tx["from"],
                "to": tx["to"],
                "value_wei": tx["value"],  # 保持原始的 Wei 值
                "value_eth": web3.from_wei(tx["value"], "ether"),  # ETH 值用于显示
                "gas_price": web3.from_wei(tx["gasPrice"], "gwei"),
                "gas_used": receipt["gasUsed"],
                "total_cost_gwei": web3.from_wei(
                    tx["gasPrice"] * receipt["gasUsed"], "gwei"
                ),
                "block_number": receipt["blockNumber"],
                "status": "成功" if receipt["status"] == 1 else "失败",
            }

            return details

        except Exception as e:
            logger.error(f"获取交易详情失败: {str(e)}")
            return {}

    def check_token_balance(
        self, token_address: str, wallet_address: str
    ) -> Dict[str, Any]:
        """查询ERC20代币余额"""
        try:
            # 先检查合约是否存在
            if not self.is_contract_exists(token_address):
                logger.error(f"合约 {token_address} 不存在")
                return {
                    "raw_balance": 0,
                    "balance": 0,
                    "decimals": 18,
                    "symbol": "UNKNOWN",
                }

            # 检查网络连接
            if not self.web3.is_connected():
                logger.error("网络未连接")
                return {
                    "raw_balance": 0,
                    "balance": 0,
                    "decimals": 18,
                    "symbol": "UNKNOWN",
                }

            # 创建合约实例
            token_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(token_address), abi=ERC20_ABI
            )

            try:
                checksum_wallet = self.web3.to_checksum_address(wallet_address)
                raw_balance = token_contract.functions.balanceOf(checksum_wallet).call()

                # 获取代币信息
                try:
                    decimals = token_contract.functions.decimals().call()
                    symbol = token_contract.functions.symbol().call()
                except Exception:
                    decimals = 18
                    symbol = "UNKNOWN"

                # 计算实际余额
                actual_balance = raw_balance / (10**decimals)

                return {
                    "raw_balance": raw_balance,
                    "balance": actual_balance,
                    "decimals": decimals,
                    "symbol": symbol,
                }

            except Exception as e:
                logger.error(f"查询代币余额失败: {str(e)}")
                return {
                    "raw_balance": 0,
                    "balance": 0,
                    "decimals": 18,
                    "symbol": "UNKNOWN",
                }

        except Exception as e:
            logger.error(f"查询代币余额失败: {str(e)}")
            return {"raw_balance": 0, "balance": 0, "decimals": 18, "symbol": "UNKNOWN"}

    def get_nft_balance(self, nft_contract_address: str, wallet_address: str) -> bool:
        """获取NFT余额"""
        try:
            # 对两个地址都进行checksum转换
            checksum_contract = self.web3.to_checksum_address(nft_contract_address)
            checksum_wallet = self.web3.to_checksum_address(wallet_address)

            # 构建合约实例
            contract = self.web3.eth.contract(address=checksum_contract, abi=nft_abi)

            # 使用转换后的钱包地址调用balanceOf
            balance = contract.functions.balanceOf(checksum_wallet).call()

            has_nft = balance > 0
            logger.info(
                f"地址 {wallet_address} NFT余额: {balance}, 是否持有: {has_nft}"
            )
            return has_nft

        except Exception as e:
            logger.error(f"获取NFT余额失败: {str(e)}")
            return False

    def is_contract_exists(self, contract_address: str) -> bool:
        """检查合约是否存在"""
        try:
            code = self.web3.eth.get_code(
                self.web3.to_checksum_address(contract_address)
            )
            return len(code) > 2  # '0x' 表示合约不存在
        except Exception as e:
            logger.error(f"检查合约失败: {str(e)}")
            return False

    def allowance(
        self, token_address: str, owner_address: str, spender_address: str
    ) -> int:
        """
        查询ERC20代币的授权额度

        Args:
            token_address: 代币合约地址
            owner_address: 持币地址
            spender_address: 被授权地址（通常是平台/合约地址）

        Returns:
            int: 授权额度
        """
        try:
            # 地址转换为checksum格式
            token = self.web3.to_checksum_address(token_address)
            owner = self.web3.to_checksum_address(owner_address)
            spender = self.web3.to_checksum_address(spender_address)

            # 创建合约实例
            abi = '[{"constant":true,"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"isAcceptedToken","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"}]'
            contract = self.web3.eth.contract(address=token, abi=abi)

            # 查询授权额度
            amount = contract.functions.allowance(owner, spender).call()
            return amount

        except Exception as e:
            logger.error(f"查询授权额度失败: {str(e)}")
            return 0

    def approve(self, token_address: str, spender_address: str, amount: int) -> bool:
        """
        构建approve函数调用

        Args:
            token_address: 代币合约地址
            owner_address: 持币地址
            spender_address: 被授权地址
            amount: 授权数量

        Returns:
            bool: approve函数的返回值
        """
        try:
            # 地址转换为checksum格式
            token = self.web3.to_checksum_address(token_address)
            spender = self.web3.to_checksum_address(spender_address)

            # approve函数的ABI
            abi = """[{
                "constant": false,
                "inputs": [
                    {"name": "spender", "type": "address"},
                    {"name": "amount", "type": "uint256"}
                ],
                "name": "approve",
                "outputs": [{"name": "", "type": "bool"}],
                "payable": false,
                "stateMutability": "nonpayable",
                "type": "function"
            }]"""

            # 创建合约实例
            contract = self.web3.eth.contract(address=token, abi=abi)

            # 构建approve调用数据
            txn = contract.functions.approve(spender, amount).build_transaction(
                {
                    "from": self.from_address,
                    "nonce": self.get_nonce(),
                }
            )

            # 使用已有的send_transaction方法发送交易
            result = self._retry_send_transaction(txn)
            if result["status"] == "success":
                logger.info(f"授权成功: {amount} 代币已授权给 {spender_address}")
                return True
            else:
                logger.error(f"授权失败: {result.get('message', '未知错误')}")
                return False

        except Exception as e:
            error_msg = str(e)
            logger.error(f"授权失败: {error_msg}")
            raise ValueError(f"授权失败: {error_msg}")

    def get_nonce(self) -> int:
        """获取nonce"""
        return self.web3.eth.get_transaction_count(self.from_address, "pending")
