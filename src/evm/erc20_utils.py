from typing import Optional, Dict, Any
from loguru import logger
from web3.types import TxParams
from web3.contract.contract import ContractFunction
from web3 import Web3
import requests
from urllib3.contrib.socks import SOCKSProxyManager
from requests.sessions import Session
from decimal import Decimal, ROUND_DOWN
from fake_useragent import UserAgent
from src.utils.proxies import Proxies


# 专门处理gas评估并返回构建好的transaction,参数为TxParams, contract_function, 返回构建好的transaction或者None
def get_transaction(
    provider: Web3,
    tx_params: TxParams,
    contract_function: ContractFunction,
    gas_adjustment_factor: float = 1.0,
) -> Optional[TxParams]:

    if "gas" not in tx_params:
        # 估算gas并应用调整因子
        estimated_gas = contract_function.estimate_gas(tx_params)
        tx_params["gas"] = int(estimated_gas * gas_adjustment_factor)

    # 如果tx_params未指定maxFeePerGas或者gasPrice，检查是否支持 EIP-1559
    if "maxFeePerGas" not in tx_params and "gasPrice" not in tx_params:
        try:
            latest_block = provider.eth.get_block("latest")
            if "baseFeePerGas" in latest_block:
                # EIP-1559 定价
                max_priority_fee = provider.eth.max_priority_fee
                base_fee = int(latest_block["baseFeePerGas"])
                # 应用gas调整因子到maxFeePerGas
                max_fee_per_gas = int(
                    (2 * base_fee + max_priority_fee) * gas_adjustment_factor
                )

                tx_params["maxFeePerGas"] = max_fee_per_gas
                tx_params["maxPriorityFeePerGas"] = max_priority_fee
                logger.info(
                    f"使用 EIP-1559 gas 定价: maxFeePerGas={max_fee_per_gas}, maxPriorityFeePerGas={max_priority_fee}"
                )
            else:
                # 传统 gas 定价，应用gas调整因子
                tx_params["gasPrice"] = int(
                    provider.eth.gas_price * gas_adjustment_factor
                )
                logger.info(f"使用传统 gas 定价: gasPrice={tx_params['gasPrice']}")
        except Exception as e:
            # 如果获取 EIP-1559 参数失败，回退传统定价
            logger.warning(f"获取 EIP-1559 参数失败，使用传统定价: {str(e)}")
            tx_params["gasPrice"] = int(provider.eth.gas_price * gas_adjustment_factor)

    try:
        # 构建合约交易
        transaction = contract_function.build_transaction(tx_params)
        return transaction
    except Exception as e:
        logger.error(f"构建合约交易失败: {str(e)}")
        return None


# 发送交易
def send_transaction(provider: Web3, transaction: TxParams, private_key: str) -> bool:
    try:
        # 签名并发送交易
        signed_tx = provider.eth.account.sign_transaction(
            transaction, private_key=private_key
        )
        tx_hash = provider.eth.send_raw_transaction(signed_tx.raw_transaction)

        # 等待交易被确认
        tx_receipt = provider.eth.wait_for_transaction_receipt(tx_hash)

        # 记录交易信息
        logger.info(f"Tx Transaction Hash: {tx_hash.hex()}")
        logger.info(f"Status: {'Success' if tx_receipt['status'] else 'Failed'}")
        logger.info(f"Gas Used: {tx_receipt['gasUsed']}")
        logger.info(f"Block Number: {tx_receipt['blockNumber']}")

        return bool(tx_receipt["status"])
    except Exception as e:
        logger.error(f"Send transaction failed: {str(e)}")
        return False


# 构建包含data的raw transaction
def get_raw_transaction(
    provider: Web3, tx_params: TxParams, gas_adjustment_factor: float = 1.0
) -> Optional[TxParams]:
    # 如果tx_params未指定data和to，则返回None
    if "data" not in tx_params or "to" not in tx_params or "from" not in tx_params:
        logger.error(f"TxParams 中缺少 data 或 to 或 from 参数")
        return None

    try:
        # 如果tx_params未指定gas，则使用estimate_gas
        if "gas" not in tx_params:
            estimated_gas = provider.eth.estimate_gas(tx_params)
            tx_params["gas"] = int(estimated_gas * gas_adjustment_factor)

        # 如果tx_params未指定maxFeePerGas或者gasPrice，检查是否支持 EIP-1559
        if "maxFeePerGas" not in tx_params and "gasPrice" not in tx_params:
            try:
                latest_block = provider.eth.get_block("latest")
                if "baseFeePerGas" in latest_block:
                    # EIP-1559 定价
                    max_priority_fee = provider.eth.max_priority_fee
                    base_fee = int(latest_block["baseFeePerGas"])
                    # 应用gas调整因子到maxFeePerGas
                    max_fee_per_gas = int(
                        (2 * base_fee + max_priority_fee) * gas_adjustment_factor
                    )

                    tx_params["maxFeePerGas"] = max_fee_per_gas
                    tx_params["maxPriorityFeePerGas"] = max_priority_fee
                    logger.info(
                        f"使用 EIP-1559 gas 定价: maxFeePerGas={max_fee_per_gas}, maxPriorityFeePerGas={max_priority_fee}"
                    )
                else:
                    # 传统 gas 定价，应用gas调整因子
                    tx_params["gasPrice"] = int(
                        provider.eth.gas_price * gas_adjustment_factor
                    )
                    logger.info(f"使用传统 gas 定价: gasPrice={tx_params['gasPrice']}")
            except Exception as e:
                # 如果获取 EIP-1559 参数失败，回退传统定价
                logger.warning(f"获取 EIP-1559 参数失败，使用传统定价: {str(e)}")
                tx_params["gasPrice"] = int(
                    provider.eth.gas_price * gas_adjustment_factor
                )

        # 确保nonce存在
        if "nonce" not in tx_params:
            tx_params["nonce"] = provider.eth.get_transaction_count(tx_params["from"])

        # 确保chainId存在
        if "chainId" not in tx_params:
            tx_params["chainId"] = provider.eth.chain_id

        # 确保value存在
        if "value" not in tx_params:
            tx_params["value"] = 0

        return tx_params

    except Exception as e:
        logger.error(f"构建原始交易失败: {str(e)}")
        return None


# 获取web3实例
def get_web3(
    provider_url: str,
    proxy: Optional[str] = None,
    user_agent: Optional[str] = None,
    timeout: int = 60,
) -> Web3:

    if proxy:
        # 如果 proxy 是字符串，转换为字典格式
        if isinstance(proxy, str):
            # 尝试解析代理地址
            try:
                if ":" not in proxy:
                    raise ValueError("代理地址格式错误，需要包含端口号")

                # 如果已经包含协议前缀，直接使用
                if proxy.startswith(("http://", "https://", "socks5h://")):
                    proxy_str = proxy
                elif proxy.startswith(("socks5://")):
                    proxy_str = f"http://{proxy.split(':')[-2]}:{proxy.split(':')[-1]}"
                else:
                    proxy_str = f"http://{proxy}"

                proxy = proxy_str
            except Exception as e:
                logger.error(f"代理地址解析失败: {str(e)}")
                raise

    request_kwargs = _get_request_kwargs(
        proxy=proxy, user_agent=user_agent, timeout=timeout
    )

    web3 = Web3(Web3.HTTPProvider(provider_url, request_kwargs=request_kwargs))

    # 检查连接是否成功
    if web3 and web3.is_connected():
        return web3
    else:
        logger.error(f"无法连接到网络: {provider_url}")
        return None


def _get_request_kwargs(
    proxy: Optional[str] = None,
    user_agent: Optional[str] = None,
    timeout: int = 60,
) -> Dict[str, Any]:
    """
    设置请求配置

    Args:
        proxy: 代理地址
        user_agent: 自定义User-Agent
        timeout: 超时时间(秒)

    Returns:
        Dict: 请求配置参数
    """
    request_kwargs = {
        "timeout": timeout,
        "headers": {
            "Content-Type": "application/json",
            "User-Agent": user_agent or UserAgent().chrome,
        },
    }

    if proxy:
        try:
            request_kwargs["proxies"] = Proxies(proxy).get_proxies()
        except Exception as e:
            logger.error(f"代理设置失败: {str(e)}")

    return request_kwargs


def get_web3_with_proxy(
    provider_url: str,
    proxy: Optional[str] = None,
    user_agent: Optional[str] = None,
    timeout: int = 60,
) -> Web3:

    request_kwargs = _get_request_kwargs(
        proxy=proxy, user_agent=user_agent, timeout=timeout
    )

    web3 = Web3(Web3.HTTPProvider(provider_url, request_kwargs=request_kwargs))

    # 检查连接是否成功
    if web3.is_connected():
        return web3
    else:
        logger.error(f"无法连接到网络: {provider_url}")
        return None


# 格式化token数量(BigNumber)，保留5位小数，不四舍五入
def formatTokenAmount(amount: int, remain_decimals: int, decimals: int = 18) -> str:
    """
    格式化代币数量，保留指定位数小数，不四舍五入

    Args:
        amount: 原始金额（整数）
        remain_decimals: 保留的小数位数
        decimals: 代币精度，默认18

    Returns:
        str: 格式化后的金额字符串
    """
    try:
        # 将整数转换为 Decimal
        amount_decimal = Decimal(amount) / Decimal(10**decimals)

        # 创建一个表示精度的字符串，例如 "0.001" 表示3位小数
        precision = Decimal("0." + "0" * remain_decimals)

        # 使用 quantize 进行截断（不四舍五入）
        formatted = amount_decimal.quantize(precision, rounding=ROUND_DOWN)

        return str(formatted)
    except Exception as e:
        logger.error(f"Format error: {str(e)}")
        return None
