import secrets
from eth_account import Account
from web3.auto import w3
from eth_account.messages import (
    encode_defunct,
    encode_typed_data,
)
from eth_account.hdaccount import generate_mnemonic
from loguru import logger
from typing import Dict, Optional
from web3 import Web3


class WalletManager:
    """
    钱包管理类
    用于处理钱包的生成、导入等操作
    """

    def __init__(self):
        """初始化钱包管理器"""
        # 启用助记词功能
        Account.enable_unaudited_hdwallet_features()

    def generate_wallet_from_mnemonic(
        self, mnemonic: str = None, password: str = None, account: int = 0
    ) -> Dict[str, str]:
        """
        从助记词生成钱包（私钥和地址）

        Args:
            mnemonic: 可选，已有的助记词。如果不提供则生成新的
            password: 可选，助记词密码
            account: 可选，账户索引，默认0

        Returns:
            Dict: 包含助记词、私钥、地址和路径的字典

        Raises:
            ValueError: 生成钱包失败时抛出
        """
        try:
            # 如果没有提供助记词，生成新的
            if not mnemonic:
                mnemonic = generate_mnemonic(num_words=24, lang="english")
                logger.info("生成新的助记词")
            else:
                # 清理助记词格式（移除多余空格）
                mnemonic = " ".join(mnemonic.strip().split())

            # 使用 Account.from_mnemonic 生成账户
            account = Account.from_mnemonic(
                mnemonic=mnemonic,
                passphrase=password if password else "",
                account_path=f"m/44'/60'/{account}'/0/0",
            )

            result = {
                "mnemonic": mnemonic,
                "private_key": account.key.hex(),
                "address": account.address,
                "path": f"m/44'/60'/{account}'/0/0",
            }

            logger.info(f"从助记词生成钱包成功: {result}")
            return result

        except Exception as e:
            logger.error(f"生成钱包失败: {str(e)}")
            raise ValueError(f"生成钱包失败: {str(e)}")

    def generate_random_wallet(self) -> Dict[str, str]:
        """
        生成随机钱包

        Returns:
            Dict: 包含私钥和地址的字典

        Raises:
            ValueError: 生成钱包失败时抛出
        """
        try:
            # 生成随机私钥
            private_key = "0x" + secrets.token_hex(32)
            account = Account.from_key(private_key)

            result = {"private_key": private_key, "address": account.address}

            logger.info(f"生成随机钱包成功: {result['address']}")
            return result

        except Exception as e:
            logger.error(f"生成随机钱包失败: {str(e)}")
            raise ValueError(f"生成随机钱包失败: {str(e)}")

    def import_wallet_from_private_key(self, private_key: str) -> Dict[str, str]:
        """
        从私钥导入钱包

        Args:
            private_key: 私钥字符串

        Returns:
            Dict: 包含私钥和地址的字典

        Raises:
            ValueError: 导入钱包失败时抛出
        """
        try:
            # 验证私钥格式
            if not self.validate_private_key(private_key):
                raise ValueError("私钥格式不正确")

            account = Account.from_key(private_key)
            result = {
                "private_key": private_key,
                "address": account.address,
            }

            logger.info(f"从私钥导入钱包成功: {result['address']}")
            return result

        except Exception as e:
            logger.error(f"导入钱包失败: {str(e)}")
            raise ValueError(f"导入钱包失败: {str(e)}")

    def validate_private_key(self, private_key: str) -> bool:
        """
        验证私钥格式

        Args:
            private_key: 私钥字符串

        Returns:
            bool: 是否是有效的私钥格式
        """
        try:
            # 移除0x前缀后应该是64位十六进制
            key = private_key.replace("0x", "")
            if len(key) != 64:
                return False
            # 验证是否是有效的十六进制
            int(key, 16)
            return True
        except ValueError:
            return False

    def validate_address(self, address: str) -> bool:
        """
        验证地址格式是否正确

        Args:
            address: 以太坊地址

        Returns:
            bool: 地址是否有效
        """
        try:
            # 创建临时Web3实例来验证地址
            web3 = Web3()
            web3.to_checksum_address(address)
            return True
        except:
            return False


class WalletSign(object):
    """
    钱包签名
    """

    def __init__(self, privateKey):
        # 钱包的私钥
        self.wallet_key = privateKey

    def sign_message(self, msg):
        message = encode_defunct(text=msg)
        signed_message = w3.eth.account.sign_message(
            message, private_key=self.wallet_key
        )
        return signed_message.signature.hex()

    def sign_hex(self, hex_str):
        message = encode_defunct(hexstr=hex_str)
        signed_message = w3.eth.account.sign_message(
            message, private_key=self.wallet_key
        )
        return signed_message.signature.hex()

    def sign_message_v4(self, full_msg):
        data = encode_typed_data(full_message=full_msg)
        signed_message = w3.eth.account.sign_message(data, private_key=self.wallet_key)
        return signed_message.signature.hex()

    def verify_sign_address(self, message, signature):
        return Account.recover_message(
            encode_defunct(text=message), signature=signature
        )

    def verify_message(self, address, signature, message):
        return Web3.to_checksum_address(
            Account.recover_message(encode_defunct(text=message), signature=signature)
        ) == Web3.to_checksum_address(address)


# # 创建钱包管理器
# wallet_manager = WalletManager()

# # 生成随机钱包
# wallet = wallet_manager.generate_random_wallet()

# # 从助记词生成钱包
# wallet_with_mnemonic = wallet_manager.generate_wallet_from_mnemonic()

# # 从私钥导入钱包
# imported_wallet = wallet_manager.import_wallet_from_private_key("your_private_key")

# # 验证地址
# is_valid = wallet_manager.validate_address("0x...")
