from time import sleep

from loguru import logger


def try_click(tab, xpath, timeout=5, max_attempts=5, id=None):
    """尝试点击元素，包含重试机制.

    Args:
        tab: 浏览器标签页对象
        xpath: 元素的xpath
        timeout: 每次尝试的超时时间
        max_attempts: 最大尝试次数
        id: 浏览器标识（用于日志）
    Returns:
        bool: 是否点击成功
    """
    for attempt in range(max_attempts):
        try:
            button = tab.ele(xpath, timeout=timeout)
            if button.states.is_clickable:
                button.click()
                return True
            else:
                if attempt < max_attempts - 1:
                    sleep(3)
                    continue
        except Exception as e:
            if attempt < max_attempts - 1:
                sleep(3)
                continue
            else:
                logger.error(f"{id} 多次尝试后仍未能点击元素: {xpath}")
                logger.error(f"错误信息: {str(e)}")
    return False


def input_text(tab, xpath, text, timeout=5):
    """输入文本的通用方法."""
    try:
        element = tab.ele(xpath, timeout=timeout)
        element.input(text)
        return True
    except Exception as e:
        logger.error(f"输入文本失败: {str(e)}")
        return False
