from DrissionPage import Chromium
from loguru import logger
from typing import List, Dict, Optional
import os

from .base_browser import BaseBrowser, BrowserType
from src.fingerprints.brave import get_page_with_browser_id
from src.utils import get_project_root_path
from src.controllers.exceptions import BrowserControllerError


# 普通 Brave 浏览器
class BraveBrowser(BaseBrowser):

    def __init__(self, id: str, browser_id: str, browser_config: dict):
        super().__init__(id, browser_id, browser_config)

    def set_extensions(self, extensions: List[Dict[str, str]]):
        """设置浏览器插件(会覆盖默认插件)

        Args:
            extensions: 插件配置列表，每个插件需包含以下格式的字典:
                [
                    {
                        "path": "/path/to/extension1",  # 插件文件路径（必需）
                        "id": "extension_id"          # 插件（可选）
                    },
                    ...
                ]

        Example:
            browser.    ([
                {
                    "path": "/extensions/metamask.crx",
                    "id": "metamask"
                },
                {
                    "path": "/extensions/ublock.crx",
                    "id": "ublock"
                }
            ])

        Raises:
            BrowserControllerError: 当浏览器配置为空时抛出
        """
        if not self.browser_config:
            raise BrowserControllerError(f"ID {self.id} 浏览器配置为空")

        self.browser_config["extensions"] = extensions

    def add_extension(self, extension: Dict[str, str]):
        """添加单个浏览器插件

        Args:
            extension: 插件配置字典，需包含以下格式:
                {
                    "path": "/path/to/extension",  # 插件文件路径（必需）
                    "id": "extension_id"           # 插件ID（可选）
                }

        Example:
            browser.add_extension({
                "path": "/extensions/metamask.crx",
                "id": "metamask"
            })

        Raises:
            BrowserControllerError: 当浏览器配置为空时抛出
        """
        if not self.browser_config:
            raise BrowserControllerError(f"ID {self.id} 浏览器配置为空")

        current_extensions = self.browser_config.get("extensions", [])
        if not current_extensions:  # 检查是否为空或为 ''
            current_extensions = []  # 确保是一个空列表
        current_extensions.extend([extension])
        self.browser_config["extensions"] = current_extensions
        logger.info(f"{self.id} 成功添加插件: {extension.get('id', extension['path'])}")

    def get_chromium_page(self) -> Optional[Chromium]:
        """获取浏览器页面实例"""
        try:
            if not self.browser_config:
                logger.warning(f"id [{self.id}] 获取对应的钱包信息为空, 请检查配置项")
                return

            self.page = get_page_with_browser_id(self.id, self.browser_config)
            return self.page
        except Exception as e:
            logger.error(f"{self.id} 获取浏览器页面实例失败: {str(e)}")
            return None

    def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                self.page.quit()
        except Exception as e:
            logger.error(f"{self.id} 关闭浏览器失败: {str(e)}")

    def wallet_config_path(self) -> str:
        """钱包配置路径"""
        return os.path.join(get_project_root_path(), "data/Brave.csv")

    @property
    def browser_type(self) -> BrowserType:
        return BrowserType.BRAVE
