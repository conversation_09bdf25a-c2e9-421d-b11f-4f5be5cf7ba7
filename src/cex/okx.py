import json
import os
import random
from loguru import logger
import okx.Funding as Funding


class OKX:
    def __init__(self) -> None:
        datas = self.read_all_data()
        api_key = datas.get("api_key")
        secret = datas.get("secret")
        passphrase = datas.get("password")
        flag = "0"
        self.funding_api = Funding.FundingAPI(api_key, secret, passphrase, False, flag)

    def config_path(self):
        here = os.path.abspath(os.path.dirname(__file__))
        json_path = os.path.join(here, "./config.json")
        return json_path

    def read_all_data(self):
        json_path = self.config_path()
        with open(json_path, "r") as f:
            datas = json.load(f)
            return datas

    def get_fee(self, token, network):
        currencies = self.funding_api.get_currencies(ccy=token)
        if not currencies:
            logger.error("获取费用失败，请检查你的网络...")
            return

        data = currencies.get("data")
        chain = f"{token}-{network}"
        for item in data:
            _chain = item.get("chain")
            # 判断是否可提现
            canWd = item.get("canWd")
            if chain == _chain:
                if not canWd:
                    logger.error(f"您选择的{_chain}目前不可提现...")
                    continue

                return item.get("minFee")

        return None

    def get_balance(self, token):
        result = self.funding_api.get_balances(token)
        if result.get("code") != "0":
            logger.error(result.get("msg"))
            return 0
        data = result.get("data")
        if not data:
            logger.error("数据获取异常")
            return 0

        for item in data:
            ccy = item.get("ccy")
            if ccy == token:
                return item.get("availBal")

        return 0

    def withdraw_eth_base(self, address, amount):
        token = "ETH"
        network = "Base"
        self.withdraw(address, token, network, amount)

    def withdraw_eth_linea(self, address, amount):
        token = "ETH"
        network = "Linea"
        self.withdraw(address, token, network, amount)

    def withdraw_eth_zks(self, address, amount):
        token = "ETH"
        network = "zkSync Era"
        self.withdraw(address, token, network, amount)

    def withdraw_pol_polygon(self, address, amount):
        token = "POL"
        network = "Polygon"
        self.withdraw(address, token, network, amount)

    def withdraw_eth(self, address, amount):
        token = "ETH"
        network = "ERC20"
        self.withdraw(address, token, network, amount)

    def withdraw_galxe_g(self, address, amount):
        token = "G"
        network = "Gravity Alpha Mainnet"
        self.withdraw(address, token, network, amount)

    def withdraw(self, address, token, network, amount):
        chain = f"{token}-{network}"
        try:

            balance = self.get_balance(token)
            if float(balance) < float(amount):
                logger.warning(f"{token} 余额不足, 请检余额!")
                return False

            fee = self.get_fee(token, network)
            logger.info(f"提现gas费:{fee}")
            # dest 提币方式 3：内部转账 4：链上提币
            result = self.funding_api.withdrawal(
                ccy=token, amt=amount, dest=4, toAddr=address, chain=chain
            )
            code = result.get("code")
            if int(code) == 0:
                logger.success(
                    f"【{chain}】 Transfer {amount} {token} to {address} succeeded"
                )
                return True

            msg = result.get("msg")
            logger.error(
                f"【{chain}】 Transfer {amount} {token} to {address} failed, error : {msg}"
            )

        except Exception as e:
            logger.error(f"【{chain}】Transfer {amount} ETH to {address} failed: {e}")

    # 随机从不同的链提现
    def withdraw_eth_random(
            self, address, amount, chains=["Arbitrum One", "Optimism", "Base"]
    ):
        token = "ETH"
        chain = random.choice(chains)
        self.withdraw(address, token, chain, amount)

    # 插件相关功能


def get_amount(start, end, decimal_places=2):
    """
    生成指定范围内的随机数，并保留指定位数的小数

    Args:
        start (float): 最小值
        end (float): 最大值
        decimal_places (int): 要保留的小数位数，默认为2

    Returns:
        float: 保留指定小数位数的随机数
    """
    amount = round(random.uniform(start, end), decimal_places)
    return amount


if __name__ == "__main__":
    pass
