from abc import ABC, abstractmethod

from typing import Optional
from src.enums.wallet_enums import WalletType
from src.browsers.config import get_browser_extension_id
from DrissionPage import Chromium


class BaseWallet(ABC):

    def __init__(self, browser):
        self.browser = browser
        self.id: str = self.browser.id
        self._page: Optional[Chromium] = None

    @abstractmethod
    def login(self, password: str) -> bool:
        pass

    @abstractmethod
    def setup(self, password: str, phrase: str) -> bool:
        pass

    @abstractmethod
    def sign(self):
        pass

    @abstractmethod
    def connect(self):
        pass

    @property
    @abstractmethod
    def wallet_type(self) -> WalletType:
        pass

    @property
    @abstractmethod
    def home_url(self) -> str:
        pass

    @property
    def extension_id(self) -> str:
        return get_browser_extension_id(
            self.wallet_type.value, self.browser.browser_type.value
        )

    @property
    def page(self) -> Optional[Chromium]:
        """获取当前浏览器页面"""
        if self._page is None:
            self._page = self.browser.page
        return self._page
