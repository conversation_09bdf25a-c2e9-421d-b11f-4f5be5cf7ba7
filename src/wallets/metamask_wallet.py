from time import sleep

from loguru import logger
from .base_wallet import BaseWallet, WalletType
from src.browsers.base_browser import BaseBrowser
from src.browsers.operations import try_click
from src.utils import get_element, get_elements
from retry import retry

def check_metamask_page(func):
    """检查是否为 MetaMask 钱包页面的装饰器"""

    def wrapper(self, *args, **kwargs):
        for _ in range(15):
            tab = self.page.latest_tab
            if "MetaMask" in tab.title:
                return func(self, *args, **kwargs)
            sleep(1)
        logger.warning(f"{self.id} 未唤起钱包页面...")
        return False

    return wrapper

class MetaMaskWallet(BaseWallet):

    def __init__(self, browser: BaseBrowser):
        super().__init__(browser=browser)

        self.import_url = f"{self.home_url}#onboarding/import-with-recovery-phrase"

    @property
    def wallet_type(self) -> WalletType:
        return WalletType.METAMASK

    @property
    def home_url(self) -> str:
        return f"chrome-extension://{self.extension_id}/home.html"

    @retry(tries=3, delay=1)
    def login(self, password: str) -> bool:
        last_tab = self.page.new_tab(self.home_url)
        sleep(3)
        if get_element(
            last_tab, "x://span[@data-testid='account-value-and-suffix']", timeout=3
        ):
            logger.success(f"{self.id} 钱包已经登录")
            last_tab.close()
            return True

        pwd_input = get_element(last_tab, "x://input[@id='password']", timeout=6)
        if not pwd_input:
            raise Exception(f"{self.id} Metamask加载失败")

        pwd_input.input(f"{password}\n")
        sleep(2)

        if get_element(
            last_tab, "x://span[@data-testid='account-value-and-suffix']", timeout=3
        ):
            logger.success(f"{self.id} 钱包登录成功")
            last_tab.close()
            return True

        return False

    def setup(self, password: str, phrase: str) -> bool:
        last_tab = self.page.new_tab(self.home_url)

        if get_element(
            last_tab, "x://span[@data-testid='account-value-and-suffix']", timeout=5
        ):
            logger.success(f"{self.id} 钱包已经导入")
            return True

        if input_ele := get_element(last_tab, "x://input[@id='password']", timeout=10):
            logger.success(f"{self.id} 钱包已经导入")
            input_ele.input(f"{password}\n")
            sleep(2)

            return True

        get_element(
            last_tab, "x://input[@data-testid='onboarding-terms-checkbox']", 2
        ).click()

        sleep(1)
        get_element(
            last_tab, "x://button[@data-testid='onboarding-import-wallet']", 2
        ).click()

        sleep(2)
        get_element(last_tab, "x://input[@type='checkbox']", 2).click()

        sleep(1)
        get_element(
            last_tab, "x://button[@data-testid='metametrics-i-agree']", 2
        ).click()

        phrases = phrase.split(" ")
        word_len = len(phrases)

        # 如果是24个长度助记词，切换成24
        if word_len == 24:
            ele = last_tab(
                "x://div[@class='dropdown import-srp__number-of-words-dropdown']//select"
            )
            ele.select.by_value("24")

        inputs = last_tab.eles(
            "x://input[@class='MuiInputBase-input MuiInput-input']", timeout=5
        )
        for index, e in enumerate(inputs):
            e.input(phrases[index])
            sleep(0.2)
        sleep(1)

        get_element(
            last_tab, "x://button[@data-testid='import-srp-confirm']", 2
        ).click()
        sleep(1)

        eles = get_elements(last_tab, ".form-field__input", 3)
        for ele in eles:
            ele.input(password)
            sleep(0.2)

        get_element(
            last_tab, "x://input[@data-testid='create-password-terms']", 2
        ).click()

        sleep(1)
        get_element(
            last_tab, "x://button[@data-testid='create-password-import']", 2
        ).click()

        # 点击完成
        last_tab.wait.ele_displayed(
            "x://button[@data-testid='onboarding-complete-done']", timeout=10
        )
        get_element(
            last_tab, "x://button[@data-testid='onboarding-complete-done']", 2
        ).click()

        last_tab.wait.ele_displayed(
            "x://button[@data-testid='pin-extension-next']", timeout=10
        )
        get_element(
            last_tab, "x://button[@data-testid='pin-extension-next']", 2
        ).click()

        last_tab.wait.ele_displayed(
            "x://button[@data-testid='pin-extension-done']", timeout=10
        )
        get_element(
            last_tab, "x://button[@data-testid='pin-extension-done']", 2
        ).click()

        if last_tab.wait.ele_displayed(
            "x://span[@data-testid='account-value-and-suffix']", timeout=15
        ):
            popover_close_ele = get_element(
                last_tab, "x://button[@data-testid='popover-close']", 5
            )
            if popover_close_ele:
                popover_close_ele.click()
            logger.success(f"{self.id} 导入钱包成功")
            sleep(6)
            return True
        return False

    def sign(self):
        pass

    @check_metamask_page
    def connect(self):
        logger.info(f"{self.id} 准备开始连接MetaMask钱包...")
        try:
            latest_tab = self.page.latest_tab
            latest_tab.ele("x://button[2]").click()
            sleep(5)
            latest_tab = self.page.get_tab(0)
            if latest_tab.title != "MetaMask":
                return

            return try_click(latest_tab, xpath="x://button[2]", id=self.id)

        except Exception as e:
            logger.error(e)
            return False