from time import sleep

from loguru import logger
from .base_wallet import BaseWallet, WalletType
from src.browsers.base_browser import BaseBrowser
from src.browsers.operations import try_click
from src.utils import get_element, get_elements
from retry import retry


def check_keplr_page(func):
    """检查是否为 keplr 钱包页面的装饰器"""

    def wrapper(self, *args, **kwargs):
        for _ in range(15):
            tab = self.page.latest_tab
            if "Keplr" in tab.title:
                return func(self, *args, **kwargs)
            sleep(1)
        logger.warning(f"{self.id} 未唤起钱包页面...")
        return False

    return wrapper


class KeplrWallet(BaseWallet):

    def __init__(self, browser: BaseBrowser):
        super().__init__(browser=browser)
        self.import_url = f"{self.home_url}/register.html#"

    @property
    def wallet_type(self) -> WalletType:
        return WalletType.KEPLR

    @property
    def home_url(self) -> str:
        return f"chrome-extension://{self.extension_id}"

    @retry(tries=3, delay=1)
    def login(self, password: str) -> bool:
        last_tab = self.page.new_tab(f"{self.home_url}/popup.html")
        sleep(3)
        if get_element(last_tab, "Available Balance", timeout=3):
            logger.success(f"{self.id} 钱包已经登录")
            last_tab.close()
            return True

        pwd_input = get_element(last_tab, "x://input[@type='password']", timeout=3)
        if not pwd_input:
            raise Exception(f"{self.id} Backpack加载失败")

        pwd_input.input(f"{password}\n")
        sleep(2)

        if get_element(last_tab, "x://button[.='Tokens']", timeout=10):
            logger.success(f"{self.id} 钱包登录成功")
            last_tab.close()
            return True

        return False

    @retry(tries=3, delay=1)
    def setup(self, password: str, phrase: str) -> bool:
        tab = self.page.new_tab(self.import_url)

        sleep(3)
        if "register" not in tab.url:
            logger.success(f"{self.id} 钱包已经导入")
            return True

        # 导入钱包
        try_click(tab, "x://button[.='Import an existing wallet']")
        sleep(1)

        # 选择导入助记词
        try_click(tab, "x://button[.='Use recovery phrase or private key']")
        sleep(3)

        words = phrase.split()

        # FIXME: 这里比较奇怪，不点按钮切换，当助记词只有12个时，input获取只有11个
        try_click(tab, "x://button[.='24 words']")
        sleep(1)
        if len(words) == 12:
            try_click(tab, "x://button[.='12 words']")
            sleep(1)

        # 输入助记词
        inputs = get_elements(tab, "x://input[@type='password']")
        for word, input_field in zip(words, inputs):
            input_field.input(word)
            sleep(0.1)

        # 导入助记词
        try_click(tab, "x://button[.='Import']")
        sleep(3)

        # wallet name
        wallet_name = tab.ele("x://input[@name='name']")
        if wallet_name:
            wallet_name.input(self.id)
            sleep(1)

        inputs = get_elements(tab, "x://input[@placeholder='At least 8 characters in length']")
        for input in inputs:
            input.input(password)
            sleep(0.5)

        # next
        try_click(tab, "x://button[.='Next']")
        sleep(1)

        # save
        try_click(tab, "x://button[.='Save']")
        sleep(1)

        if tab.ele("Account Created", timeout=10):
            logger.success(f"{self.id} 钱包导入成功")
            
            # finish
            try_click(tab, "x://button[.='Finish']")
            return True

        logger.error(f"{self.id} 钱包导入失败")
        return False

    def sign(self):
        pass

    def approve(self):
        pass

    # 钱包连接
    @check_keplr_page
    def connect(self, password: str) -> bool:
        logger.info(f"{self.id} 准备开始连接keplr钱包...")
        try:
            latest_tab = self.page.latest_tab
            password_ele = latest_tab.ele("x://input[@type='password']", timeout=5)
            if password_ele:
                password_ele.input(password)
                sleep(1)
                try_click(latest_tab, "x://button[.='Unlock']")
                sleep(1)

            approve_ele = latest_tab.ele("x://div[.='Approve']", timeout=6)
            if approve_ele:
                try_click(latest_tab, "x://div[.='Approve']")
                sleep(1)

            if self.page.latest_tab.title != "Backpack":
                return True

            return False
        except Exception as e:
            logger.error(e)
            return False
