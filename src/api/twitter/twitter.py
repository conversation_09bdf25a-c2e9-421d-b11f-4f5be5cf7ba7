import json
from urllib.parse import urlparse

from src.utils.fake_ua import get_headers
from src.utils.tls_client import TLSClient

from .client_transaction import ClientTransaction


class UserNotFoundError(Exception):
    def __init__(self):
        super().__init__("User not found")


def _get_headers(index) -> dict:
    headers = get_headers("https://x.com", int(index))
    headers["accept"] = "*/*"
    headers["accept-language"] = "en-US,en;q=0.9"
    headers["authorization"] = (
        "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs"
        "%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA"
    )
    headers["x-twitter-active-user"] = "yes"
    headers["x-twitter-auth-type"] = "OAuth2Session"
    headers["x-twitter-client-language"] = "en"
    headers["x-csrf-token"] = ""
    return headers


class Twitter:
    COOKIES_DOMAIN = ".x.com"

    def __init__(self, index, token: str, proxy: str):
        self.index = index
        self.token = token
        self.proxy = proxy
        self.ct0 = None
        self.screen_name = None
        self.twitter_id = None
        self.twitter_error = False
        self.client_transaction = ClientTransaction()

        # 初始化 TLS 客户端
        self.tls = TLSClient(self.proxy, _get_headers(self.index), debug=True)

    def start(self):
        """初始化并启动 Twitter 客户端."""
        self.client_transaction.init(self.tls)
        self.set_cookies({"auth_token": self.token})
        ct0 = self.ct0
        if ct0 == "" or ct0 is None:
            ct0 = self._get_ct0()
            self.ct0 = ct0
        self.set_cookies({"ct0": ct0})
        self.tls.update_headers({"x-csrf-token": ct0})
        if self.screen_name == "" or self.screen_name is None:
            self.screen_name = self.get_my_profile_info()
        if self.twitter_id == "" or self.twitter_id is None:
            self.twitter_id = self.get_user_id(self.screen_name)

    def set_cookies(self, cookies):
        for name, value in cookies.items():
            self.tls.sess.cookies.set(name, value, self.COOKIES_DOMAIN)

    def request(self, method, url, resp_handler=None, **kwargs):
        try:
            tx_id = self.client_transaction.generate_transaction_id(method, urlparse(url).path)
            headers = {"X-Client-Transaction-Id": tx_id}
            if "headers" in kwargs:
                headers.update(kwargs.pop("headers"))
            resp_handler = self.get_check_errors_resp_handler(resp_handler)
            return self.tls.request(method, url, headers=headers, resp_handler=resp_handler, **kwargs)
        except Exception as e:
            self.twitter_error = True
            raise Exception(f"Request error: {str(e)}") from e

    def _get_ct0(self):
        try:
            self.tls.get("https://api.x.com/1.1/account/settings.json", raw=True)
            return self.tls.sess.cookies.get("ct0", self.ct0, self.COOKIES_DOMAIN)
        except Exception as e:
            reason = "Your account has been locked\n" if "Your account has been locked" in str(e) else ""
            self.twitter_error = True
            raise Exception(f"Failed to get ct0 for twitter: {reason}{str(e)}") from e

    def get_check_errors_resp_handler(self, resp_handler):
        check = self.check_response_errors
        return lambda resp: check(resp) if resp_handler is None else resp_handler(check(resp))

    @classmethod
    def check_response_errors(cls, resp):
        if type(resp) is not dict:
            return resp
        errors = resp.get("errors", [])
        if type(errors) is not list:
            return resp
        if len(errors) == 0:
            return resp
        msgs = [
            msg
            for msg in [f"{err.get('message')} (code={err.get('code')})" for err in errors if type(err) is dict]
            if msg
        ]
        msgs = list(set(msgs))
        error_msg = " | ".join(msgs)
        if len(error_msg) == 0:
            return resp
        raise Exception(error_msg)

    def get_my_profile_info(self):
        url = "https://api.x.com/graphql/UhddhjWCl-JMqeiG4vPtvw/Viewer"
        features = {
            "rweb_tipjar_consumption_enabled": True,
            "responsive_web_graphql_exclude_directive_enabled": True,
            "verified_phone_label_enabled": False,
            "creator_subscriptions_tweet_preview_api_enabled": True,
            "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
            "responsive_web_graphql_timeline_navigation_enabled": True,
        }
        field_toggles = {
            "isDelegate": False,
            "withAuxiliaryUserLabels": False,
        }
        variables = {"withCommunitiesMemberships": True}
        params = {
            "features": features,
            "fieldToggles": field_toggles,
            "variables": variables,
        }
        try:
            return self.request(
                "GET",
                url,
                params=params,
                resp_handler=lambda r: r["data"]["viewer"]["user_results"]["result"]["legacy"]["screen_name"],
            )
        except Exception as e:
            raise Exception(f"Get my username error: {str(e)}") from e

    def get_followers_count(self, username):
        url = "https://x.com/i/api/graphql/G3KGOASz96M-Qu0nwmGXNg/UserByScreenName"
        params = {
            "variables": to_json({"screen_name": username, "withSafetyModeUserFields": True}),
            "features": to_json(
                {
                    "hidden_profile_likes_enabled": True,
                    "hidden_profile_subscriptions_enabled": True,
                    "responsive_web_graphql_exclude_directive_enabled": True,
                    "verified_phone_label_enabled": False,
                    "subscriptions_verification_info_is_identity_verified_enabled": True,
                    "subscriptions_verification_info_verified_since_enabled": True,
                    "highlights_tweets_tab_ui_enabled": True,
                    "creator_subscriptions_tweet_preview_api_enabled": True,
                    "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                    "responsive_web_graphql_timeline_navigation_enabled": True,
                }
            ),
            "fieldToggles": to_json({"withAuxiliaryUserLabels": False}),
        }
        try:
            return self.request(
                "GET",
                url,
                params=params,
                resp_handler=lambda r: r["data"]["user"]["result"]["legacy"]["followers_count"],
            )
        except Exception as e:
            raise Exception(f"Get followers count error: {str(e)}") from e

    def get_user_id(self, username):
        url = "https://x.com/i/api/graphql/-0XdHI-mrHWBQd8-oLo1aA/ProfileSpotlightsQuery"
        if username[0] == "@":
            username = username[1:]
        username = username.lower()
        params = {"variables": to_json({"screen_name": username})}

        def _handler(resp):
            if type(resp) is dict and len(resp.get("data", {})) == 0:
                raise UserNotFoundError()
            return int(resp["data"]["user_result_by_screen_name"]["result"]["rest_id"])

        try:
            return self.request("GET", url, params=params, resp_handler=_handler)
        except Exception as e:
            raise Exception(f"Get user id error: {str(e)}") from e

    def follow(self, username):
        user_id = self.get_user_id(username)
        url = "https://x.com/i/api/1.1/friendships/create.json"
        params = {
            "include_profile_interstitial_type": "1",
            "include_blocking": "1",
            "include_blocked_by": "1",
            "include_followed_by": "1",
            "include_want_retweets": "1",
            "include_mute_edge": "1",
            "include_can_dm": "1",
            "include_can_media_tag": "1",
            "include_ext_has_nft_avatar": "1",
            "include_ext_is_blue_verified": "1",
            "include_ext_verified_type": "1",
            "include_ext_profile_image_shape": "1",
            "skip_status": "1",
            "user_id": user_id,
        }
        headers = {"content-type": "application/x-www-form-urlencoded"}
        try:
            self.request("POST", url, params=params, headers=headers, resp_handler=lambda r: r["id"])
        except Exception as e:
            raise Exception(f"Follow error: {str(e)}") from e

    def post_tweet(self, text, tweet_id=None) -> str:
        action = "CreateTweet"
        query_id = "xT36w0XM3A8jDynpkram2A"
        _json = {
            "variables": {
                "tweet_text": text,
                "media": {"media_entities": [], "possibly_sensitive": False},
                "semantic_annotation_ids": [],
                "dark_request": False,
            },
            "features": {
                "communities_web_enable_tweet_community_results_fetch": True,
                "c9s_tweet_anatomy_moderator_badge_enabled": True,
                "tweetypie_unmention_optimization_enabled": True,
                "responsive_web_edit_tweet_api_enabled": True,
                "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                "view_counts_everywhere_api_enabled": True,
                "longform_notetweets_consumption_enabled": True,
                "responsive_web_twitter_article_tweet_consumption_enabled": True,
                "tweet_awards_web_tipping_enabled": False,
                "creator_subscriptions_quote_tweet_preview_enabled": False,
                "longform_notetweets_rich_text_read_enabled": True,
                "longform_notetweets_inline_media_enabled": True,
                "articles_preview_enabled": True,
                "rweb_video_timestamps_enabled": True,
                "rweb_tipjar_consumption_enabled": True,
                "responsive_web_graphql_exclude_directive_enabled": True,
                "verified_phone_label_enabled": False,
                "freedom_of_speech_not_reach_fetch_enabled": True,
                "standardized_nudges_misinfo": True,
                "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                "responsive_web_graphql_timeline_navigation_enabled": True,
                "responsive_web_enhance_cards_enabled": False,
            },
            "queryId": query_id,
        }

        if tweet_id:
            _json["variables"]["reply"] = {"in_reply_to_tweet_id": tweet_id, "exclude_reply_user_ids": []}

        url = f"https://x.com/i/api/graphql/{query_id}/{action}"

        def _handler(resp):
            _result = resp["data"]["create_tweet"]["tweet_results"]["result"]
            _username = _result["core"]["user_results"]["result"]["legacy"]["screen_name"]
            _tweet_id = _result["rest_id"]
            _url = f"https://x.com/{_username}/status/{_tweet_id}"
            return _url

        try:
            return self.request("POST", url, json=_json, resp_handler=_handler)
        except Exception as e:
            raise Exception(f"Post tweet error: {str(e)}") from e

    def retweet(self, post_id):
        action = "CreateRetweet"
        query_id = "ojPdsZsimiJrUGLR1sjUtA"
        url = f"https://x.com/i/api/graphql/{query_id}/{action}"
        _json = {"variables": {"tweet_id": post_id, "dark_request": False}, "queryId": query_id}
        try:
            resp = self.request("POST", url, json=_json, resp_handler=lambda r: r)
            return resp
        except Exception as e:
            raise Exception(f"Retweet error: {str(e)}") from e

    def like(self, post_id) -> bool:
        action = "FavoriteTweet"
        query_id = "lI07N6Otwv1PhnEgXILM7A"
        url = f"https://x.com/i/api/graphql/{query_id}/{action}"
        _json = {"variables": {"tweet_id": post_id, "dark_request": False}, "queryId": query_id}
        try:
            return self.request("POST", url, json=_json, resp_handler=lambda r: r["data"]["favorite_tweet"] == "Done")
        except Exception as e:
            raise Exception(f"Like error: {str(e)}") from e

    def find_posted_tweet(self, text_condition_func, count=20) -> str:
        action = "UserTweets"
        query_id = "E3opETHurmVJflFsUBVuUQ"
        params = {
            "variables": to_json(
                {
                    "userId": self.twitter_id,
                    "count": count,
                    "includePromotedContent": False,
                    "withQuickPromoteEligibilityTweetFields": False,
                    "withVoice": False,
                    "withV2Timeline": True,
                }
            ),
            "features": to_json(
                {
                    "profile_label_improvements_pcf_label_in_post_enabled": False,
                    "rweb_tipjar_consumption_enabled": True,
                    "responsive_web_graphql_exclude_directive_enabled": True,
                    "verified_phone_label_enabled": False,
                    "creator_subscriptions_tweet_preview_api_enabled": True,
                    "responsive_web_graphql_timeline_navigation_enabled": True,
                    "responsive_web_graphql_skip_user_profile_image_extensions_enabled": False,
                    "premium_content_api_read_enabled": False,
                    "communities_web_enable_tweet_community_results_fetch": True,
                    "c9s_tweet_anatomy_moderator_badge_enabled": True,
                    "responsive_web_grok_analyze_button_fetch_trends_enabled": True,
                    "responsive_web_grok_analyze_post_followups_enabled": False,
                    "responsive_web_grok_share_attachment_enabled": False,
                    "articles_preview_enabled": True,
                    "responsive_web_edit_tweet_api_enabled": True,
                    "graphql_is_translatable_rweb_tweet_is_translatable_enabled": True,
                    "view_counts_everywhere_api_enabled": True,
                    "longform_notetweets_consumption_enabled": True,
                    "responsive_web_twitter_article_tweet_consumption_enabled": True,
                    "tweet_awards_web_tipping_enabled": False,
                    "creator_subscriptions_quote_tweet_preview_enabled": False,
                    "freedom_of_speech_not_reach_fetch_enabled": True,
                    "standardized_nudges_misinfo": True,
                    "tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled": True,
                    "rweb_video_timestamps_enabled": True,
                    "longform_notetweets_rich_text_read_enabled": True,
                    "longform_notetweets_inline_media_enabled": True,
                    "responsive_web_enhance_cards_enabled": False,
                }
            ),
        }

        url = f"https://x.com/i/api/graphql/{query_id}/{action}"

        def _handler(resp):
            instructions = resp["data"]["user"]["result"]["timeline_v2"]["timeline"]["instructions"]
            entries = None
            for instruction in instructions:
                if instruction["type"] == "TimelineAddEntries":
                    entries = instruction["entries"]
                    break
            if entries is None:
                return None
            for entry in entries:
                tweet_text = entry["content"]["itemContent"]["tweet_results"]["result"]
                tweet_text = tweet_text["legacy"]["full_text"]
                if not text_condition_func(tweet_text):
                    continue
                post_id = entry["entryId"]
                if post_id.startswith("tweet-"):
                    post_id = post_id[6:]
                _url = f"https://x.com/{self.screen_name}/status/{post_id}"
                return _url
            return None

        try:
            return self.request("GET", url, params=params, resp_handler=_handler)
        except Exception as e:
            raise Exception(f"Find posted tweet error: {str(e)}") from e


def to_json(obj):
    return json.dumps(obj, separators=(",", ":"), ensure_ascii=True)
