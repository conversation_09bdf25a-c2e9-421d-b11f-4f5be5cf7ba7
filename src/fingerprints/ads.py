import os

import requests
from dotenv import load_dotenv
from loguru import logger
from retry import retry

load_dotenv()

BASE_URL = os.getenv("ADS_BROWSER_API_BASE_URL") or "http://127.0.0.1:50325"
if BASE_URL.endswith("/"):
    BASE_URL = BASE_URL[:-1]

class OpenErrorException(Exception):
    def __init__(self, message="打开浏览器异常，重试"):
        super().__init__(message)


@retry(OpenErrorException, tries=3, delay=1)
def get_address_with_adspower(aid):
    debugger_address = None
    try:
        url = f"{BASE_URL}/api/v1/browser/start?cdp_mask=0&user_id=" + aid
        params = {"open_tabs": 1, "ip_tab": 1}

        # 检查请求响应
        response = requests.get(url=url, params=params)
        if response.status_code != 200:
            logger.error(f"请求失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            raise OpenErrorException(f"请求失败，状态码: {response.status_code}")

        # 检查响应内容是否为JSON
        try:
            resp = response.json()
        except ValueError:
            logger.error(f"响应内容不是有效的JSON: {response.text}")
            raise OpenErrorException("响应内容解析失败")

        if resp["code"] != 0:
            logger.error(f"API返回错误: {resp.get('msg', '未知错误')}")
            raise OpenErrorException(resp.get("msg", "未知错误"))

        # 检查返回数据结构
        if not resp.get("data"):
            logger.error("返回数据缺少data字段")
            raise OpenErrorException("返回数据格式错误")

        if not resp["data"].get("ws"):
            logger.error("返回数据缺少ws字段")
            raise OpenErrorException("返回数据格式错误")

        if not resp["data"]["ws"].get("selenium"):
            logger.error("返回数据缺少selenium字段")
            raise OpenErrorException("返回数据格式错误")

        debugger_address = resp["data"]["ws"]["selenium"]
        # logger.info(f"成功获取调试地址: {debugger_address}")

    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        raise OpenErrorException(f"请求异常: {str(e)}")
    except Exception as e:
        if isinstance(e, OpenErrorException):
            raise e
        logger.error(f"获取调试地址失败: {str(e)}")
        raise OpenErrorException(f"获取调试地址失败: {str(e)}")

    return debugger_address


def close_browser_with_adspower_id(aid):
    try:
        close_url = f"{BASE_URL}/api/v1/browser/stop?user_id=" + aid
        response = requests.get(close_url)
        if response.status_code != 200:
            logger.error(f"关闭浏览器请求失败，状态码: {response.status_code}")
        else:
            logger.info("浏览器关闭成功")
    except Exception as e:
        logger.error(f"关闭浏览器失败: {str(e)}")
