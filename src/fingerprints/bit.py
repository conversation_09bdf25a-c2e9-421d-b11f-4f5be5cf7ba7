import json
import os

import requests
from dotenv import load_dotenv

# 官方文档地址
# https://doc2.bitbrowser.cn/jiekou/ben-di-fu-wu-zhi-nan.html

# 此demo仅作为参考使用，以下使用的指纹参数仅是部分参数，完整参数请参考文档
load_dotenv()

url = os.getenv("BIT_BROWSER_API_BASE_URL") or "http://127.0.0.1:54345"
if url.endswith("/"):
    url = url[:-1]

headers = {"Content-Type": "application/json"}


def getBrowserList():
    json_data = {
        "page": 0,
        "pageSize": 100,
    }
    res = requests.post(
        f"{url}/browser/list", data=json.dumps(json_data), headers=headers
    ).json()

    if res["success"] == True:
        return res["data"]["list"]
    else:
        return False


def reset_closing_state(id):
    json_data = {"id": f"{id}"}
    return requests.post(
        f"{url}/browser/closing/reset", data=json.dumps(json_data), headers=headers
    ).json()


def open_browser(id):
    json_data = {"id": f"{id}"}
    return requests.post(
        f"{url}/browser/open", data=json.dumps(json_data), headers=headers
    ).json()


def get_address_with_bit(id):
    """获取指纹浏览器的调试地址
    Args:
        id (str): 浏览器ID
    Returns:
        str: 调试地址
    Raises:
        ValueError: 当ID无效或打开浏览器失败时
        TypeError: 当ID类型错误时
    """
    # 参数验证
    if id is None:
        raise ValueError("浏览器ID不能为空")
    if not isinstance(id, str):
        raise TypeError("浏览器ID必须是字符串类型")
    if not id.strip():
        raise ValueError("浏览器ID不能为空字符串")

    try:
        # 打开浏览器, 打开时经常会提示浏览器正在打开中或者关闭中，所以每次先重置状态
        # reset_closing_state(id)
        res = open_browser(id)

        # 验证返回结果
        if not res:
            raise ValueError(f"打开浏览器失败: ID={id}")

        if "data" not in res:
            raise ValueError(f"返回数据格式错误: {res}")

        if "http" not in res["data"]:
            raise ValueError(f"未找到调试地址: {res['data']}")

        debuggerAddress = res["data"]["http"]
        if not debuggerAddress:
            raise ValueError("获取到的调试地址为空")

        return debuggerAddress

    except Exception as e:
        print(f"获取调试地址失败: {str(e)}")
        raise ValueError(f"获取调试地址时发生错误: {str(e)}")


def close_browser_with_bit_id(id):
    json_data = {"id": f"{id}"}
    requests.post(
        f"{url}/browser/close", data=json.dumps(json_data), headers=headers
    ).json()


def deleteBrowser(id):
    json_data = {"id": f"{id}"}
    print(
        requests.post(
            f"{url}/browser/delete", data=json.dumps(json_data), headers=headers
        ).json()
    )


def updateBrowserProxy(
    id, port, country
):  # 更新窗口，支持批量更新和按需更新，ids 传入数组，单独更新只传一个id即可，只传入需要修改的字段即可，比如修改备注，具体字段请参考文档，browserFingerPrint指纹对象不修改，则无需传入
    json_data = {
        "ids": [f"{id}"],
        "ipCheckService": "IP2Location",
        "proxyMethod": 2,
        "proxyType": "socks5",
        "host": "127.0.0.1",
        "port": f"{port}",
        "proxyUserName": "",
        "proxyPassword": "",
        "ip": "",
        "city": "",
        "province": "",
        "country": f"{country}",
        "isIpNoChange": False,
        "dynamicIpUrl": "",
        "dynamicIpChannel": "",
        "isDynamicIpChangeIp": True,
        "isGlobalProxyInfo": False,
        "isIpv6": False,
    }
    response = requests.post(
        f"{url}/browser/proxy/update", data=json.dumps(json_data), headers=headers
    ).json()
    if response.get("success") is True:
        return True
    else:
        print(response)
        return False


def checkagent(host="127.0.0.1", port=0, proxyType="socks5", source=None):
    print(port)
    json_data = {"host": f"{host}", "port": f"{port}", "proxyType": f"{proxyType}"}
    response = requests.post(
        f"{url}/checkagent", data=json.dumps(json_data), headers=headers
    ).json()
    """处理响应数据并保存到文件"""
    if response.get("success") and response.get("data", {}).get("success") is True:
        return True
    else:
        print(response)
        return False


if __name__ == "__main__":
    # 配置代理
    updateBrowserProxy(
        id="447cbc16698246aa89d4b44a401d0db71", port="540020", country="us"
    )
