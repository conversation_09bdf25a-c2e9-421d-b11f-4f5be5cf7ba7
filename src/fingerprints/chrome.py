import os
from DrissionPage import Chromium, ChromiumOptions
from loguru import logger
from config import USER_DATA_PATH

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from abc import ABC, abstractmethod


@dataclass
class ExtensionConfig:
    path: str
    id: Optional[str] = None


@dataclass
class ChromeBrowserConfig:
    id: str
    user_agent: Optional[str] = None
    proxy: Optional[str] = None
    extensions: List[ExtensionConfig] = None


class ConfigurationStrategy(ABC):
    @abstractmethod
    def configure(self, options: ChromiumOptions, config: any) -> None:
        pass


class UserAgentStrategy(ConfigurationStrategy):
    def configure(self, options: ChromiumOptions, user_agent: str) -> None:
        if user_agent:
            options.set_user_agent(user_agent)


class ProxyStrategy(ConfigurationStrategy):
    def configure(self, options: ChromiumOptions, proxy: str) -> None:
        if not proxy:
            return

        parts = proxy.split(":")
        if len(parts) == 2:
            options.set_proxy(f"http://{proxy}")
        elif len(parts) == 3:
            options.set_proxy(proxy)


class ExtensionStrategy(ConfigurationStrategy):
    def configure(
            self, options: ChromiumOptions, extensions: List[ExtensionConfig]
    ) -> None:
        if not extensions:
            return

        for ext in extensions:
            if not ext.path:
                logger.warning(f"插件 {ext.id} 未指定路径，已跳过")
                continue

            if not os.path.exists(ext.path):
                logger.warning(f"插件路径不存在: {ext.path}")
                continue

            try:
                options.add_extension(ext.path)
                logger.info(f"成功加载插件: {ext.id or ext.path}")
            except Exception as e:
                logger.error(f"加载插件失败 {ext.id or ext.path}: {str(e)}")


class BrowserOptionsBuilder:
    def __init__(
            self, id: str, browser_config: dict, extensions: List[ExtensionConfig]
    ):
        self.id = id
        self.config = self._parse_config(browser_config, extensions)
        self.co = ChromiumOptions()
        self.co.set_argument("--accept-lang=en-US,en")
        self.co.set_argument("--lang=en-US")

        self.strategies = {
            "user_agent": UserAgentStrategy(),
            "proxy": ProxyStrategy(),
            "extensions": ExtensionStrategy(),
        }

    def _parse_config(
            self, data: Dict[str, Any], extensions: List[Dict[str, Any]]
    ) -> ChromeBrowserConfig:
        """解析浏览器配置

        Args:
            data: 浏览器基础配置字典
            extensions: 扩展配置列表

        Returns:
            ChromeBrowserConfig: 浏览器配置对象

        Raises:
            ValueError: 配置格式错误时抛出
        """
        try:
            valid_extensions = [
                ExtensionConfig(**ext)
                for ext in extensions
                if self._is_valid_extension(ext)
            ]

            return ChromeBrowserConfig(
                id=data.get("id"),
                user_agent=data.get("user_agent"),
                proxy=data.get("proxy"),
                extensions=valid_extensions,
            )
        except TypeError as e:
            raise ValueError(f"配置格式错误: {str(e)}")
        except Exception as e:
            raise ValueError(f"解析配置失败: {str(e)}")

    def _is_valid_extension(self, ext) -> bool:
        """验证扩展配置是否有效

        Args:
            ext: 扩展配置字典

        Returns:
            bool: 配置有效返回True，否则返回False
        """
        if not isinstance(ext, dict):
            logger.warning(f"无效的插件配置类型: {type(ext)}")
            return False

        if "path" not in ext:
            logger.warning(f"插件配置缺少必要的path字段: {ext}")
            return False

        return True

    def build(self) -> ChromiumOptions:
        """构建并返回浏览器配置"""
        self._validate()
        self._configure_user_data()

        # 应用所有配置策略
        self.strategies["user_agent"].configure(self.co, self.config.user_agent)
        self.strategies["proxy"].configure(self.co, self.config.proxy)
        self.strategies["extensions"].configure(self.co, self.config.extensions)

        return self.co

    def _validate(self) -> None:
        """验证必要的配置参数"""
        if not self.id:
            raise ValueError("浏览器ID不能为空")
        if not USER_DATA_PATH:
            raise ValueError("用户数据目录未配置, 请检查.env配置文件")

    def _configure_user_data(self):
        """配置用户数据目录"""
        # 移除重复的检查，因为 _validate 已经检查过了
        user_data_path = rf"{USER_DATA_PATH}/{self.id}"
        if not os.path.exists(user_data_path):
            os.makedirs(user_data_path)

        # 注意：local_port 不能和系统浏览器端口冲突
        local_port = 10000 + int(self.id)
        self.co.set_paths(user_data_path=user_data_path, local_port=local_port)


# 自动获取系统浏览器
def get_page_auto():
    co = ChromiumOptions().auto_port()
    return Chromium(co)


# 从已有的浏览器用户进行调试
def get_page_by_browser_user():
    co = ChromiumOptions()
    co.use_system_user_path(on_off=True)
    return Chromium(addr_or_opts=co)


# 根据浏览器名称获取浏览器配置
def get_page_with_browser_id(
        id: str, browser_config: dict, extensions: List[ExtensionConfig]
) -> Chromium:
    """
    根据浏览器ID和配置创建浏览器页面实例

    Args:
        id: 浏览器标识
        browser_config: 浏览器配置字典，包含 user_agent, proxy 等配置
        extensions: 浏览器扩展配置列表
    Returns:
        Chromium: 配置好的浏览器页面实例
    """
    co = BrowserOptionsBuilder(id, browser_config, extensions).build()
    return Chromium(co)
