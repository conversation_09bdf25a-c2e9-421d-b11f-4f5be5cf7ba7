import hashlib
import os
import time
import uuid
from threading import Lock

import requests
from dotenv import load_dotenv
from loguru import logger
from retry import retry

# 线程锁
start_lock = Lock()

load_dotenv()

BASE_URL = os.getenv("MORE_LOGIN_API_BASE_URL") or "http://127.0.0.1:40000"
if BASE_URL.endswith("/"):
    BASE_URL = BASE_URL[:-1]

class OpenErrorException(Exception):
    def __init__(self, message="打开浏览器异常，重试"):
        super().__init__(message)



def get_headers() -> dict:
    """生成请求头
    Returns:
        dict: 包含认证信息的请求头
    """
    api_id = os.getenv("MORELOGIN_API_ID")
    api_key = os.getenv("MORELOGIN_API_KEY")
    timestamp = str(int(time.time() * 1000))
    nonce_id = f"{timestamp}:{str(uuid.uuid4())}"

    # 计算签名: MD5(API ID + NonceId + SecretKey)
    sign_str = f"{api_id}{nonce_id}{api_key}"
    authorization = hashlib.md5(sign_str.encode()).hexdigest()

    return {
        "Content-Type": "application/json",
        "X-Api-Id": api_id,
        "X-Nonce-Id": nonce_id,
        "Authorization": authorization
    }



@retry(OpenErrorException, tries=3, delay=1)
def get_address_with_morelogin(aid):
    debugger_address = None
    try:
        url = f"{BASE_URL}/api/env/start"
        data = {"envId": aid}
        headers = get_headers()
        response = requests.post(url, json=data, headers=headers, timeout=30)
        if response.status_code != 200:
            logger.error(f"请求失败，状态码: {response.status_code}")
            logger.error(f"响应内容: {response.text}")
            raise OpenErrorException(f"请求失败，状态码: {response.status_code}")

        # 检查响应内容是否为JSON
        try:
            resp = response.json()
        except ValueError:
            logger.error(f"响应内容不是有效的JSON: {response.text}")
            raise OpenErrorException("响应内容解析失败")

        if resp["code"] != 0:
            logger.error(f"API返回错误: {resp.get('msg', '未知错误')}")
            raise OpenErrorException(resp.get("msg", "未知错误"))

        # 检查返回数据结构
        if not resp.get("data"):
            logger.error("返回数据缺少data字段")
            raise OpenErrorException("返回数据格式错误")

        if not resp["data"].get("debugPort"):
            logger.error("返回数据缺少ws字段")
            raise OpenErrorException("返回数据格式错误")

        debugger_address = int(resp["data"]["debugPort"])
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {str(e)}")
        raise OpenErrorException(f"请求异常: {str(e)}")
    except Exception as e:
        if isinstance(e, OpenErrorException):
            raise e
        logger.error(f"获取调试地址失败: {str(e)}")
        raise OpenErrorException(f"获取调试地址失败: {str(e)}")

    return debugger_address


def close_browser_with_morelogin_id(aid):
    try:
        close_url = f"{BASE_URL}/api/env/close"
        data = {"envId": aid}
        headers = get_headers()
        response = requests.post(close_url, json=data, headers=headers, timeout=30)
        response.raise_for_status()
        resp = response.json()

        if response.status_code != 200:
            logger.error(f"关闭浏览器请求失败，状态码: {response.status_code}")

        if resp["code"] != 0:
            error_msg = f"浏览器 {aid} 关闭失败， error={resp}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        else:
            logger.info("浏览器关闭成功")
    except Exception as e:
        logger.error(f"关闭浏览器失败: {str(e)}")
