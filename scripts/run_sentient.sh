#!/bin/bash

# 添加错误处理
set -e

# 记录开始时间
echo "开始执行时间: $(date)"

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录（脚本目录的上一级）
PROJECT_DIR="$( cd "$SCRIPT_DIR/.." && pwd )"

# 切换到项目目录
cd $PROJECT_DIR

if [ -d "$PROJECT_DIR/.venv" ]; then
    VENV_DIR="$PROJECT_DIR/.venv"
elif [ -d "$PROJECT_DIR/venv" ]; then
    VENV_DIR="$PROJECT_DIR/venv"
else
    echo "错误：未找到虚拟环境目录，请确保已创建虚拟环境（.venv 或 venv）"
    exit 1
fi

# 激活虚拟环境
source "$VENV_DIR/bin/activate"

# 执行 Python 脚本
# python3 examples/sentient/giveaway.py run -i 1-100

# 退出虚拟环境
deactivate

# 记录结束时间
echo "结束执行时间: $(date)"
