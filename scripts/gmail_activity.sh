#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录（假设脚本在 scripts 目录下）
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 切换到项目目录
cd "${PROJECT_DIR}" || exit 1


if [ -d "$PROJECT_DIR/.venv" ]; then
    VENV_DIR="$PROJECT_DIR/.venv"
elif [ -d "$PROJECT_DIR/venv" ]; then
    VENV_DIR="$PROJECT_DIR/venv"
else
    echo "错误：未找到虚拟环境目录，请确保已创建虚拟环境（.venv 或 venv）"
    exit 1
fi

# 激活虚拟环境
source "$VENV_DIR/bin/activate"

# 添加错误处理
handle_error() {
    echo "发生错误，脚本退出"
    exit 1
}

# 设置错误处理
trap handle_error ERR


echo "开始启动gmail..."
if ! python3 index.py open -u 'https://mail.google.com/mail/u/0/' -t ads -i 1-100 -c; then
    echo "执行失败，等待下次重试..."
fi
echo "启动gmail程序完成..."


