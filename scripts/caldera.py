import click
from src.evm.nft_utils import NFTUtils
from src.utils.browser_config import BrowserConfigInstance
import os
import random, time
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType

from src.utils.browser_config import BrowserConfigInstance
from src.utils.secure_encryption import SecureEncryption

from config import DEFAULT_BROWSER_TYPE
from src.utils.common import parse_indices
from src.utils.hhcsv import HHCSV
from src.controllers import Browser<PERSON>ontroller

ARB_RPC_URL = "https://arbitrum-one-rpc.publicnode.com"
RAR_RPC_URL = "https://mainnet.rpc.rarichain.org/http"
MANTA_RPC_URL = "https://pacific-rpc.manta.network/http"
B3_RPC_URL = "https://mainnet-rpc.b3.fun"
SANKO_RPC_URL = "https://mainnet.sanko.xyz"
ZERO_RPC_URL = "https://rpc.zerion.io/v1/zero"
default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
MAX_TASK_COUNT = 4
TASK_URL = "https://superboard.xyz/quests/caldera-wrapped-zero-edition"


class ScriptCaldera(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "caldera"
        self.wallet_address = self.browser_config.evm_address

    def day_3_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=ARB_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            return nft_utils.balance_erc1155(
                "******************************************",
                self.wallet_address,
                1,
            )
        except Exception as e:
            logger.error(f"获取第三天NFT余额失败: {e}")
            return 0

    def day_4_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=RAR_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            return nft_utils.balance_erc1155(
                "******************************************",
                self.wallet_address,
                0,
            )
        except Exception as e:
            logger.error(f"获取第四天NFT余额失败: {e}")
            return 0

    def day_5_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=MANTA_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            wallet_address = self.browser_config.evm_address
            if not wallet_address:
                logger.error(f"钱包地址为空: {self.browser_config.evm_address}")
                return 0
            return nft_utils.balance_erc1155(
                "******************************************",
                wallet_address,
                0,
            )
        except Exception as e:
            logger.error(f"获取第五天NFT余额失败: {e}")
            return 0

    def day_6_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=B3_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            wallet_address = self.browser_config.evm_address
            if not wallet_address:
                logger.error(f"钱包地址为空: {self.browser_config.evm_address}")
                return 0
            return nft_utils.balance_erc1155(
                "******************************************",
                wallet_address,
                0,
            )
        except Exception as e:
            logger.error(f"获取第六天NFT余额失败: {e}")
            return 0

    def day_7_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=SANKO_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            wallet_address = self.browser_config.evm_address
            if not wallet_address:
                logger.error(f"钱包地址为空: {self.browser_config.evm_address}")
                return 0

            return nft_utils.balance_erc1155(
                "******************************************",
                wallet_address,
                0,
            )
        except Exception as e:
            logger.error(f"获取第七天NFT余额失败: {e}")
            return 0

    def day_9_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=ARB_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            wallet_address = self.browser_config.evm_address
            if not wallet_address:
                logger.error(f"钱包地址为空: {self.browser_config.evm_address}")
                return 0

            return nft_utils.balance_erc1155(
                "******************************************",
                wallet_address,
                0,
            )
        except Exception as e:
            logger.error(f"获取第九天NFT余额失败: {e}")
            return 0

    def day_10_nft_balance(self):
        try:
            nft_utils = NFTUtils(
                rpc_url=ZERO_RPC_URL,
                proxy=self.browser_config.proxy,
                user_agent=self.browser_config.user_agent,
            )
            wallet_address = self.browser_config.evm_address
            if not wallet_address:
                logger.error(f"钱包地址为空: {self.browser_config.evm_address}")
                return 0

            return nft_utils.balance_erc1155(
                "******************************************",
                wallet_address,
                0,
            )
        except Exception as e:
            logger.error(f"获取第十天NFT余额失败: {e}")
            return 0


@click.group()
def cli():
    pass


# @cli.command("u")
# @click.option("-i", "--indexes", type=str, help="钱包地址")
# @click.option(
#     "-t",
#     "--type",
#     type=click.Choice(BROWSER_TYPES),
#     default=default_browser_type,
#     help="浏览器类型",
# )
def update_nft_balance(indexes: str, type: str):
    path = os.path.join(os.path.dirname(__file__), "caldera.csv")
    input_csv = HHCSV(path)
    indices = parse_indices(indexes)
    for index in indices:

        result = input_csv.query(criteria={"id": str(index)})
        if not result:
            logger.warning(f"{index} 未找到对应的配置")
            continue

        # info = result[0]
        # day6 = info.get("day6") or 0
        # if int(day6) == 1:
        #     continue

        caldera = ScriptCaldera(type, index)
        day10 = caldera.day_10_nft_balance()
        logger.info(f"{index} {caldera.wallet_address} day10: {day10}")
        input_csv.update_row(
            criteria={"id": str(index)},
            updates={"day10": day10},
        )


@cli.command("r")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
def run(type: str):
    path = os.path.join(os.path.dirname(__file__), "caldera.csv")
    input_csv = HHCSV(path)
    results = input_csv.query()
    random.shuffle(results)
    tasks = []
    for result in results:
        id = result.get("id")

        tx = result.get("tx") or 0
        if int(tx) < 3:
            continue

        day10 = result.get("day10") or 0
        if int(day10) == 1:
            continue

        caldera = ScriptCaldera(default_browser_type, id)
        day10 = caldera.day_10_nft_balance()
        if day10 == 1:
            input_csv.update_row(
                criteria={"id": str(id)},
                updates={"day10": day10},
            )
            continue

        tasks.append(id)

        if len(tasks) >= MAX_TASK_COUNT:
            break

    logger.info(f"tasks: {tasks}")

    for task in tasks:
        browser = BrowserController(type, task)
        browser.metamask_wallet_login()
        # browser.okx_wallet_login()
        # browser.close_other_tabs()
        time.sleep(3)
        browser.open_url(TASK_URL)


# @cli.command("w")
# def write_csv():
#     path = os.path.join(os.path.dirname(__file__), "caldera.csv")
#     input_csv = HHCSV(path)
#     indexes = ['14', '64', '59', '31', '48', '39']
#     for index in indexes:
#         input_csv.update_row(
#             criteria={"id": str(index)},
#             updates={"day5": 1},
#         )


if __name__ == "__main__":
    cli()
    # update_nft_balance("1", default_browser_type)
    # write_csv()
    # update_nft_balance("12")
