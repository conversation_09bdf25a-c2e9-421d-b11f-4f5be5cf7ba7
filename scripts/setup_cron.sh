#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
# 获取项目根目录（脚本目录的上一级）
PROJECT_DIR="$( cd "$SCRIPT_DIR/.." && pwd )"

# 设置日志文件路径
LOG_FILE="$PROJECT_DIR/logs/somnia_cron.log"
LOG_DIR=$(dirname "$LOG_FILE")

# 创建日志目录（如果不存在）
mkdir -p "$LOG_DIR"

# 日志记录函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查当前 crontab 中是否已存在该任务
CURRENT_CRON=$(crontab -l 2>/dev/null)

# 定义两个不同的定时任务
CRON_JOB1="0 6 * * * /bin/bash $PROJECT_DIR/scripts/run_somnia.sh >> $LOG_FILE 2>&1"
CRON_JOB2="0 5 * * * /bin/bash $PROJECT_DIR/scripts/run_somnia.sh ads >> $LOG_FILE 2>&1"

# 创建一个临时文件来存储新的 crontab 内容
TEMP_CRON=$(mktemp)
echo "$CURRENT_CRON" > "$TEMP_CRON"

# 检查并添加第一个任务
if [[ $CURRENT_CRON == *"$CRON_JOB1"* ]]; then
    log_message "定时任务1已存在，无需重复添加"
else
    echo "$CRON_JOB1" >> "$TEMP_CRON"
    log_message "添加定时任务1：每天早上 6 点执行"
fi

# 检查并添加第二个任务
if [[ $CURRENT_CRON == *"$CRON_JOB2"* ]]; then
    log_message "定时任务2已存在，无需重复添加"
else
    echo "$CRON_JOB2" >> "$TEMP_CRON"
    log_message "添加定时任务2：每天早上 5 点执行 ads 参数"
fi

# 应用新的 crontab
crontab "$TEMP_CRON"

if [ $? -eq 0 ]; then
    log_message "成功更新 crontab"
    log_message "当前 crontab 内容："
    
    # 直接将 crontab 内容输出到日志
    CRONTAB_CONTENT=$(crontab -l 2>/dev/null)
    if [ -z "$CRONTAB_CONTENT" ]; then
        log_message "crontab 为空"
    else
        echo "$CRONTAB_CONTENT" | while IFS= read -r line; do
            log_message "  $line"
        done
    fi
else
    log_message "更新 crontab 失败"
fi

# 删除临时文件
rm "$TEMP_CRON"