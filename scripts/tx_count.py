from typing import Dict, Optional, Any
from loguru import logger
from web3 import Web3
from src.utils.hhcsv import HHCSV
from config import ETH_RPC_URL, ALCHEMY_API_KEY
import requests


class EthereumTxScanner:
    """Ethereum Transaction Scanner - Query address nonce"""

    def __init__(self, rpc_url: str):
        """Initialize scanner"""
        self.web3 = Web3(Web3.HTTPProvider(rpc_url))
        self.alchemy_base_url = (
            f"https://eth-mainnet.g.alchemy.com/v2/{ALCHEMY_API_KEY}"
        )

        if not self.web3.is_connected():
            raise ConnectionError("Failed to connect to Ethereum network")

    def check_address_nfts(self, address: str) -> Dict[str, Any]:
        """检查地址的所有NFT持有情况"""
        try:
            url = f"{self.alchemy_base_url}/getNFTs/"
            params = {
                "owner": address,
                "pageSize": 1,  # 只需要确认是否持有，所以设置为1
            }

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                total_nfts = data.get("totalCount", 0)
                return {"has_nft": total_nfts > 0, "nft_count": total_nfts}
            return {"has_nft": False, "nft_count": 0}
        except Exception as e:
            logger.error(f"检查NFT失败: {str(e)}")
            return {"has_nft": False, "nft_count": 0}

    def process_addresses_from_file(self, input_file: str, output_file: str) -> None:
        """
        Process addresses from file

        Args:
            input_file: Input CSV file path
            output_file: Output CSV file path
        """
        try:
            # Initialize input CSV with headers if file doesn't exist
            input_headers = ["address"]
            input_csv = HHCSV(input_file, headers=input_headers)

            # Initialize output CSV with headers
            output_headers = ["address", "status", "nonce", "total_tx", "block_number"]

            output_csv = HHCSV(output_file, headers=output_headers)

            # Process each address
            addresses = [row["address"] for row in input_csv.query()]
            total = len(addresses)

            if total == 0:
                logger.warning("No addresses found in input file")
                return

            for idx, address in enumerate(addresses, 1):
                logger.info(f"Processing {idx}/{total}: {address}")

                try:
                    result = self.get_address_nonce(address)
                    output_csv.add_row(result)

                except Exception as e:
                    logger.error(f"Error processing address {address}: {str(e)}")
                    continue

            logger.success(f"All addresses processed, results saved to: {output_file}")

        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")

    def get_address_nonce(self, address: str) -> Dict[str, Any]:
        """
        Get address nonce information

        Args:
            address: Wallet address to query

        Returns:
            Dict: Transaction count information
        """
        try:
            if not self.web3 or not self.web3.is_connected():
                raise Exception("Web3 not connected")

            # Convert to checksum address
            checksum_address = self.web3.to_checksum_address(address)

            # Get nonce
            nonce = self.web3.eth.get_transaction_count(checksum_address)

            # 检查NFT持有情况
            nft_info = self.check_address_nfts(address)

            result = {
                "address": checksum_address,
                "status": "success",
                "nonce": nonce,
                "total_tx": nonce + 1,  # nonce starts from 0
                "nft_count": nft_info["nft_count"],
            }

            logger.info(f"Address {address} nonce info: {result}")
            return result

        except Exception as e:
            error_msg = f"Failed to get address nonce: {str(e)}"
            logger.error(error_msg)
            return {
                "address": address,
                "status": "error",
                "nonce": 0,
                "total_tx": 0,
                "block_number": 0,
            }


def main():
    INPUT_FILE = "scripts/addresses.csv"
    OUTPUT_FILE = "scripts/nonce_results.csv"
    try:
        # Create scanner instance
        scanner = EthereumTxScanner(rpc_url=ETH_RPC_URL)

        # 直接使用已存在的地址文件
        scanner.process_addresses_from_file(INPUT_FILE, OUTPUT_FILE)

    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")


if __name__ == "__main__":
    main()
