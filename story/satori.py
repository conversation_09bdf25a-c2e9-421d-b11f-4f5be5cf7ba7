import random
import json
import os
from src.utils.proxies import Proxies
from loguru import logger
from time import sleep
from retry import retry
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from .contract.satori_contract import SatoriContract
from config import MAX_GAS_PRICE, PROXY_URL


class StorySatori(StoryBase):
    """Satori NFT任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "satori"
        self.home_url = "https://story-test.satori.finance/"
        self.wallet_address = self.browser.browser_config.evm_address
        self._satori_contract = None
        self.badge_contract_address = "******************************************"

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        tab = page.latest_tab
        address_ele = tab.ele("x://span[@class='address-span']", timeout=5)
        if address_ele:
            logger.success(f"{self.browser_id} 任务连接钱包成功")
            return True

        return False

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            if self._check_wallet_connected(page):
                return True

            # 点击连接钱包
            tab = page.latest_tab
            connect_wallet_button = tab.ele(
                "x://div[text()='Connect Wallet']", timeout=5
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                tab.ele("OKX Wallet").click()
                sleep(1)
                tab.ele("Send Request").click()
                self._connect_okx_wallet()
                sleep(3)

                # 关闭弹窗
                dialog = tab.ele("x://div[@id='notification_1']", timeout=5)
                if dialog:
                    dialog.ele(
                        "x://i[@class='el-icon el-notification__closeBtn']"
                    ).click()

            # 检查连接状态
            if self._check_wallet_connected(page):
                return True

            raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    @property
    def satori_contract(self) -> SatoriContract:
        if not self._satori_contract:
            rpc_url = os.getenv("RPC_URL")
            private_key = self.browser.browser_config.evm_private_key
            proxy = self.browser.browser_config.proxy
            if proxy:
                is_valid = Proxies(proxy).verify()
                if not is_valid:
                    logger.error(f"{self.browser_id} {self.project_name} 代理无效")
                    raise Exception(f"{self.browser_id} {self.project_name} 代理无效")

            user_agent = self.browser.browser_config.user_agent
            self._satori_contract = SatoriContract(
                id=self.browser_id,
                rpc_url=rpc_url,
                private_key=private_key,
                proxy=proxy,
                user_agent=user_agent,
            )

        return self._satori_contract

    def _get_deposit_balance(self, tab) -> float:
        """获取存款余额"""
        account_amount_ele = tab.ele("x://div[@class='account-amount']", timeout=3)
        account_amount = account_amount_ele.text
        account_amount = account_amount.replace("$", "")
        return float(account_amount)

    def _check_deposit_balance(self, tab, timeout=180) -> bool:
        """检查存款余额"""
        for _ in range(timeout):
            try:
                account_amount = self._get_deposit_balance(tab)
                if account_amount > 0:
                    logger.success(f"{self.browser_id} 找到存款余额: {account_amount}")
                    return True

                sleep(1)
            except Exception as e:
                logger.error(f"{self.browser_id} 获取存款余额失败: {e}")

        return False

    def _task_connects_wallet(self) -> bool:
        """任务连接钱包"""
        try:
            tab = self.page.latest_tab

            address_ele = tab.ele("x://span[@class='address-span']", timeout=5)
            if address_ele:
                logger.success(f"{self.browser_id} 任务连接钱包成功")
                return True

            # 点击连接钱包
            connect_wallet_button = tab.ele(
                "x://div[text()='Connect Wallet']", timeout=5
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                tab.ele("OKX Wallet").click()
                sleep(1)
                tab.ele("Send Request").click()
                self._connect_okx_wallet()
                sleep(3)

                # 关闭弹窗
                dialog = tab.ele("x://div[@id='notification_1']", timeout=5)
                if dialog:
                    dialog.ele(
                        "x://i[@class='el-icon el-notification__closeBtn']"
                    ).click()

            if not self._check_deposit_balance(tab):
                logger.error(f"{self.browser_id} 存款余额不足")
                return False

            return True
        except Exception as e:
            logger.error(f"{self.browser_id} 任务连接钱包失败: {e}")
            return False

    def _task_long_order(self) -> bool:
        """任务长单"""
        tab = self.page.latest_tab

        if not self._check_deposit_balance(tab):
            logger.error(f"{self.browser_id} 存款余额不足")
            return False

        # 找到输入框
        input_ele = tab.ele("x:(//input[@type='number'])[3]", timeout=5)
        if not input_ele:
            logger.error(f"{self.browser_id} 任务长单输入框查找失败")
            return False

        input_ele.clear(True)
        random_amount = random.randint(5, 10) * 10
        input_ele.input(random_amount)

        sleep(1)
        for _ in range(3):
            tab.ele("x://div[text()='Place Limit Order']/../..").click()
            sleep(1)
            result = self._sign_okx_wallet()
            if result:
                break

        for _ in range(30):
            order_placed_ele = tab.ele("Order placed", timeout=1)
            if order_placed_ele:
                logger.success(f"{self.browser_id} 下单成功")
                return True

        logger.error(f"{self.browser_id} 任务长单失败")
        return False

    @retry(tries=3, delay=1)
    def _task_vaults(self) -> bool:
        """任务Vaults"""
        try:
            sleep(random.randint(3, 6))
            tab = self.page.latest_tab

            # 找到Vaults点击tab
            tab.ele("x://span[text()='Vaults ']/..").click(True)

            sleep(6)
            # 找到自己创建的Vaults行
            vault_row_ele = tab.ele(
                "x:((//div[@class='el-table__body-wrapper'])[2]//tr[@class='el-table__row'])[2]",
                timeout=5,
            )
            if not vault_row_ele:
                logger.error(f"{self.browser_id} 没找到Vaults行")
                return False

            vault_row_ele.click(True)
            sleep(1)
            vault_row_ele.next().ele("x://div[@class='expand-stake-btn']").click()
            sleep(3)

            # 输入金额
            current_amount_ele = tab.ele("x://span[@class='amount-span pr-0.5']")
            if not current_amount_ele:
                logger.error(f"{self.browser_id} 没找到当前金额")
                return False

            current_amount = float(current_amount_ele.text)
            input_ele = tab.ele("x://input[@class='filed-input-inner']")
            if not input_ele:
                logger.error(f"{self.browser_id} 没找到输入框")
                return False

            input_ele.clear(True)

            amount = random.randint(1, 5) * 10
            if amount > current_amount:
                amount = current_amount
            input_ele.input(amount)

            # 点击提交
            tab.ele("x://button[@type='submit']").click()
            self._sign_okx_wallet()

            # 等待完成
            for _ in range(30):
                deposit_success_ele = tab.ele("Deposit successfully", timeout=0.5)
                if deposit_success_ele:
                    logger.success(f"{self.browser_id} 任务Vaults完成")
                    return True
                sleep(0.5)

            return False

        except Exception as e:
            logger.error(f"{self.browser_id} 任务Vaults失败: {e}")
            raise

    def get_sign_for_odyssey(self):
        import requests

        if not PROXY_URL:
            logger.error(f"{self.browser_id} 代理未配置")
            raise Exception(f"{self.browser_id} 代理未配置")

        url = "https://story-odyssey.satori.finance/api/data-center/pub/taskVerify/signForOdyssey"

        payload = json.dumps({"address": self.wallet_address, "brandExchange": "story"})
        ua = self.browser.browser_config.user_agent
        proxies = {"http": PROXY_URL}
        # proxy = self.browser.browser_config.proxy
        # proxies = {"http": proxy, "https": proxy}
        headers = {
            "accept": "*/*",
            "accept-language": "en-US,en;q=0.9",
            "cache-control": "no-cache",
            "content-type": "application/json",
            "origin": "https://story-odyssey.satori.finance",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": "https://story-odyssey.satori.finance/",
            "user-agent": ua,
        }

        response = requests.request(
            "POST", url, headers=headers, data=payload, proxies=proxies
        )
        if "SUCCESS" in response.text:
            return response.json().get("data")
        raise Exception(f"获取mint satori 签名失败: {response.text}")

    def _task_mint_badge(self, signature=None):
        nft_contract = self.get_nft_contract()
        if not signature:
            signature = self.get_sign_for_odyssey()

        input_data = (
            "0xb510391f"
            f"{self.wallet_address.lower()[2:].rjust(64, '0')}"
            f"0000000000000000000000000000000000000000000000000000000000000040"
            f"0000000000000000000000000000000000000000000000000000000000000041"
            f"{signature['r'][2:]}"
            f"{signature['s'][2:]}"
            f"{signature['v'][2:].ljust(64, '0')}"
        )

        success = nft_contract.write_contract(
            self.badge_contract_address,
            input_data,
        )

        if success:
            logger.success(f"{self.browser_id} NFT铸造成功")
            self._update_task_status(True)
        else:
            logger.error(f"{self.browser_id} NFT铸造失败")

        return success

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Unleash任务"""
        if self._check_task_completed():
            return False

        if self._check_badge_minted():
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            return False

        try:
            # gas低了再启用
            gas_prices = self.get_gas_price()
            if gas_prices and gas_prices.average > MAX_GAS_PRICE:
                logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
                return False

            # 尝试获取签名, 有就进行mint勋章
            try:
                signature = self.get_sign_for_odyssey()
                if signature:
                    return self._task_mint_badge(signature)
            except Exception as e:
                pass

            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            sleep(5)
            if not self._connect_wallet(self.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 检查存款余额
            balance = self._get_deposit_balance(self.page.latest_tab)
            if balance == 0:
                # 领取测试币
                result = self.satori_contract.faucet_usdc_token()
                if not result:
                    logger.error(f"{self.browser_id} 领取测试币失败")
                    return False

                # 存钱
                result = self.satori_contract.deposit_usdc()
                if not result:
                    logger.error(f"{self.browser_id} 存钱失败")
                    return False

                result = self._task_connects_wallet()
                if not result:
                    logger.error(f"{self.browser_id} 连接钱包失败")
                    return False

            result = self._task_long_order()
            if not result:
                logger.error(f"{self.browser_id} 看涨任务失败")

            result = self._task_vaults()
            if not result:
                logger.error(f"{self.browser_id} 任务Vaults失败")
                return False

            # 随机等待一段时间
            sleep(random.randint(5, 20))
            result = self._task_mint_badge()
            if not result:
                logger.error(f"{self.browser_id} 铸造NFT失败")
                return False

            logger.success(f"{self.browser_id} Satori任务完成")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Satori任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Satori任务失败")
