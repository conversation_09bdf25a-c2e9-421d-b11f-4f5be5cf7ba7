import random

from loguru import logger
from src.browsers import BrowserType
from retry import retry
from src.evm import ChainInfo
from src.utils.browser_config import BrowserConfigInstance
from src.evm.erc20_utils import get_web3
from src.evm.erc20_utils import send_transaction, get_transaction


class StoryMorkie(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "morkie"
        self.wallet_address = self.browser_config.evm_address

        self.private_key = self.get_private_key()
        self.morkie_contract_address = "******************************************"
        self.morkie_contract_abi = [
            {
                "inputs": [
                    {"internalType": "address", "name": "_receiver", "type": "address"},
                    {"internalType": "uint256", "name": "_quantity", "type": "uint256"},
                    {"internalType": "address", "name": "_currency", "type": "address"},
                    {
                        "internalType": "uint256",
                        "name": "_pricePerToken",
                        "type": "uint256",
                    },
                    {
                        "components": [
                            {
                                "internalType": "bytes32[]",
                                "name": "proof",
                                "type": "bytes32[]",
                            },
                            {
                                "internalType": "uint256",
                                "name": "quantityLimitPerWallet",
                                "type": "uint256",
                            },
                            {
                                "internalType": "uint256",
                                "name": "pricePerToken",
                                "type": "uint256",
                            },
                            {
                                "internalType": "address",
                                "name": "currency",
                                "type": "address",
                            },
                        ],
                        "internalType": "tuple",
                        "name": "_allowlistProof",
                        "type": "tuple",
                    },
                    {"internalType": "bytes", "name": "_data", "type": "bytes"},
                ],
                "name": "claim",
                "outputs": [],
                "stateMutability": "payable",
                "type": "function",
            },
            {
                "inputs": [
                    {"internalType": "address", "name": "owner", "type": "address"}
                ],
                "name": "balanceOf",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function",
            },
        ]

    def _claim(self) -> bool:
        logger.info("[INFO] 执行claim")
        try:
            # 构建交易
            tx_params = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
                "value": 100000000000000000,  # 添加value参数，0.1 ETH
            }

            # 构建 allowlistProof 元组
            # 使用 2**256 - 1 作为最大的 uint256 值
            max_uint256 = 2**256 - 1

            allowlist_proof = (
                [],  # bytes32[] proof
                0,  # uint256 quantityLimitPerWallet
                max_uint256,  # uint256 pricePerToken
                "******************************************",  # address currency
            )

            # 执行合约函数
            morkie_contract = self.web3.eth.contract(
                address=self.morkie_contract_address, abi=self.morkie_contract_abi
            )
            # 获取余额
            balance = morkie_contract.functions.balanceOf(self.wallet_address).call()
            if balance > 0:
                logger.info(f"[INFO] 已经mint过Morkie NFT")
                return True
            mint_function = morkie_contract.functions.claim(
                self.wallet_address,  # _receiver
                1,  # _quantity
                "******************************************",  # _currency (ETH)
                100000000000000000,  # _pricePerToken (0.1 ETH)
                allowlist_proof,  # _allowlistProof (tuple)
                b"",  # _data
            )

            # 执行交易,需要先模拟交易执行
            try:
                mint_function.call(
                    {"from": self.wallet_address, "value": 100000000000000000}
                )
            except Exception as e:
                logger.info(f"[INFO] claim 交易失败: {e}")
                return False

            transaction = get_transaction(
                self.web3, tx_params, mint_function, gas_adjustment_factor=1
            )
            if not transaction:
                logger.error(f"[ERROR] 构建交易失败")
                return False

            # 如果交易成功，则执行交易
            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] claim 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] claim 异常: {e}")
            return False

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        """执行 morkie claim 任务"""
        try:
            logger.info(f"[INFO] 开始执行 {self.browser_id} 的 morkie claim 任务")
            rpc_url = ChainInfo.STORY_ODYSSEY["rpc"]
            proxy = self._get_valid_proxy()
            logger.info(f"[INFO] 使用代理: {proxy}")
            self.web3 = get_web3(
                rpc_url, proxy, user_agent=self.browser_config.user_agent
            )
            if self.web3 is None:
                return False

            if not self._claim():
                logger.error(f"[ERROR] {self.browser_id} morkie claim 任务失败")
                return False

            logger.success(f"[SUCCESS] {self.browser_id} morkie claim 任务执行完成")
            return True
        except Exception as e:
            logger.error(f"[ERROR] {self.browser_id} 任务失败: {e}")
            return False
