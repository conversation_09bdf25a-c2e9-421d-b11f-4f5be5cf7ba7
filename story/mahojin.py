import random
import json
from typing import Optional, Callable
from loguru import logger
from time import sleep
from retry import retry
from src.wallets.okx_wallet import OKXWallet
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from src.browsers.operations import try_click


class StoryMah<PERSON>jin(StoryBase):
    """Mahojin任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "mahojin"
        self.home_url = "https://app.mahojin.ai/ip-badge"
        self.wallet_address = self.browser.browser_config.evm_address

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = page.ele(
                "x://button[@aria-haspopup='dialog']",
                timeout=5,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        return True

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Mahojin任务"""
        if self._check_task_completed():
            return True

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.browser.page)

            # logger.success(f"{self.browser_id} Satori任务完成")
            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Mahojin任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Mahojin任务失败")
