import random
import json
from typing import Optional, Callable
from loguru import logger
from time import sleep
from retry import retry
from src.wallets.okx_wallet import OKXWallet
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from src.browsers.operations import try_click


class StoryUnleash(StoryBase):
    """Unleash NFT任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "unleash"
        self.home_url = "https://app.unleashprotocol.xyz/quests/"
        self.wallet_address = self.browser.browser_config.evm_address

    def get_status(self):
        """获取任务状态"""
        return self._get_task_status()

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = page.ele(
                "x://button[@aria-haspopup='dialog']",
                timeout=5,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            if self._check_wallet_connected(page):
                return True

            connect_button = page.ele("x://div[text()='Login']", timeout=5)
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect Wallet 按钮失败"
                )

            connect_button.click()

            # 如果点击了Connect Wallet后，出现Verify your account，则先签名即可
            auth_button = page.ele(
                "x://button[@data-testid='rk-auth-message-button']", timeout=3
            )
            if auth_button:
                auth_button.click()
                self._sign_okx_wallet()

                # 检查连接状态
                if self._check_wallet_connected(page):
                    return True

                raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

            btn_okx_wallet = page.ele(
                "x://div[contains(text(), 'OKX Wallet')]", timeout=3
            )
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            self._connect_okx_wallet()

            # Verify your account
            auth_button = page.ele(
                "x://button[@data-testid='rk-auth-message-button']", timeout=5
            )
            if auth_button:
                auth_button.click()
                self._sign_okx_wallet()

            # 检查连接状态
            if self._check_wallet_connected(page):
                return True

            raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    # https://x.com/xiaoyubtc/status/1856216176097145182
    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Unleash任务"""
        if self._check_task_completed():
            return True

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.browser.page)

            self.browser.open_url("https://badge.unleashprotocol.xyz/", new_tab=True)
            self.browser.open_url("https://verio.network/staking/", new_tab=True)

            # # 连接钱包
            # if not self._connect_wallet(self.browser.page):
            #     raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # # 定义任务列表
            # tasks = [
            #     (self._task_follow_twitter, "关注推特"),
            #     (self._task_fire_icon, "点击火焰图标"),
            #     (self._task_create_song, "创建歌曲"),
            #     (self._claim_badge, "领取徽章"),
            # ]

            # # 执行任务列表
            # for task_func, task_name in tasks:
            #     self._execute_task(task_func, task_name)

            # logger.success(f"{self.browser_id} Unleash任务完成")
            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Unleash任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Soloai任务失败")
