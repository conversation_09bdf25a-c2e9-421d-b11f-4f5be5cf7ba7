import random

from loguru import logger
from src.browsers import BrowserType
from retry import retry
from src.evm import ChainInfo
from src.utils.browser_config import BrowserConfigInstance
from src.evm.erc20_utils import get_web3
from src.evm.erc20_utils import get_raw_transaction, send_transaction
import time


class StorySoloaiDaily(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "soloai_daily"
        self.wallet_address = self.browser_config.evm_address
        self.private_key = self.get_private_key()

        self.soloai_contract_address = "******************************************"
        self.boost_data = "0xa66f42c0"
        self.transactions = [
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002430623536643937312d353732302d343932642d623132632d33333762316234366435386200000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002435656362363537322d343865662d343539342d626333332d66333130633534656364343900000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002438383961646139322d643965662d346131612d623933382d31663831343139623238343200000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002433353335656531302d393132362d346331662d393232382d30386533333138646364383500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002464366133356434652d396263382d346636332d393731392d64363235306636616534613700000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002462633263383864342d613934342d346132372d383633642d37636534323139333863613700000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002430346638366661352d373066612d343437662d393835362d30666666633031636638373900000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002433313131373437642d306532642d343639642d613263392d38386235616261343131363500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002462353164336366662d626566652d346337312d383964652d30616261333363343762616500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002431343433636537392d646332622d343665632d623132332d33666534643566383762336300000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002430373334633062642d636364642d346166332d623336382d38613837623736306334396500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002434366233363137302d353766392d343466642d623436392d33326364383335363064646500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002465393932636366382d633831662d343436322d613536612d31376434653766616438623700000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002464623733333162662d363036312d343938352d396161652d63663262626664356165366600000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002463653038616461382d613231632d346632312d383233632d30343230623637386661393500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002437303233363961652d393130342d343462642d386233302d39376335393562386334633900000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002437303233363961652d393130342d343462642d386233302d39376335393562386334633900000000000000000000000000000000000000000000000000000000",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033623062396431383730646365323963633533366534653232643161633166",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033616231396135306332363765373330356266336362653465366365383362",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033613062343235326431643038343865653136313030623866303936666566",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033396636333535383961356131393435326532333031336164313362393565",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033393632653736393964373432366134623261363066373964326566396366",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002465613337663362362d366165392d343831642d626434642d37303863386535393339316500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002439316365343930652d613461342d346430662d393239302d30653864613961303465663900000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002434636165356632652d646238652d343936652d383934622d31353864363634396637663300000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002466373162306166622d383131642d343261342d623561392d61306137323561336232356400000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002437326339306538622d343861622d346532632d613264332d36333931306563376561363600000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002463313633656336342d333237352d343230612d396432342d31303133393037383366383900000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002432346331666532662d383338632d346233312d386337612d33333466653362363931376200000000000000000000000000000000000000000000000000000000",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033616236393739373563366562343037383831363338633435626663336334",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033613863356437616436356264613365353731343935656634616163373733",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033393735306232636536653939626563636431326637396239616466656466",
            "0xec7f90a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000203033393637313633396333376339346235633736343537653439303566643938",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002465373965643631372d666466652d346464342d396330372d65653735376564633166383500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002431646663346662302d366133352d343037312d383466622d36393864646666653831353600000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002430663137323965342d323431372d343038342d396533332d32363233336264336335366200000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002462393539336464352d653337612d346331312d626561342d39343261626635373130326300000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002461316237313633622d386439312d343931662d386166332d33666331303362633934376500000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002463613034343265382d613163322d343033652d613861312d38663633626330653233366100000000000000000000000000000000000000000000000000000000",
            "0xec7f90a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002439623263343938392d653561312d346561362d383436342d34636438666266376563643700000000000000000000000000000000000000000000000000000000",
        ]

    # daily checkin
    def _daily_checkin(self) -> bool:
        logger.info("[INFO] 执行daily checkin")

        try:
            # 随机从transactions中选择一个
            transaction = random.choice(self.transactions)
            logger.info(f"[INFO] 选择执行的transaction: {transaction}")

            # 构建交易
            tx_params = {
                "from": self.wallet_address,
                "to": self.soloai_contract_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
                "data": transaction,
            }
            raw_transaction = get_raw_transaction(self.web3, tx_params)
            if not raw_transaction:
                logger.error(f"[ERROR] 构建交易失败")
                return False
            # 执行交易
            result = send_transaction(self.web3, raw_transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] daily checkin 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] daily checkin 异常: {e}")
            return False

    def _boost(self) -> bool:
        logger.info("[INFO] 执行boost")
        try:
            # 构建交易
            tx_params = {
                "from": self.wallet_address,
                "to": self.soloai_contract_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
                "data": self.boost_data,
            }
            raw_transaction = get_raw_transaction(self.web3, tx_params)
            if not raw_transaction:
                logger.error(f"[ERROR] 构建交易失败")
                return False
            # 执行交易
            result = send_transaction(self.web3, raw_transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] boost 交易成功")
            return result
        except Exception as e:
            logger.error(f"[ERROR] boost 异常: {e}")
            return False

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        """执行 soloai daily checkin 任务"""
        try:
            logger.info(
                f"[INFO] 开始执行 {self.browser_id} 的 soloai daily checkin 任务"
            )
            rpc_url = ChainInfo.STORY_ODYSSEY["rpc"]
            proxy = self._get_valid_proxy()
            self.web3 = get_web3(
                rpc_url, proxy, user_agent=self.browser_config.user_agent
            )
            if self.web3 is None:
                logger.error(f"[ERROR] 获取web3实例失败")
                return False
            logger.info(f"[INFO] 获取web3实例成功")

            # 1. boost 逻辑
            if not self._boost():
                logger.error(f"[ERROR] {self.browser_id} soloai boost 任务失败")
                return False

            # 2. daily checkin 逻辑
            if not self._daily_checkin():
                logger.error(f"[ERROR] {self.browser_id} soloai daily checkin 任务失败")
                return False

            # 随机延迟3-5s
            sleep_time = random.randint(5, 10)
            logger.info(f"[INFO] 随机延迟 {sleep_time} 秒")
            time.sleep(sleep_time)

            logger.success(
                f"[SUCCESS] {self.browser_id} soloai daily checkin 任务执行完成"
            )
            return True
        except Exception as e:
            logger.error(f"[ERROR] {self.browser_id} 任务失败: {e}")
            return False

    def close(self):
        pass
