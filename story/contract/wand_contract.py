from typing import Dict, Optional
from web3 import Web3
from loguru import logger
from .base_contract import BaseContract


# NFT相关合约
WAND_NFT_CONTRACT: str = "******************************************"
# 存款相关合约
WAND_DEPOSIT_CONTRACT: str = "******************************************"
# 代币合约
USB_TOKEN_CONTRACT: str = "******************************************"
# 合约方法ID
METHOD_IDS: Dict[str, str] = {
    "mint_nft": "0xb510391f",  # NFT铸造方法
    "deposit": "0x50eb741c",  # 存款方法
}


class WandContract(BaseContract):

    def _get_wand_nft_balance(self) -> bool:
        """获取NFT余额"""
        try:
            return self._get_nft_balance(WAND_NFT_CONTRACT)
        except Exception as e:
            logger.error(f"{self.id} 获取NFT余额失败: {str(e)}")
            return False

    def _get_usb_token_balance(self) -> float:
        """获取USB代币余额"""
        try:
            balance_info = self.web3_manager.check_token_balance(
                USB_TOKEN_CONTRACT, self.account_address
            )
            return balance_info["balance"]
        except Exception as e:
            logger.error(f"{self.id} 获取USB代币余额失败: {str(e)}")
            return 0

    def check_nft_minted(self) -> bool:
        """检查NFT是否已铸造"""
        return self._get_wand_nft_balance()

    def build_transaction_data(
        self, function_selector: str, wallet_address: str, signature: str
    ) -> str:
        """构建交易数据

        Args:
            function_selector: 函数选择器(带0x前缀)
            wallet_address: 钱包地址(带0x前缀)
            signature: 签名数据(带0x前缀)
        """
        try:
            # 移除所有0x前缀
            function_selector = function_selector.replace("0x", "")
            wallet_address = wallet_address.replace("0x", "")
            signature = signature.replace("0x", "")

            # 1. 函数选择器(4字节)
            if len(function_selector) != 8:  # 4字节 = 8个hex字符
                raise ValueError("函数选择器必须是4字节")

            # 2. 地址补齐到32字节
            padded_address = wallet_address.zfill(64)

            # 3. 动态数据位置指针(固定为0x40)
            dynamic_position = (
                "0000000000000000000000000000000000000000000000000000000000000040"
            )

            # 4. 签名数据长度(0x41 = 65字节)
            signature_length = (
                "0000000000000000000000000000000000000000000000000000000000000041"
            )

            # 5. 签名数据(补齐到32字节的倍数)
            padded_signature = signature.ljust(192, "0")

            # 拼接完整数据
            hex_data = (
                function_selector
                + padded_address
                + dynamic_position
                + signature_length
                + padded_signature
            )

            return "0x" + hex_data

        except Exception as e:
            raise Exception(f"构建交易数据失败: {str(e)}")

    def create_contract_call(self, amount, method_signature):
        """创建完整的合约调用数据

        Args:
            amount: 金额
            method_signature: 方法签名(带0x前缀)
        """
        try:
            # 确保方法签名格式正确
            if not method_signature.startswith("0x"):
                method_signature = "0x" + method_signature

            if len(method_signature) != 10:  # "0x" + 8个字符
                raise ValueError(f"{self.id} 方法签名必须是4字节（8个十六进制字符）")

            # 将金额转换为wei，然后转为16进制
            value_in_wei = Web3.to_wei(amount, "ether")
            hex_value = hex(value_in_wei)[2:].zfill(64)  # 补齐到64位

            # 拼接数据
            result = method_signature + hex_value
            return result

        except Exception as e:
            raise Exception(f"{self.id} 构建交易数据失败: {str(e)}")

    def mint_nft(self, signature: str, evm_address: str) -> bool:
        """铸造NFT"""
        if not all([self.private_key, self.rpc_url, signature]):
            raise ValueError(f"{self.id} 缺少必要参数")

        web3 = self.web3_manager.connect_to_network(
            rpc_url=self.rpc_url, proxy=self.proxy, user_agent=self.user_agent
        )
        if not web3:
            raise ValueError(f"{self.id} 连接网络失败")

        try:
            # 查询是否已经有NFT
            has_nft = self._get_wand_nft_balance()
            if has_nft:
                logger.success(f"{self.id} 已经铸造过NFT, 跳过合约调用")
                return True

            method_id = METHOD_IDS["mint_nft"]
            input_data = self.build_transaction_data(method_id, evm_address, signature)

            def _do_mint():
                return self._execute_contract_call(
                    contract_address=WAND_NFT_CONTRACT,
                    input_data=input_data,
                )

            success = self._execute_with_retry(_do_mint)
            if success:
                logger.success(f"{self.id} NFT铸造成功")
            return success

        except Exception as e:
            logger.error(f"{self.id} 铸造NFT失败: {str(e)}")
            return False

    def deposit(self, amount: float) -> bool:
        """调用代理合约, 实现存钱"""
        if not all([self.private_key, self.rpc_url, amount]):
            raise ValueError(f"{self.id} 缺少必要参数")

        web3 = self.web3_manager.connect_to_network(
            rpc_url=self.rpc_url, proxy=self.proxy, user_agent=self.user_agent
        )
        if not web3:
            raise ValueError(f"{self.id} 连接网络失败")

        try:
            account = web3.eth.account.from_key(self.private_key)
            balance = web3.eth.get_balance(account.address)
            required_amount = web3.to_wei(amount, "ether")

            # 查询USB代币余额
            usb_balance = self._get_usb_token_balance()
            if usb_balance > 0:
                logger.success(
                    f"{self.id} USB 代币余额: {usb_balance}, 已经存过钱, 跳过合约调用"
                )
                return True

            # 检查余额是否足够支付交易费用
            if balance < required_amount:
                logger.error(
                    f"{self.id} 账户余额不足: 需要 {amount} IP, 当前余额: {web3.from_wei(balance, 'ether')} IP"
                )
                return False

            method_id = METHOD_IDS["deposit"]
            input_data = self.create_contract_call(amount, method_id)
            value_in_wei = web3.to_wei(amount, "ether")

            def _do_deposit():
                return self._execute_contract_call(
                    contract_address=WAND_DEPOSIT_CONTRACT,
                    input_data=input_data,
                    value_in_wei=value_in_wei,
                )

            success = self._execute_with_retry(_do_deposit)
            if success:
                logger.success(f"{self.id} 存款交易成功")
            return success

        except Exception as e:
            logger.error(f"{self.id} 调用失败: {str(e)}")
            return False
