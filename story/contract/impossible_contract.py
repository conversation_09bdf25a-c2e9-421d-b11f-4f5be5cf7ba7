from typing import Dict, Optional
from web3 import Web3
from loguru import logger
from .base_contract import BaseContract
import random


# 合约地址
IMPOSSIBLE_BADGE_CONTRACT: str = "0x2323AFf23019Dc366e4446172A983E828338A06B"
FAUCET_CONTRACT: str = "0x1044F1F2e208e213FBdFfeEbDc4eE0d194307438"
SPENDER_ADDRESS = "0x7a63846d7c1f2718cd548df853746ca2e81b61d2"

# 代币信息
TOKEN_SYMBOL: str = "TIDIA"
TOKEN_DECIMALS: int = 18  # 假设精度是18，如果不是请调整


class ImpossibleContract(BaseContract):
    def _get_nft_balance(self) -> bool:
        """获取NFT余额"""
        try:
            balance = self.web3_manager.get_nft_balance(
                IMPOSSIBLE_BADGE_CONTRACT, self.account_address
            )
            return balance > 0
        except Exception as e:
            logger.error(f"{self.id} 获取NFT余额失败: {str(e)}")
            return False

    def check_nft_minted(self) -> bool:
        """检查NFT是否已铸造"""
        return self._get_nft_balance()

    def build_transaction_data(
        self, function_selector: str, wallet_address: str, signature: str
    ) -> str:
        """构建交易数据

        Args:
            function_selector: 函数选择器(带0x前缀)
            wallet_address: 钱包地址(带0x前缀)
            signature: 签名数据(带0x前缀)
        """
        try:
            # 移除所有0x前缀
            function_selector = function_selector.replace("0x", "")
            wallet_address = wallet_address.replace("0x", "")
            signature = signature.replace("0x", "")

            # 1. 函数选择器(4字节)
            if len(function_selector) != 8:  # 4字节 = 8个hex字符
                raise ValueError("函数选择器必须是4字节")

            # 2. 地址补齐到32字节
            padded_address = wallet_address.zfill(64)

            # 3. 动态数据位置指针(固定为0x40)
            dynamic_position = (
                "0000000000000000000000000000000000000000000000000000000000000040"
            )

            # 4. 签名数据长度(0x41 = 65字节)
            signature_length = (
                "0000000000000000000000000000000000000000000000000000000000000041"
            )

            # 5. 签名数据(补齐到32字节的倍数)
            padded_signature = signature.ljust(192, "0")

            # 拼接完整数据
            hex_data = (
                function_selector
                + padded_address
                + dynamic_position
                + signature_length
                + padded_signature
            )

            return "0x" + hex_data

        except Exception as e:
            raise Exception(f"构建交易数据失败: {str(e)}")

    def mint_nft(self, signature: str, wallet_address: str) -> bool:
        """铸造NFT

        Args:
            signature: 签名数据(带0x前缀)
        """
        if not all([self.private_key, self.rpc_url]):
            raise ValueError(f"{self.id} 缺少必要参数")

        try:
            # 检查是否已经有NFT
            has_nft = self._get_nft_balance()
            if has_nft:
                logger.success(f"{self.id} 已经铸造过NFT, 跳过合约调用")
                return True

            # 使用build_transaction_data构建调用数据
            input_data = self.build_transaction_data(
                "0xb510391f", wallet_address, signature
            )

            def _do_mint():
                return self._execute_contract_call(
                    contract_address=IMPOSSIBLE_BADGE_CONTRACT,
                    input_data=input_data,
                )

            success = self._execute_with_retry(_do_mint)
            if success:
                logger.success(f"{self.id} NFT铸造成功")
            return success

        except Exception as e:
            logger.error(f"{self.id} 铸造NFT失败: {str(e)}")
            return False

    def get_tidia_token_balance(self) -> int:
        """获取TIDIA代币余额"""
        return self._get_token_balance(FAUCET_CONTRACT)

    def mint_test_token(self) -> int:
        """领取TIDIA测试币"""
        if not all([self.private_key, self.rpc_url]):
            raise ValueError(f"{self.id} 缺少必要参数")

        try:
            current_balance = self._get_token_balance_wei(FAUCET_CONTRACT)
            if current_balance > 0:
                readable_balance = current_balance / 10**TOKEN_DECIMALS
                logger.success(
                    f"{self.id} 已有{TOKEN_SYMBOL}余额: {readable_balance} {TOKEN_SYMBOL}, 跳过领取"
                )
                return current_balance

            # 生成随机数量 (0.5-2.0 TIDIA)
            min_amount = 0.5
            max_amount = 2.0
            random_amount = round(random.uniform(min_amount, max_amount), 1)
            amount = int(random_amount * 10**TOKEN_DECIMALS)  # 转换为wei单位

            logger.info(f"{self.id} 准备领取 {random_amount} {TOKEN_SYMBOL}")

            # 构建调用数据
            input_data = (
                "0x40c10f19"  # mint_token方法ID
                + self.account_address[2:].zfill(64)  # to address
                + hex(amount)[2:].zfill(64)  # amount
            )

            def _do_mint_token():
                return self._execute_contract_call(
                    contract_address=FAUCET_CONTRACT,
                    input_data=input_data,
                )

            success = self._execute_with_retry(_do_mint_token)
            if success:
                logger.success(f"{self.id} 领取 {random_amount} {TOKEN_SYMBOL} 成功")
                return amount
            return 0

        except Exception as e:
            logger.error(f"{self.id} 领取{TOKEN_SYMBOL}测试币失败: {str(e)}")
            return 0

    def approve_token(self, amount_wei: int, spender_address: str) -> bool:
        """授权代币给指定地址

        Args:
            amount_wei: 授权数量(wei)
            spender_address: 被授权地址
        """
        try:
            # 构建授权数据
            approve_data = (
                "0x095ea7b3"  # approve方法ID
                + "000000000000000000000000"  # 补齐到32字节的前缀
                + spender_address[2:]  # spender地址(不带0x前缀)
                + hex(amount_wei)[2:].zfill(64)  # amount补齐到32字节
            )

            def _do_approve():
                return self._execute_contract_call(
                    contract_address=FAUCET_CONTRACT,
                    input_data=approve_data,
                )

            approve_success = self._execute_with_retry(_do_approve)
            if approve_success:
                logger.success(f"{self.id} 授权{TOKEN_SYMBOL}成功")
            else:
                logger.error(f"{self.id} 授权{TOKEN_SYMBOL}失败")
            return approve_success

        except Exception as e:
            logger.error(f"{self.id} 授权{TOKEN_SYMBOL}失败: {str(e)}")
            return False

    def _check_staked(self) -> bool:
        """检查是否已经质押"""

        return False

    def stake_token(self, amount_wei: int) -> bool:
        """质押TIDIA代币"""
        if not all([self.private_key, self.rpc_url]):
            raise ValueError(f"{self.id} 缺少必要参数")

        try:
            logger.info(f"========== {self.id} 开始质押 {TOKEN_SYMBOL} ==========")
            readable_amount = amount_wei / 10**TOKEN_DECIMALS
            logger.info(
                f"计划质押数量: {readable_amount} {TOKEN_SYMBOL} ({amount_wei} wei)"
            )

            # 检查是否已经质押
            is_staked = self._check_staked()
            if is_staked:
                logger.success(f"{self.id} 已经质押, 跳过质押")
                return True

            # 检查代币余额
            current_balance = self._get_token_balance_wei(FAUCET_CONTRACT)
            logger.info(
                f"当前余额: {current_balance / 10**TOKEN_DECIMALS} {TOKEN_SYMBOL} ({current_balance} wei)"
            )
            if current_balance < amount_wei:
                logger.error(
                    f"{self.id} {TOKEN_SYMBOL}余额不足: {current_balance} < {amount_wei}"
                )
                return False

            # 1. 授权质押合约
            approve_success = self.approve_token(amount_wei, SPENDER_ADDRESS)
            if not approve_success:
                logger.error(f"{self.id} 授权{TOKEN_SYMBOL}失败")
                return False

            logger.success(f"{self.id} 授权{TOKEN_SYMBOL}成功")

            # 2. 构建质押数据
            stake_data = (
                "0x770c5c12"  # stake方法ID
                + "0000000000000000000000000000000000000000000000000000000000000000"  # trackId = 0
                + hex(amount_wei)[2:].zfill(64)  # amount
            )

            def _do_stake():
                return self._execute_contract_call(
                    contract_address=SPENDER_ADDRESS,  # 在质押合约上调用stake
                    input_data=stake_data,
                )

            logger.info("开始执行质押交易...")
            stake_success = self._execute_with_retry(_do_stake)

            if stake_success:
                logger.success(f"{self.id} 质押 {readable_amount} {TOKEN_SYMBOL} 成功")
            else:
                logger.error(f"{self.id} 质押失败")

            logger.info(f"========== 质押操作结束 ==========")
            return stake_success

        except Exception as e:
            logger.error(f"{self.id} 质押{TOKEN_SYMBOL}失败: {str(e)}")
            logger.info(f"========== 质押操作异常结束 ==========")
            return False

    def claim_token(self) -> bool:
        """领取奖励"""
        pass
