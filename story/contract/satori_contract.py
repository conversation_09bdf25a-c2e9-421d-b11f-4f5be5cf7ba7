from story.contract.base_contract import BaseContract
from loguru import logger
import random
import time
from time import sleep

USDC_CONTRACT_ADDRESS = "0x71194200de8735a7d40cd62bdc89344d3ce598b2"
USDC_CONTRACT_ADDRESS_TEST = "0x70Db6f70345221eE6FEBa2AA67b1c46afEB9abab"
TOKEN_DECIMALS = 6
TOKEN_SYMBOL = "USDC"


class SatoriContract(BaseContract):

    def get_usdc_balance(self) -> float:
        """获取USDC余额"""
        try:
            balance_wei = self._get_token_balance_wei(USDC_CONTRACT_ADDRESS_TEST)
            balance = balance_wei / 10**TOKEN_DECIMALS
            return balance
        except Exception as e:
            logger.error(f"{self.id} 获取{TOKEN_SYMBOL}余额失败: {str(e)}")
            return 0

    def faucet_usdc_token(self):
        """领取测试币"""
        logger.debug(f"{self.id} 领取测试币")
        balance = self.get_usdc_balance()
        if balance > 0:
            logger.success(f"{self.id} 已领取测试币")
            return True
        logger.debug(f"{self.id} 测试币balance: {balance}")
        input_data = "0xe9ba77fb"

        def _do_mint_token():
            return self._execute_contract_call(
                contract_address=USDC_CONTRACT_ADDRESS,
                input_data=input_data,
            )

        success = self._execute_with_retry(_do_mint_token)
        if success:
            logger.success(f"{self.id} 领取测试币成功")
            return True
        return False

    # 授权USDC
    def approve_usdc(
        self, token_address: str, owner_address: str, spender: str, amount: int
    ):
        """授权USDC"""
        # 最大授权额度
        amount_wei = 2**256 - 1
        current_allowance = self.web3_manager.allowance(
            token_address, owner_address, spender
        )
        logger.debug(f"{self.id} 当前授权额度: {current_allowance / 10**6} USDC")
        logger.debug(f"{self.id} 需要授权额度: {amount * 10**6} USDC")
        if current_allowance >= amount * 10**6:
            logger.info(
                f"{self.id} USDC额度授权已足够: {current_allowance / 10**6} USDC"
            )
            return True

        return self.web3_manager.approve(token_address, spender, amount_wei)

    def deposit_usdc(self):
        logger.debug(f"{self.id} 存钱")
        # 等待10-20秒
        sleep(random.randint(10, 20))

        amount = random.choice([300, 400, 500])
        spender = "0x57b538a541618047244e08c205c1ccf3a0783e31"
        token_address = USDC_CONTRACT_ADDRESS_TEST

        try:
            success = self.approve_usdc(
                token_address=token_address,
                owner_address=self.account_address,
                spender=spender,
                amount=amount,
            )
            if not success:
                return False
            logger.success(f"{self.id} 授权USDC成功")
        except Exception as e:
            logger.error(f"{self.id} 授权USDC失败: {str(e)}")
            return False

        task_id = self._get_task_id()
        current_timestamp = int(time.time() * 1000 + random.randint(1, 999))
        amount_wei = int(amount * 10**6)
        try:
            input_data = (
                "0x72f66b67"
                + format(task_id, "064x")
                + format(current_timestamp, "064x")
                + token_address.lower()[2:].rjust(64, "0")
                + format(amount_wei, "064x")
            )
            logger.info(f"deposti_usdc: {input_data}")

            def _do_deposit():
                return self._execute_contract_call(
                    contract_address=spender,
                    input_data=input_data,
                )

            success = self._execute_with_retry(_do_deposit)
            if success:
                logger.success(f"{self.id} 存款交易成功, 金额: {amount} USDC")
            return success
        except Exception as e:
            logger.error(f"{self.id} 存款交易失败: {str(e)}")
            return False

    def _get_task_id(self) -> str:
        """获取任务ID"""
        snowflake = Snowflake(
            worker_id=1,
            datacenter_id=0,
            max_seq_number=63,
            min_seq_number=5,
            seq_bit_length=6,
            worker_id_bit_length=6,
            top_over_cost_count=2000,
        )
        return snowflake.get_id()


class Snowflake:
    def __init__(
        self,
        worker_id,
        datacenter_id,
        max_seq_number,
        min_seq_number,
        seq_bit_length,
        worker_id_bit_length,
        top_over_cost_count,
    ):
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.max_seq_number = max_seq_number
        self.min_seq_number = min_seq_number
        self.seq_bit_length = seq_bit_length
        self.worker_id_bit_length = worker_id_bit_length
        self.top_over_cost_count = top_over_cost_count
        self.sequence = self.min_seq_number
        self.last_timestamp = -1

    def _get_timestamp(self):
        return int(time.time() * 1000)

    def _til_nextMillis(self, last_timestamp):
        timestamp = self._get_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._get_timestamp()
        return timestamp

    def get_id(self):
        timestamp = self._get_timestamp()
        if timestamp < self.last_timestamp:
            raise Exception(
                "Clock moved backwards. Refusing to generate id for %d milliseconds"
                % (self.last_timestamp - timestamp)
            )

        if self.last_timestamp == timestamp:
            self.sequence = (self.sequence + 1) & self.max_seq_number
            if self.sequence < self.min_seq_number:
                timestamp = self._til_nextMillis(self.last_timestamp)
        else:
            self.sequence = self.min_seq_number

        self.last_timestamp = timestamp

        return self._generate_id(timestamp)

    def _generate_id(self, timestamp):
        return (
            (
                (timestamp - 1577836800000)
                << (self.worker_id_bit_length + self.seq_bit_length)
            )
            | (self.worker_id << self.seq_bit_length)
            | self.sequence
        )
