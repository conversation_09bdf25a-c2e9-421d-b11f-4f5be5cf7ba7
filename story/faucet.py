from retry import retry
from loguru import logger
from src.browsers import BrowserType
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from time import sleep
from src.utils.element_util import click_on_image
import json
from src.utils.hhcsv import HHCSV
from src.utils.common import get_project_root_path
import requests
from src.utils.proxies import Proxies
import warnings
import os

warnings.filterwarnings("ignore", message=".*InsecureRequestWarning.*")


logger.add("logs/faucetme.log", level="SUCCESS", rotation="100kb")


class StoryFaucet(StoryBase):
    """Story水龙头任务"""

    def __init__(self, index: int, type: BrowserType):
        super().__init__(type, str(index))
        self.project_name = "faucet"
        self.home_url = "https://faucet.story.foundation/"
        self.wallet_address = self.browser.browser_config.evm_address

        # 设置代理
        proxy = self._get_valid_proxy()
        self.proxies = Proxies(proxy).get_proxies()

    def _connect_wallet(self, tab) -> bool:
        """连接钱包"""
        try:
            connect_button = tab.ele(
                "x://button[@data-testid='rk-connect-button']", timeout=5
            )
            if not connect_button:
                return True

            connect_button.click()
            wallet_option = tab.ele(
                "x://button[@data-testid='rk-wallet-option-com.okex.wallet']", timeout=5
            )
            if not wallet_option:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            wallet_option.click()
            self._sign_okx_wallet()

            if not self._check_wallet_address(tab):
                raise WalletConnectionError(f"{self.browser_id} 链接钱包失败...")

            return True

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _check_wallet_address(self, tab) -> bool:
        """检查钱包地址"""
        address_ele = tab.ele("x://div[contains(text(), '0x')]", timeout=5)
        if not address_ele:
            logger.error(f"{self.browser_id} 未找到钱包地址")
            return False
        return True

    @retry(tries=3, delay=1)
    def _claim_token(self, tab) -> bool:
        """处理验证码"""
        try:
            # 检测是否已经有按钮
            claim_button = tab.ele(
                "x://button[contains(text(), 'Claim Tokens')]", timeout=10
            )
            if claim_button:
                logger.success(f"{self.browser_id} 验证码成功")
                claim_button.click()

                ele = tab.ele("@|text():Success@|text():You have already claimed")
                if ele:
                    return True

                raise TaskExecutionError(f"{self.browser_id} 领取失败")

            captcha_container = tab.ele("x://div[@id='captcha-container']", timeout=10)
            if not captcha_container:
                raise TaskExecutionError(f"{self.browser_id} 未找到验证码容器")

            iframe_body = (
                captcha_container.ele("tag:div").sr("tag:iframe").ele("tag:body")
            )
            if not iframe_body:
                raise TaskExecutionError(f"{self.browser_id} 未找到iframe body")

            checkbox = iframe_body.sr("x://input[@type='checkbox']", timeout=30)
            if not checkbox:
                raise TaskExecutionError(f"{self.browser_id} 未找到验证码输入框")

            try:
                checkbox.wait.has_rect(timeout=15)
                if os.name == 'nt':  # Windows
                    image_path = f"{get_project_root_path()}/story/img/check_box.jpg"
                else:
                    image_path = f"{get_project_root_path()}/story/img/check_box.png"
                click_on_image(image_path)
            except Exception as e:
                logger.error(f"{self.browser_id} 获取验证码坐标失败: {str(e)}")

            # 等待领取按钮出现
            if tab.wait.ele_displayed(
                "x://button[contains(text(), 'Claim Tokens')]", 30
            ):
                claim_button = tab.ele(
                    "x://button[contains(text(), 'Claim Tokens')]", timeout=5
                )
                claim_button.click()
                ele = tab.ele("@|text():Success@|text():You have already claimed")
                if ele:
                    return True

                raise TaskExecutionError(f"{self.browser_id} 领取失败")

            raise TaskExecutionError(f"{self.browser_id} 领取失败")

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 领取失败: {str(e)}")

    def _claim(self, tab) -> bool:
        """领取水龙头"""
        try:
            claim_button = tab.ele("x://button[contains(text(), 'Claim')]", timeout=5)
            if not claim_button or not claim_button.states.has_rect:
                raise TaskExecutionError(f"{self.browser_id} 未找到领取按钮")

            if not claim_button.states.is_clickable:
                raise TaskExecutionError(f"{self.browser_id} 领取按钮不可点击")

            claim_button.click()

            success_text = tab.ele(
                "x://p[contains(text(), 'No more requests today')]", timeout=5
            )
            if not success_text or not success_text.states.has_rect:
                raise TaskExecutionError(f"{self.browser_id} 领取失败")

            return True

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 领取失败: {str(e)}")

    @retry(tries=3, delay=1)
    def faucet(self) -> bool:

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开水龙头页面
            self.browser.open_url(self.home_url)
            try:
                self.page.latest_tab.set.window.max()
            except Exception as e:
                logger.error(f"{self.browser_id} 设置窗口最大化失败: {e}")

            # 连接钱包
            if not self._connect_wallet(self.page.latest_tab):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 处理验证码
            if not self._claim_token(self.page.latest_tab):
                raise TaskExecutionError(f"{self.browser_id} 验证码处理失败")

            # 更新任务状态
            logger.success(f"{self.browser_id} 领取成功")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise  # 重新抛出钱包连接错误
        except Exception as e:
            logger.error(f"{self.browser_id} 领取失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} 领取失败")

    def _dc_auth(self, tab) -> bool:
        """Discord授权"""
        # Log in with Discord
        login = tab.ele("Log in with Discord", timeout=5)
        login.wait.has_rect(timeout=5)
        login.click()

        if not tab.wait.ele_displayed("x:(//button[@type='button'])[2]", timeout=15):
            raise TaskExecutionError(f"{self.browser_id} 未找到授权按钮")

        authorize = tab.ele("x:(//button[@type='button'])[2]", timeout=5)
        authorize.wait.has_rect(timeout=5)
        authorize.click()

        for i in range(30):
            if "story.faucetme.pro" in tab.url:
                return True
            if "account.faucetme.pro" in tab.url:
                sleep(3)
                tab.wait.ele_deleted("Discord authorization", timeout=20)
                tab.get("https://story.faucetme.pro/")
                sleep(3)
                return True
            sleep(1)
        raise TaskExecutionError(f"{self.browser_id} 授权失败")

    def _save_dc_data(self, headers, postData):
        try:
            csv_path = f"{get_project_root_path()}/story/faucetme.csv"
            csv = HHCSV(
                csv_path,
                headers=["id", "headers", "postdata", "address"],
            )
            return csv.update_row(
                criteria={"id": str(self.browser_id)},
                updates={"headers": headers, "postdata": postData},
            )
        except Exception as e:
            logger.error(f"{self.browser_id} 保存DC数据失败: {e}")
            return False

    def _save_request_data(self, tab) -> bool:
        """保存请求数据"""
        try:
            login = tab.ele("Log in with Discord", timeout=5)
            if login and login.states.has_rect:
                self._dc_auth(tab)

            address_input = tab.ele(
                "x:(//input[@placeholder='Enter your wallet address'])[2]", timeout=20
            )
            if not address_input:
                self._dc_auth(tab)

            address_input.input(self.wallet_address)
            tab.listen.start("https://api.story.faucetme.pro/wallet/request-token")

            tab.ele(
                "x:(//label[text()='Send me']/parent::button)[2]", timeout=5
            ).click()
            res = tab.listen.wait(timeout=30)
            if res:
                headers = json.dumps(dict(res.request.headers))
                postData = json.dumps(res.request.postData)
                # 填充到fauceme.csv文件中
                result = self._save_dc_data(headers, postData)
                if result:
                    logger.success(f"{self.browser_id} 保存DC数据成功")
                else:
                    logger.error(f"{self.browser_id} 保存DC数据失败")
        except Exception as e:
            logger.error(f"{self.browser_id} 保存DC数据失败: {e}")
            return False

    @retry(tries=3, delay=1)
    def update_faucetme_data(self) -> bool:

        tab = self.page.new_tab("https://story.faucetme.pro/")
        sleep(5)
        try:
            self._save_request_data(tab)
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 领取失败")
        finally:
            self.page.quit()

    @retry(tries=10, delay=1)
    def faucet_me(self):
        """领取faucetme水龙头"""
        try:
            # 从CSV文件获取认证数据
            csv_path = f"{get_project_root_path()}/story/faucetme.csv"
            csv = HHCSV(csv_path)
            rows = csv.query({"id": str(self.browser_id)})
            if len(rows) == 0:
                return False

            row = rows[0]
            if not row or not row.get("headers") or not row.get("postdata"):
                raise TaskExecutionError(f"{self.browser_id} 未找到认证数据")

            # 解析存储的headers和postdata
            try:
                headers = json.loads(row["headers"])
                data = json.loads(row["postdata"])

                if not headers or not data:
                    return False

                # 清理headers，移除HTTP/2特殊字段
                cleaned_headers = {}
                for key, value in headers.items():
                    if not key.startswith(":"):  # 跳过HTTP/2伪头部字段
                        cleaned_headers[key] = value

                # 更新钱包地址
                data["address"] = self.wallet_address

                response = requests.post(
                    "https://api.story.faucetme.pro/wallet/request-token",
                    headers=cleaned_headers,
                    json=data,
                    proxies=self.proxies,
                )

                if response.status_code == 200:
                    logger.success(
                        f"{self.browser_id} faucetme领取成功 {response.text}"
                    )
                    return True
                else:
                    if "amount" in response.text:
                        logger.success(f"{self.browser_id} faucetme 领取成功")
                        return True

                    if "You have already claimed" in response.text:
                        logger.success(f"{self.browser_id} faucetme 已经领取过了")
                        return True

                    logger.info(f"{self.browser_id} faucetme 领取失败: {response.text}")
                    sleep(3)
                    raise TaskExecutionError(
                        f"{self.browser_id} faucetme 领取失败: {response.text}"
                    )

            except json.JSONDecodeError as e:
                raise TaskExecutionError(
                    f"{self.browser_id} 解析认证数据失败: {str(e)}"
                )
            except Exception as e:
                raise TaskExecutionError(f"{self.browser_id} faucetme领取失败")

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} faucetme领取失败")
