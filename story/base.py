import requests
import urllib3
from abc import ABC, abstractmethod
from typing import Optional
from src.controllers import BrowserController
from src.browsers import BrowserType
from loguru import logger
from .repository import story_repository
from .contract.nft_utils import NFTContract
from src.utils.proxies import Proxies
import os
from story.gas_utils import GasTracker
from src.enums.wallet_enums import GasLevel
from config import PROXY_URL, MAX_GAS_PRICE
from retry import retry
from time import sleep
from src.utils.secure_encryption import SecureEncryption

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class StoryBase(ABC):
    """Story项目的基类，处理通用的浏览器操作和数据存储

    Attributes:
        browser: 浏览器控制器实例
        browser_id: 浏览器ID
        project_name: 项目名称，由子类设置
    """

    def __init__(self, type: BrowserType, id: str):
        self.browser = BrowserController(type, str(id))
        self.browser_id = str(id)
        self.project_name: str = ""  # 子类需要设置项目名称
        self.home_url: str = ""  # 子类需要设置主页URL
        self.badge_contract_address: str = ""  # 子类需要设置徽章合约地址
        self._nft_contract: Optional[NFTContract] = None

    @abstractmethod
    def _connect_wallet(self, page) -> bool:
        """连接钱包的抽象方法，由子类实现具体逻辑"""
        pass

    def _login_wallet(self) -> bool:
        """登录 OKX 钱包"""
        try:
            result = self.browser.okx_wallet_login()
            if not result:
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")
            return True
        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 登录钱包失败: {str(e)}")

    @retry(tries=3, delay=1)
    def _verify_proxy(self, proxy):
        """验证代理有效性"""
        try:
            if not proxy:
                return False
            return Proxies(proxy).verify()
        except Exception as e:
            logger.error(f"{self.browser_id} 代理验证失败: {str(e)}")
            return False

    def _get_valid_proxy(self):
        """获取有效代理"""
        proxy = self.browser.browser_config.proxy
        if self._verify_proxy(proxy):
            return proxy

        proxy = {"http": PROXY_URL}
        if self._verify_proxy(proxy):
            return proxy

        raise WalletConnectionError(f"{self.browser_id} 无法获取有效代理")

    def _badge_mint(self, signature: str) -> bool:
        """铸造徽章"""
        try:
            nft_contract = self.get_nft_contract()

            # 使用build_transaction_data构建调用数据
            input_data = nft_contract.build_transaction_data(
                "0xb510391f",  # mint方法ID
                self.browser.browser_config.evm_address,
                signature,
            )

            success = nft_contract.write_contract(
                self.badge_contract_address,
                input_data,
            )

            if success:
                logger.success(f"{self.browser_id} NFT铸造成功")
                self._update_task_status(True)
            else:
                logger.error(f"{self.browser_id} NFT铸造失败")

            return success

        except Exception as e:
            logger.error(f"{self.browser_id} NFT铸造异常: {str(e)}")
            return False

    def get_private_key(self) -> Optional[str]:
        pk = self.browser.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        return pk

    def get_nft_contract(self) -> NFTContract:
        """获取NFT合约"""
        if self._nft_contract:
            return self._nft_contract

        rpc_url = os.getenv("RPC_URL")
        private_key = self.get_private_key()
        proxy = self._get_valid_proxy()

        user_agent = self.browser.browser_config.user_agent
        nft_contract = NFTContract(
            self.browser_id, rpc_url, private_key, proxy, user_agent
        )
        self._nft_contract = nft_contract
        return nft_contract

    def _get_task_status(self) -> Optional[bool]:
        """获取任务状态"""
        if not self.project_name:
            raise ValueError("project_name 未设置")
        return story_repository.get_status(self.browser_id, self.project_name)

    def _update_task_status(self, status: bool = True) -> bool:
        """更新任务状态"""
        if not self.project_name:
            raise ValueError("project_name 未设置")
        return story_repository.update_status(
            self.browser_id, self.project_name, status
        )

    def _update_balance(self, balance: float) -> bool:
        """更新余额"""
        if not self.project_name:
            raise ValueError("project_name 未设置")
        return story_repository.update_balance(self.browser_id, balance)

    def _check_task_completed(self) -> bool:
        """检查任务是否已完成"""
        status = self._get_task_status()
        if status:
            logger.success(f"{self.browser_id} {self.project_name}任务已完成")
            return True
        return False

    def _check_badge_minted(self) -> bool:
        """检查徽章任务是否铸造成功"""
        nft_utils = self.get_nft_contract()
        return nft_utils.is_minted(self.badge_contract_address)

    def _close_other_tabs(self, page):
        """关闭其他标签页"""
        try:
            page.close_tabs(others=True)
        except Exception as e:
            logger.error(f"{self.browser_id} 关闭标签页失败: {e}")

    def _sign_okx_wallet(self) -> bool:
        """钱包签名"""
        try:
            return self.browser.okx_wallet_sign()
        except Exception as e:
            logger.error(f"{self.browser_id} 钱包签名失败: {e}")
            return False

    def _approve_okx_wallet(
        self, gas_level: GasLevel = GasLevel.SLOW, gas_limit: int = MAX_GAS_PRICE
    ) -> bool:
        """钱包授权"""
        try:
            # 等待gas价格低于指定值
            for _ in range(60):
                gas_prices = self.get_gas_price()
                if gas_prices and gas_prices.average <= gas_limit:
                    break
                sleep(1)
            else:
                logger.warning(f"{self.browser_id} gas价格高于1500, 跳过combo")
                return False

            return self.browser.okx_wallet_approve(gas_level)
        except Exception as e:
            logger.error(f"{self.browser_id} 钱包授权失败: {e}")
            return False

    def _connect_okx_wallet(self) -> bool:
        """连接钱包"""
        try:
            return self.browser.okx_wallet_connect()
        except Exception as e:
            logger.error(f"{self.browser_id} 钱包签名失败: {e}")
            return False

    def close(self):
        """关闭浏览器页面"""
        try:
            self.browser.close_page()
        except Exception as e:
            logger.error(f"{self.browser_id} 关闭页面失败: {e}")

    @property
    def page(self):
        """获取页面"""
        return self.browser.page

    def get_gas_price(self) -> bool:
        """获取gas价格"""
        gas_tracker = GasTracker()
        # 获取当前gas价格
        gas_prices = gas_tracker.get_gas_prices()
        logger.info(
            f"gas价格, 高{gas_prices.fast}, 中{gas_prices.average}, 低{gas_prices.slow}"
        )
        return gas_prices

    def _make_post_request(
        self, url: str, headers: dict = None, data: dict = None
    ) -> tuple[bool, dict]:
        """发送POST请求的通用方法

        Args:
            url: 请求地址
            headers: 请求头，将覆盖默认headers，可选
            data: 请求数据，可选

        Returns:
            (success, response_data)
        """
        try:
            request_headers = {
                "accept": "*/*",
                "content-type": "application/json",
                "accept-language": "en-US,en;q=0.9",
                "referer": self.home_url,
            }

            user_agent = self.browser.browser_config.user_agent
            if user_agent:
                request_headers["user-agent"] = user_agent

            if headers:
                request_headers.update(headers)

            proxies = None
            if proxy := self.browser.browser_config.proxy:
                proxies = {"http": proxy, "https": proxy}

            response = requests.post(
                url=url,
                json=data if data else {},
                headers=request_headers,
                proxies=proxies,
                verify=False,
            )

            if response.status_code == 200:
                return True, response.json()

            return False, {"error": response.text}

        except Exception as e:
            logger.error(f"{self.browser_id} {self.project_name} 请求异常: {str(e)}")
            return False, {"error": str(e)}

    def _make_get_request(
        self, url: str, headers: dict = None, params: dict = None
    ) -> tuple[bool, dict]:
        """发送GET请求的通用方法"""
        try:
            request_headers = {
                "accept": "*/*",
                "content-type": "application/json",
                "accept-language": "en-US,en;q=0.9",
                "referer": self.home_url,
            }

            user_agent = self.browser.browser_config.user_agent
            if user_agent:
                request_headers["user-agent"] = user_agent

            if headers:
                request_headers.update(headers)

            proxies = None
            if proxy := self.browser.browser_config.proxy:
                proxies = {"http": proxy, "https": proxy}

            response = requests.get(
                url=url,
                params=params if params else {},
                headers=request_headers,
                proxies=proxies,
                verify=False,
            )

            if response.status_code == 200:
                return True, response.json()

            return False, {"error": response.text}

        except Exception as e:
            logger.error(f"{self.browser_id} {self.project_name} 请求异常: {str(e)}")
            return False, {"error": str(e)}


class StoryException(Exception):
    """Story项目的基础异常类"""

    pass


class WalletConnectionError(StoryException):
    """钱包连接相关的异常"""

    pass


class TaskExecutionError(StoryException):
    """任务执行相关的异常"""

    pass
