badges = [
    {
        "title": "Wand Protocol",
        "url": "https://explorer.story.foundation/collections/0xfe5c197BAa5eD96a8A99689Be1A7F7b941E58Dd3",
    },
    {
        "title": "D3X",
        "url": "https://explorer.story.foundation/collections/0x1Ce5AA796437A0fDD0127419a8517EfbD87aadF5",
    },
    {
        "title": "Nightly Odyssey Badge",
        "url": "https://explorer.story.foundation/collections/0xAE3a3D9A209780c1CfcdBc09178E18711Af0d9dF",
    },
    {
        "title": "Satori Badge",
        "url": "https://explorer.story.foundation/collections/0xCD62DbCB84Fcf7837FC813139A0D8F25b54033D9",
    },
    {
        "title": "Solo",
        "url": "https://explorer.story.foundation/collections/0x7ab31e9355e821629D19F6Dcd3a3a45AF674D67E",
    },
    {
        "title": "Color",
        "url": "https://explorer.story.foundation/collections/0xA9f7E75cBA8222A9096d50568FD001fB966cA4Cd",
    },
    {
        "title": "Playarts",
        "url": "https://explorer.story.foundation/collections/0x05a88df85657109A7675490A38f51C5425827beB",
    },
    {
        "title": "Mahojin IP-Badge",
        "url": "https://explorer.story.foundation/collections/0x3Eb5Ebaf49512ef134470263Dc4b04732dcEF4Bf",
    },
    {
        "title": "1Combo Story Badge",
        "url": "https://explorer.story.foundation/collections/0x5884F2396A8D5978b6A9fEC42a39ACF01A00C665",
    },
    {
        "title": "Blockbook Badge",
        "url": "https://explorer.story.foundation/collections/0x916D9366dCC642D45075C7a62daef00b14A7460f",
    },
    {
        "title": "Rightsfually Protection Badge",
        "url": "https://explorer.story.foundation/collections/0x928099c9dF3AcFD56bFcf01b6E235B30153a78",
    },
    {
        "title": "StoryHunt Badge",
        "url": "https://explorer.story.foundation/collections/0x22c3772f45268A68470Db15f4F73EC0310Ecac85",
    },
    {
        "title": "Story x OKX Odyssey Badge",
        "url": "https://explorer.story.foundation/collections/0xb21aF4ebde92fB3BCaB496BAE8d7c4dE362B4a42",
    },
    {
        "title": "Sekai Story Protocol Badge",
        "url": "https://explorer.story.foundation/collections/******************************************",
    },
    {
        "title": "Infinite Seas Badge",
        "url": "https://explorer.story.foundation/collections/******************************************",
    },
]


import random
from time import sleep, time
from typing import Optional

from loguru import logger
from retry import retry

from src.browsers import BrowserType
from .base import StoryBase, WalletConnectionError, TaskExecutionError


class StoryBadges(StoryBase):
    """徽章任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "bian"
        self.home_url = "https://odyssey.1combo.io/"
        self.wallet_address = self.browser.browser_config.evm_address

    def _connect_wallet(self, page) -> bool:
        return False

    def balance(self):
        nft_tools = self.get_nft_contract()
        balance = nft_tools.get_balance()
        return balance

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行任务"""
        nft_tools = self.get_nft_contract()

        balance = nft_tools.get_balance()
        self._update_balance(balance)

        contract_address = "******************************************"
        result = nft_tools.is_minted(contract_address)
        logger.info(
            f"ID {self.browser_id} {self.wallet_address} 币安钱包徽章铸造状态: {result}"
        )
        if result:
            self._update_task_status()

        # for badge in badges:
        #     contract_address = badge["url"].split("/")[-1]
        #     result = nft_tools.is_minted(contract_address)
        #     if result:
        #         logger.success(
        #             f"{self.browser_id} {self.wallet_address} {badge['title']}徽章已铸造"
        #         )
        #         count = count + 1
        #     else:
        #         logger.error(
        #             f"{self.browser_id} {self.wallet_address} {badge['title']}徽章未铸造"
        #         )
        #     sleep(1)
        # balance = nft_tools.get_balance()
        # logger.success(f"{self.browser_id} {self.wallet_address}  余额: {balance}")
        # self._update_balance(balance)
