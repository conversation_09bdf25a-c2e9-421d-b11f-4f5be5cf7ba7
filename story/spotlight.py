from loguru import logger
from time import sleep, time
from retry import retry
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from src.utils.common import generate_username
import random


class StorySpotlight(StoryBase):
    """Spotlight任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "spotlight"
        self.home_url = "https://app.spotlightprotocol.com/badge"
        self.mint_url = "https://app.spotlightprotocol.com/be-spotlighted"
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = page.ele(
                "x://button[@aria-haspopup='menu']",
                timeout=5,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        return True

    def _login(self, page):
        if self._check_wallet_connected(page):
            logger.debug(f"{self.browser_id} 已登录")
            return True

        timeout = 30
        start_time = time()
        while time() - start_time <= timeout:
            try:
                page("text=Log In", timeout=2).click()
                sleep(2)
            except Exception as e:
                logger.debug(f"{self.browser_id} 未找到Log In 按钮，retry...")

            try:
                page(
                    "x://html/body/div[1]/div/div/main/section/div[1]/button", timeout=2
                ).click()
                sleep(2)
            except Exception as e:
                logger.debug(f"{self.browser_id} 未找到Connect按钮，retry...")

            try:
                page("x://div[text()='MetaMask']", timeout=2).click()
                sleep(3)
                self._connect_okx_wallet()
            except Exception as e:
                logger.debug(f"{self.browser_id} 未找到MetaMask按钮，retry...")

            try:
                page.ele(
                    "x://button[@data-testid='rk-auth-message-button']", timeout=2
                ).click()
                sleep(3)
                self._sign_okx_wallet()
            except Exception as e:
                logger.debug(f"{self.browser_id} 未找到Sign message按钮，retry...")

            try:
                page(
                    "x://html/body/div[1]/div/div/main/section/div[2]/button", timeout=2
                ).click()
                sleep(2)
            except Exception as e:
                logger.debug(f"{self.browser_id} 未找到Connect按钮，retry...")

            try:
                page("x://input[@value='Authorize app']", timeout=2).click()
                sleep(5)
            except Exception as e:
                pass

            if self._check_wallet_connected(page):
                logger.debug(f"{self.browser_id} 登录成功")
                return True
        return False

    def _mint_task(self, page, task_name):
        logger.debug(f"{self.browser_id} 开始执行 {task_name}")
        task_btn = page(f"x://a[text()='{task_name}']", timeout=5)
        if not task_btn:
            raise WalletConnectionError(f"{self.browser_id} 查找{task_name}失败")
        task_btn.click()
        sleep(2)

        last_tab = page.latest_tab
        if task_name == "Join a Spotlight":
            self._create_spotlight(last_tab)

        last_tab("x://input[@id='name']").input(generate_username())
        last_tab("x://input[@id='description']").input(generate_username())
        last_tab.ele("Mint Now").click()
        self._sign_okx_wallet()
        self._sign_okx_wallet()
        self._sign_okx_wallet()
        self._approve_okx_wallet(gas_limit=2000)
        last_tab.close()

    def _create_spotlight(self, page):
        dialog = page(
            "x://button[@class='chakra-modal__close-btn css-cpyrmn']", timeout=5
        )
        if dialog:
            dialog.click()

        create = page("Create Spotlight", timeout=5)
        if create:
            create.click()

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Spotlight任务"""
        if self._check_task_completed():
            return False

        # 检查NFT是否已铸造
        nft_contract = self.get_nft_contract()
        if nft_contract.is_minted(self.badge_contract_address):
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            return False

        balance = nft_contract.get_balance()
        if balance < 6:
            logger.warning(f"{self.browser_id} 余额{balance}不足，跳过铸造")
            return False

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.page)

            if not self._login(self.page):
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            task_names = ["Mint a Spotlight", "Join a Spotlight"]
            for task_name in task_names:
                self._mint_task(self.page, task_name)
                sleep(2)

            # FIXME: 铸造徽章

            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Spotlight任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Spotlight任务失败")

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        parent_ids = [
            "******************************************",
            "****************************************** ",
            "******************************************",
            "******************************************",
            "******************************************",
            "******************************************",
            "******************************************",
            "******************************************",
            "******************************************",
            "******************************************",
        ]
        parent_id = random.choice(parent_ids)
        url = f"https://app.spotlightprotocol.com/be-spotlighted?parent_id={parent_id}"
        self.browser.open_url(url)
        sleep(2)
        last_tab = self.page.latest_tab
        last_tab("x://input[@id='name']").input(generate_username())
        last_tab("x://input[@id='description']").input(generate_username())
        last_tab.ele("Mint Now").click()
        self._sign_okx_wallet()
        self._sign_okx_wallet()
        self._sign_okx_wallet()
        self._approve_okx_wallet(gas_limit=2000)
        last_tab.close()
