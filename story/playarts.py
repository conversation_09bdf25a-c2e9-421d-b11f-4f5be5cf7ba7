import random
from typing import Optional
from loguru import logger
from time import sleep
from retry import retry

from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from config import MAX_GAS_PRICE


BADGE_NFT_CONTRACT: str = "******************************************"


class StoryPlayarts(StoryBase):
    """Playarts任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "playarts"
        self.home_url = "https://playarts.ai/"
        self.wallet_address = self.browser.browser_config.evm_address

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = page.ele(
                "x://div[@class='walletInfo']",  # 需要根据实际页面调整
                timeout=5,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        """连接钱包

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 是否连接成功
        """
        try:
            result = self._check_wallet_connected(page)
            if result:
                logger.success(f"{self.browser_id} 钱包已连接")
                return True

            # 点击连接钱包按钮
            connect_btn = page.ele(
                "x://button[contains(text(), 'Connect wallet')]",  # 需要根据实际页面调整
                timeout=5,
            )
            if not connect_btn:
                logger.error(f"{self.browser_id} 未找到连接钱包按钮")
                return False

            connect_btn.click()
            sleep(2)
            page.ele(
                "x://button[text()='Connect wallet' and contains(@class,'btn-A')]"
            ).click()

            OKX_JS = """
            document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-connect-view").shadowRoot.querySelector("wui-flex > wui-flex > w3m-connector-list").shadowRoot.querySelector("wui-flex > w3m-connect-injected-widget").shadowRoot.querySelector("wui-flex > wui-list-wallet:nth-child(1)").click()
            """
            page.run_js(OKX_JS)
            sleep(3)

            result = self._connect_okx_wallet()
            if not result:
                logger.error(f"{self.browser_id} 连接钱包失败")
                return False

            SWITCH_NETWORK_JS = """
            document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-unsupported-chain-view").shadowRoot.querySelector("wui-flex > wui-flex:nth-child(2) > wui-list-network:nth-child(1)").shadowRoot.querySelector("button").click()
            """
            page.run_js(SWITCH_NETWORK_JS)
            sleep(3)
            # 检查是否连接成功
            if not self._check_wallet_connected(page):
                logger.error(f"{self.browser_id} 连接钱包失败")
                return False

            logger.success(f"{self.browser_id} 连接钱包成功")
            return True

        except Exception as e:
            logger.error(f"{self.browser_id} 连接钱包异常: {str(e)}")
            return False

    def _login_wallet_register(self, page) -> bool:
        """注册页面登录钱包"""
        try:
            sleep(1)
            page.ele(
                "x://button[text()='Wallet connect']",
                timeout=5,
            ).click()

            page.ele(
                "x://div[contains(@class, 'modal')]//button[text()='Connect wallet']",
                timeout=5,
            ).click()

            OKX_LOGIN_JS = """
            document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-connect-view").shadowRoot.querySelector("wui-flex > wui-flex > w3m-connector-list").shadowRoot.querySelector("wui-flex > w3m-connect-injected-widget").shadowRoot.querySelector("wui-flex > wui-list-wallet:nth-child(1)").click()
            """
            page.run_js(OKX_LOGIN_JS)

            result = self._connect_okx_wallet()
            if not result:
                logger.error(f"{self.browser_id} 连接钱包失败")
                return False

            ele = page.ele("x://input[@id='email']", timeout=5)
            return bool(ele)

        except Exception as e:
            logger.error(f"{self.browser_id} 注册页面登录钱包异常: {str(e)}")
            return False

    def _request_get_signature(self, address: str) -> Optional[str]:
        """获取签名"""
        try:
            url = f"{self.home_url}api/signature"
            data = {"address": address}

            success, response_data = self._make_post_request(url=url, data=data)
            if not success:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data.get('error')}"
                )
                return False

            if "signature" in response_data:
                return response_data["signature"]
            elif "error" in response_data:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data['error']}"
                )
                return None

            return None

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 获取签名异常: {str(e)}"
            )
            return False

    def register(self, page) -> bool:
        """注册"""
        lasted_page = page.new_tab("https://app-alpha.playarts.ai/")
        sleep(3)

        login_btn = lasted_page.ele(
            "x://button[text()='LOGIN']",
            timeout=5,
        )
        if login_btn:
            login_btn.click()
            if not self._login_wallet_register(lasted_page):
                logger.error(f"{self.browser_id} 注册页面登录钱包失败")
                raise WalletConnectionError(f"{self.browser_id} 注册页面登录钱包失败")

            lasted_page.ele("x://input[@id='email']", timeout=5).input(
                self.browser.browser_config.email
            )
            lasted_page.ele("x://button[text()='Confirm']", timeout=5).click()

        wallet_info = lasted_page.ele(
            "x://div[contains(@class,'wallet_info')]",
            timeout=5,
        )
        if not wallet_info:
            logger.error(f"{self.browser_id} 注册页面钱包信息获取失败")
            raise WalletConnectionError(f"{self.browser_id} 注册页面钱包信息获取失败")

        return True

    def generate_random_prompt(self) -> str:
        """
        Randomly generate prompts for PlayArts AI
        Returns:
            str: Generated prompt in English
        """
        # Locations
        locations = [
            "magical forest",
            "futuristic city",
            "ancient temple",
            "crystal palace",
            "space station",
            "fairy tale village",
            "underwater kingdom",
            "desert oasis",
            "floating city",
            "ice castle",
            "cyberpunk metropolis",
            "steampunk workshop",
            "enchanted garden",
            "mystical cave",
            "alien planet",
        ]

        # Time & Atmosphere
        atmospheres = [
            "at sunrise",
            "during sunset",
            "under moonlight",
            "in starlight",
            "after rain",
            "in morning mist",
            "on a sunny day",
            "during storm",
            "under northern lights",
            "in golden hour",
            "in neon lights",
            "in cosmic nebula",
            "in ethereal fog",
        ]

        # Art Styles
        styles = [
            "watercolor art",
            "oil painting style",
            "digital art",
            "concept art",
            "fantasy art",
            "photorealistic",
            "studio ghibli style",
            "cyberpunk style",
            "steampunk",
            "impressionist",
            "anime style",
            "3d rendering",
            "baroque style",
            "minimalist",
            "pop art",
        ]

        # Quality & Effects
        effects = [
            "cinematic lighting",
            "volumetric lighting",
            "dramatic atmosphere",
            "highly detailed",
            "8k uhd",
            "professional photography",
            "hyper realistic",
            "trending on artstation",
            "unreal engine 5",
            "octane render",
            "ray tracing",
            "ambient occlusion",
            "depth of field",
            "bokeh effect",
        ]

        # Additional Details
        details = [
            "intricate details",
            "rich colors",
            "dynamic composition",
            "atmospheric perspective",
            "rule of thirds",
            "golden ratio",
            "dramatic shadows",
            "soft lighting",
            "high contrast",
            "vivid colors",
            "masterpiece",
            "best quality",
        ]

        # Combine elements randomly
        prompt = (
            f"{random.choice(locations)} {random.choice(atmospheres)}, "
            f"{random.choice(styles)}, {random.choice(effects)}, "
            f"{random.choice(details)}, {random.choice(effects)}, "
            f"masterpiece, best quality, highly detailed"
        )

        return prompt

    def create_image(self, page) -> bool:
        """访问 app-alpha 页面并完成任务"""
        try:
            # 打开新标签页访问 app-alpha
            lasted_page = page.new_tab(
                "https://app-alpha.playarts.ai/generate/txt_img_to_artwork"
            )
            sleep(3)

            wallet_info = lasted_page.ele(
                "x://div[contains(@class,'wallet_info')]",
                timeout=5,
            )
            if not wallet_info:
                logger.error(f"{self.browser_id} 钱包信息获取失败")
                raise WalletConnectionError(f"{self.browser_id} 钱包信息获取失败")

            textarea = lasted_page.ele(
                "x://textarea",
                timeout=5,
            )
            textarea.input(self.generate_random_prompt())

            lasted_page.ele(
                "x://button[text()='Generate']",
                timeout=5,
            ).click()
            return True

        except Exception as e:
            logger.error(f"{self.browser_id} Alpha页面任务失败: {str(e)}")
            return False

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Playarts任务"""
        if self._check_task_completed():
            return True

        # 检查NFT是否已铸造
        nft_contract = self.get_nft_contract()
        if nft_contract.is_minted(BADGE_NFT_CONTRACT):
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            return False

        # gas低了再启用
        gas_prices = self.get_gas_price()
        if gas_prices and gas_prices.average > MAX_GAS_PRICE:
            logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
            return False

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.page)

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 注册
            try:
                self.register(self.browser.page)
            except Exception as e:
                logger.error(f"{self.browser_id} 注册失败: {e}")

            # 创建图片
            try:
                self.create_image(self.browser.page)
            except Exception as e:
                logger.error(f"{self.browser_id} 创建图片失败: {e}")

            try:
                self.browser.page.ele("x://button[text()='Follow']", timeout=5).click()
                sleep(1)

            except Exception:
                pass

            try:
                self.browser.page.ele("x://button[text()='RT']", timeout=5).click()
                sleep(1)
            except Exception:
                pass

            try:
                self.browser.page.ele("x://button[text()='Join']", timeout=5).click()
                sleep(1)
            except Exception:
                pass

            self._close_other_tabs(self.page)
            self.page.refresh()

            signature = self._request_get_signature(
                self.browser.browser_config.evm_address
            )
            if not signature:
                logger.error(f"{self.browser_id} 获取签名失败, 说明任务还未完成")
                return False

            # 添加mint NFT的调用
            if not self.mint_nft(signature):
                return False

            logger.success(f"{self.browser_id} Playarts任务完成")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Playarts任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Playarts任务失败")

    def mint_nft(self, signature: str) -> bool:
        """铸造NFT"""
        try:
            nft_contract = self.get_nft_contract()

            # 使用build_transaction_data构建调用数据
            input_data = nft_contract.build_transaction_data(
                "0xb510391f",  # mint方法ID
                self.browser.browser_config.evm_address,
                signature,
            )

            success = nft_contract.write_contract(
                BADGE_NFT_CONTRACT,
                input_data,
            )

            if success:
                logger.success(f"{self.browser_id} NFT铸造成功")
                self._update_task_status(True)
            else:
                logger.error(f"{self.browser_id} NFT铸造失败")

            return success

        except Exception as e:
            logger.error(f"{self.browser_id} NFT铸造异常: {str(e)}")
            return False
