from abc import ABC, abstractmethod
from typing import Optional, Dict, List
from loguru import logger
import os
from functools import wraps
from typing import Callable, Any, TypeVar
from src.utils.hhcsv import HHCSV

T = TypeVar("T")


def require_csv_file(func: Callable[..., T]) -> Callable[..., T]:
    """验证CSV文件是否存在的装饰器"""

    @wraps(func)
    def wrapper(self: Any, *args, **kwargs) -> T:
        try:
            if not os.path.exists(self.file_path):
                logger.error(f"CSV文件不存在: {self.file_path}")
                return func.__annotations__.get("return")()
            return func(self, *args, **kwargs)
        except Exception as e:
            logger.error(f"{func.__name__} 失败: {e}")
            return func.__annotations__.get("return")()

    return wrapper


class StoryRepository(ABC):
    """Story项目状态存储接口"""

    @abstractmethod
    def update_status(self, id: str, project: str, status: bool = True) -> bool:
        """更新项目状态"""
        pass

    @abstractmethod
    def get_status(self, id: str, project: str) -> Optional[bool]:
        """获取项目状态"""
        pass

    @abstractmethod
    def get_all_status(self, project: str) -> Dict[str, bool]:
        """获取项目所有状态"""
        pass

    @abstractmethod
    def get_pending_ids(self, project: str) -> List[str]:
        """获取项目未完成的ID列表"""
        pass


class CSVStoryRepository(StoryRepository):
    """CSV实现的Story项目状态存储"""

    def __init__(self):
        self.file_path = "story/story.csv"
        self.csv_handler = HHCSV(self.file_path)

    @require_csv_file
    def update_status(self, id: str, project: str, status: bool = True) -> bool:
        """
        更新项目状态

        Args:
            id: 账号ID
            project: 项目名称 (例如: 'colormp', 'other_nft'等)
            status: True表示完成，False表示未完成
        """
        try:
            # 如果列不存在，需要添加新列
            if project not in self.csv_handler.headers:
                self.csv_handler.headers.append(project)
                self.csv_handler._save()

            # 更新状态
            updated = self.csv_handler.update_row(
                criteria={"id": str(id)}, updates={project: "1" if status else "0"}
            )

            if updated:
                logger.success(f"ID {id} 的 {project} 状态更新为: {status}")
                return True
            logger.error(f"ID {id} 状态更新失败")
            return False
        except Exception as e:
            logger.error(f"更新状态失败: {e}")
            return False

    @require_csv_file
    def update_balance(self, id: str, value: float) -> bool:
        """
        更新项目余额

        Args:
            id: 账号ID
            value: 余额
        """
        try:
            # 如果列不存在，需要添加新列
            if "balance" not in self.csv_handler.headers:
                self.csv_handler.headers.append("balance")
                self.csv_handler._save()

            # 更新余额
            updated = self.csv_handler.update_row(
                criteria={"id": str(id)}, updates={"balance": value}
            )

            if updated:
                logger.success(f"ID {id} 的余额更新为: {value}")
                return True
            logger.error(f"ID {id} 余额更新失败")
            return False
        except Exception as e:
            logger.error(f"更新余额失败: {e}")
            return False

    @require_csv_file
    def get_status(self, id: str, project: str) -> Optional[bool]:
        """
        获取项目状态

        Returns:
            Optional[bool]:
                True: 已完成
                False: 未完成
                None: 未找到记录或发生错误
        """
        try:
            if project not in self.csv_handler.headers:
                return None

            results = self.csv_handler.query(criteria={"id": str(id)})
            if not results:
                return None

            status = results[0].get(project)
            if status is None or status == "":
                return None

            return status == "1"
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return None

    @require_csv_file
    def get_all_status(self, project: str) -> Dict[str, bool]:
        """
        获取项目所有状态

        Returns:
            Dict[str, bool]: {id: status} 的字典
        """
        try:
            if project not in self.csv_handler.headers:
                return {}

            results = self.csv_handler.query()
            status_dict = {}

            for row in results:
                status = row.get(project)
                if status and status != "":
                    status_dict[row["id"]] = status == "1"

            return status_dict
        except Exception as e:
            logger.error(f"获取所有状态失败: {e}")
            return {}

    @require_csv_file
    def get_pending_ids(self, project: str) -> List[str]:
        """
        获取项目未完成的ID列表

        Returns:
            List[str]: 未完成的ID列表
        """
        try:
            results = self.csv_handler.query(criteria={"state": "1"})
            pending_ids = []

            for row in results:
                status = row.get(project, "")
                if status == "" or status == "0":
                    pending_ids.append(row["id"])

            return pending_ids
        except Exception as e:
            logger.error(f"获取未完成ID失败: {e}")
            return []


# 创建单例实例
story_repository = CSVStoryRepository()
