import random

from loguru import logger
from src.browsers import BrowserType
from retry import retry
from src.evm import ChainInfo
from src.utils.browser_config import BrowserConfigInstance
from src.evm.erc20_utils import get_web3_with_proxy
from src.evm.erc20_utils import send_transaction, get_transaction
from src.evm import ERC20
import time
from web3.types import TxParams
from src.utils.secure_encryption import SecureEncryption


class StoryPiperx(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "piperx"
        self.wallet_address = self.browser_config.evm_address
        pk = self.browser_config.evm_private_key
        if SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)
        self.private_key = pk

        self.usdt_contract_address = "******************************************"
        self.usdc_contract_address = "******************************************"
        self.weth_contract_address = "******************************************"
        self.wbtc_contract_address = "******************************************"
        self.piperx_faucet_abi = [
            {
                "inputs": [],
                "name": "drip",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function",
            }
        ]
        self.PiperXswapV2Router02 = "******************************************"
        self.PiperXswapV2Router02_abi = [
            {
                "inputs": [
                    {"name": "amountIn", "type": "uint256"},
                    {"name": "path", "type": "address[]"},
                ],
                "name": "getAmountsOut",
                "outputs": [{"name": "amounts", "type": "uint256[]"}],
                "stateMutability": "view",
                "type": "function",
            },
            {
                "inputs": [
                    {"name": "amountIn", "type": "uint256"},
                    {"name": "amountOutMin", "type": "uint256"},
                    {"name": "path", "type": "address[]"},
                    {"name": "to", "type": "address"},
                    {"name": "deadline", "type": "uint256"},
                ],
                "name": "swapExactTokensForETH",
                "outputs": [{"name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function",
            },
        ]
        self.WIP = "******************************************"

    def _faucet(self):
        logger.info("[INFO] 执行faucet")
        tokens = [
            self.usdt_contract_address,
            self.usdc_contract_address,
            self.weth_contract_address,
            self.wbtc_contract_address,
        ]
        for token in tokens:
            try:
                # 构建交易
                tx_params = {
                    "from": self.wallet_address,
                    "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
                }
                # 执行合约函数
                faucet_contract = self.web3.eth.contract(
                    address=token, abi=self.piperx_faucet_abi
                )
                faucet_function = faucet_contract.functions.drip()

                # 先用 call() 检查交易是否会成功
                try:
                    faucet_function.call({"from": self.wallet_address})
                except Exception as e:
                    logger.info(f"[INFO] {token} 已经领过水")
                    continue

                # transaction = faucet_function.build_transaction(tx_params)
                transaction = get_transaction(
                    self.web3, tx_params, faucet_function, gas_adjustment_factor=1.2
                )
                if not transaction:
                    logger.error(
                        f"[ERROR] {self.browser_id} 构建{token} faucet 交易失败"
                    )
                # 执行交易
                result = send_transaction(self.web3, transaction, self.private_key)
                if result:
                    logger.success(f"[SUCCESS] {token} faucet 交易成功")
                else:
                    logger.error(f"[ERROR] {token} faucet 交易失败")
            except Exception as e:
                logger.error(f"[ERROR] faucet 异常: {e}")
            finally:
                # 随机等待5-10秒
                time.sleep(random.randint(5, 10))

    # swap from PiperXswapV2Router02，打印出tx hash, 返回true/false.
    def _swap_2_ip(self, amount, path) -> bool:
        logger.info(f"[INFO] 执行swap {amount} {path} to IP")
        try:
            # 首先做approve，无限额度
            token_contract = ERC20(path[0], self.web3)
            approved = token_contract.approval(
                self.wallet_address, self.PiperXswapV2Router02, self.private_key, amount
            )
            if not approved:
                logger.error(f"[ERROR] approve {path[0]} to PiperXswapV2Router02 失败")
                return False

            PiperXswapV2Router02_contract = self.web3.eth.contract(
                address=self.PiperXswapV2Router02, abi=self.PiperXswapV2Router02_abi
            )
            amounts = PiperXswapV2Router02_contract.functions.getAmountsOut(
                amount, path
            ).call()
            amount_out = amounts[len(path) - 1]
            logger.info(f"[INFO] receipt amounts: {amount_out}")

            deadline = int(time.time()) + 60 * 10  # 当前时间戳 + 10分钟

            # 构建交易参数
            tx_params: TxParams = {
                "from": self.wallet_address,
                "nonce": self.web3.eth.get_transaction_count(self.wallet_address),
            }

            # 调用合约函数
            swap_function = (
                PiperXswapV2Router02_contract.functions.swapExactTokensForETH(
                    amount,
                    amount_out * 90 // 100,  # 下浮10%
                    path,
                    self.wallet_address,
                    deadline,
                )
            )

            # 获取并发送交易
            transaction = get_transaction(
                self.web3, tx_params, swap_function, gas_adjustment_factor=1.1
            )
            if not transaction:
                logger.error(f"[ERROR] swap {amount} {path[0]} to IP 构建交易失败")
                return False

            result = send_transaction(self.web3, transaction, self.private_key)
            if result:
                logger.success(f"[SUCCESS] swap {amount} {path[0]} to IP 交易成功")
            return result

        except Exception as e:
            logger.error(f"[ERROR] swap {amount} {path[0]} to IP 异常: {e}")
            return False

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        """执行 piperx 任务"""
        try:
            logger.info(f"[INFO] 开始执行 {self.browser_id} 的 piperx 任务")
            rpc_url = ChainInfo.STORY_ODYSSEY["rpc"]
            proxy = self._get_valid_proxy()
            user_agent = self.browser_config.user_agent
            self.web3 = get_web3_with_proxy(rpc_url, proxy, user_agent)

            # 1. faucet 逻辑，先检查test_usdt余额
            self._faucet()
            # 2. swap 逻辑
            tokens = [
                self.usdt_contract_address,
                self.usdc_contract_address,
                self.weth_contract_address,
                self.wbtc_contract_address,
            ]
            for token in tokens:
                try:
                    # 获取余额
                    token_contract = ERC20(token, self.web3)
                    balance = token_contract.balance_of(self.wallet_address)
                    logger.info(f"[INFO] {token} balance: {balance}")
                    if balance > 0:
                        if token == self.usdc_contract_address:
                            path = [token, self.usdt_contract_address, self.WIP]
                        else:
                            path = [token, self.WIP]
                        self._swap_2_ip(token_contract.parse_amount(balance), path)
                except Exception as e:
                    logger.error(f"[ERROR] swap {token} 异常: {e}")
                finally:
                    time.sleep(random.randint(5, 10))
            logger.success(f"[SUCCESS] {self.browser_id} piperx 任务执行完成")
            return True
        except Exception as e:
            logger.error(f"[ERROR] {self.browser_id} 任务失败: {e}")
            return False

    def close(self):
        pass
