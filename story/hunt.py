from loguru import logger
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from retry import retry
from typing import Optional
from config import MAX_GAS_PRICE


class StoryHunt(StoryBase):
    """StoryHunt任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "storyhunt"
        self.home_url = "https://app.storyhunt.xyz/quest"
        self.wallet_address = self.browser.browser_config.evm_address
        self._nft_contract = None
        self.badge_contract_address = "******************************************"

    def _connect_wallet(self, page) -> bool:
        """连接钱包"""
        return True

    def _request_get_signature(self, address: str) -> Optional[str]:
        """获取签名"""
        try:
            url = f"https://app.storyhunt.xyz/api/badge-mint-signature/{address}"
            headers = {
                "accept": "*/*",
                "priority": "u=1, i",
                "sec-ch-ua": '"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "Referer": "https://app.storyhunt.xyz/quest",
                "Referrer-Policy": "strict-origin-when-cross-origin",
            }

            success, response_data = self._make_get_request(url=url, headers=headers)
            if response_data:
                signature = response_data
                logger.success(f"{self.browser_id} 获取签名成功: {signature}")
                return signature

            if not success or "error" in response_data:
                logger.error(
                    f"{self.browser_id} {self.project_name} 获取签名失败: {response_data.get('error')}"
                )

            return None

        except Exception as e:
            logger.error(
                f"{self.browser_id} {self.project_name} 获取签名异常: {str(e)}"
            )
            return None

    def mint_nft(self, signature: str) -> bool:
        """铸造NFT"""
        try:
            nft_contract = self.get_nft_contract()

            # 使用build_transaction_data构建调用数据
            input_data = nft_contract.build_transaction_data(
                "0xb510391f",  # mint方法ID
                self.browser.browser_config.evm_address,
                signature,
            )

            success = nft_contract.write_contract(
                self.badge_contract_address,
                input_data,
            )

            if success:
                logger.success(f"{self.browser_id} NFT铸造成功")
                self._update_task_status(True)
            else:
                logger.error(f"{self.browser_id} NFT铸造失败")

            return success

        except Exception as e:
            logger.error(f"{self.browser_id} NFT铸造异常: {str(e)}")
            return False

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行StoryHunt任务"""
        if self._check_task_completed():
            return False

        # 检查NFT是否已铸造
        if self._check_badge_minted():
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            return False

        try:

            # gas低了再启用
            gas_prices = self.get_gas_price()
            if gas_prices and gas_prices.average > MAX_GAS_PRICE:
                logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
                return False

            # 获取签名
            signature = self._request_get_signature(
                self.browser.browser_config.evm_address
            )
            if not signature:
                logger.error(f"{self.browser_id} 获取签名失败")
                return False

            # 铸造NFT
            if not self.mint_nft(signature):
                return False

            logger.success(f"{self.browser_id} StoryHunt任务完成")
            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} StoryHunt任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} StoryHunt任务失败")

            return False, {"error": str(e)}
