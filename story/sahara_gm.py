from loguru import logger
from retry import retry
from src.browsers import BrowserType
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance


class StorySaharaGM(BrowserConfigInstance):
    """Sahara GM任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.channel_id = "1275198783621304342"

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Sahara GM任务"""
        discord = DiscordChatBot(
            self.channel_id, self.browser_config.proxy, self.browser_config.dc_token
        )
        if not discord.send_message("gm"):
            raise Exception("发送gm消息失败")
        logger.success(f"{self.browser_id} Sahara GM任务完成")
        return True
