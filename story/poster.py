import random
import json
from typing import Optional, Callable
from loguru import logger
from time import sleep
from retry import retry
from src.wallets.okx_wallet import OKXWallet
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from src.browsers.operations import try_click
import requests
from .contract.poster_nft import PosterNF<PERSON>
from src.evm import ChainInfo


class StoryPoster(StoryBase):
    """Poster任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "poster"
        self.home_url = "https://www.poster.fun/badge-mint"
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"
        self.poster_nft_contract_address = "******************************************"
        self.poster_nft_contract_address_2 = (
            "******************************************"
        )
        self.poster_nft_img_uri = (
            "ipfs://bafkreihwkg5pqmx5jqpyfxzwvplkcmcxwpybuf5ux7anffzl55x7i7n6aq"
        )
        # "https://nftstorage.link/ipfs/bafybeicocq5m6igkzw6amjt2xyrsp5v72tpldtqjpwcht33bmwckegtl7y"
        self.poster_nft_img_uri_2 = "https://ipfs.io/ipfs/bafkreiepd6boffjvuellseuru24har6sm56olmn76cypc7zapc33kfacge"

    def _check_wallet_connected(self, tab, timeout: int = 5) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            create_button = tab.ele(
                "x://span[text()='Create']",
                timeout=timeout,
            )
            if create_button:
                # 可能要点击一下Switch Network
                switch_network_button = tab.ele(
                    "x://button[contains(text(),'Switch Netwo')]", timeout=timeout
                )
                if switch_network_button:
                    switch_network_button.click()
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page, timeout: int = 5) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            if self._check_wallet_connected(page.latest_tab):
                logger.info(f"{self.browser_id} 钱包已经是连接状态")
                return True

            login_button = page.latest_tab.ele(
                "x://button[text()='Login']", timeout=timeout
            )
            if not login_button:
                raise WalletConnectionError(f"{self.browser_id} 查找 Login 按钮失败")

            login_button.click()
            btn_okx_wallet = page.latest_tab.ele(
                "x://span[text()='OKX Wallet']", timeout=timeout
            )
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            sleep(2)
            self._connect_okx_wallet()
            # 等待钱包连接完成
            self._wait_for_okx_wallet_disappear(page, timeout=timeout)
            logger.info(f"{self.browser_id} 钱包连接完成")

            # terms accept
            terms_button = page.latest_tab.ele(
                "x://button[text()='Accept']", timeout=timeout
            )
            if terms_button:
                terms_button.click()
                logger.info(f"{self.browser_id} 接受条款")

            # # 检查连接状态
            if self._check_wallet_connected(page.latest_tab, timeout=timeout):
                return True

            return False

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _click_task_button(self, tab, task_name: str, timeout: int = 5):
        """点击任务按钮

        Args:
            page: 页面对象
            task_name: 任务名称，如 'Follow Solo Twitter'
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        task_button = self._find_task_button(tab, task_name, timeout)
        if not task_button:
            raise TaskExecutionError(f"{self.browser_id} 查找 {task_name} 按钮失败")
        task_button.click()

    def _wait_for_okx_wallet(self, page, timeout: int = 5) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title == "OKX Wallet":
                return True
            sleep(1)
        return False

    def _wait_for_okx_wallet_disappear(self, page, timeout: int = 5) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title != "OKX Wallet":
                return True
            sleep(1)
        return False

    def _mint_badge(self, page, timeout: int = 5):
        """点击Mint Badge按钮并处理mint流程"""
        try:
            # 1. 获取签名
            headers = {
                "accept": "*/*",
                "accept-language": "en-US,en;q=0.9",
                "content-type": "application/json",
                "origin": "https://www.poster.fun/badge-mint",
                "referer": "https://www.poster.fun/badge-mint",
                "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Windows"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "user-agent": self.browser.browser_config.user_agent,
            }

            signature_url = f"https://www.poster.fun/api/signature"
            response = requests.post(
                signature_url,
                headers=headers,
                json={"address": self.wallet_address},
                proxies={
                    "http": self.browser.browser_config.proxy,
                    "https": self.browser.browser_config.proxy,
                },
            )
            response.raise_for_status()

            signature_data = response.json()
            if signature_data.get("signature") is None:
                raise Exception(f"获取签名失败: {signature_data}")

            signature = signature_data["signature"]
            logger.info(f"{self.browser_id} 获取签名成功: {signature}")

            tx_hash = self._badge_mint(signature)

            logger.info(f"{self.browser_id} Mint Badge 交易哈希: {tx_hash}")
            return True

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} Mint Badge 失败: {str(e)}")

    # 等待mint badge完成
    def _wait_for_mint_badge(self, page, timeout: int = 30):
        for _ in range(timeout):
            if self._check_badge_minted():
                return True
            sleep(1)
        return False

    def _check_step_completed(self, tab, step: int, timeout: int = 10):
        """检查任务是否完成"""
        step_spans = tab.eles(f"x://span[contains(text(),'Step')]", timeout=timeout)
        if step_spans:
            for step_span in step_spans:
                if str(step) in step_span.text:
                    next_ele = step_span.next()
                    if next_ele:
                        return True
        return False

    def _task_follow_twitter(self, page) -> bool:
        """Follow Twitter任务"""
        if self._check_step_completed(page.latest_tab, 1, timeout=30):
            logger.info(f"{self.browser_id} Follow Twitter任务完成")
            return True

        twitter_button = page.latest_tab.ele(
            "x://button[text()='Follow Twitter']", timeout=10
        )
        if not twitter_button:
            logger.error(f"{self.browser_id} 查找Follow Twitter按钮失败")
            return False
        twitter_button.click()
        sleep(3)

        # 关注推特，这里需要已经登陆了推特
        follow_button = page.latest_tab.ele(
            "x://span[text()='Follow @Posterdotfun']", timeout=60
        )
        if not follow_button:
            logger.error(f"{self.browser_id} 查找Follow @Posterdotfun按钮失败")
            return False
        follow_button.click()
        sleep(3)
        page.latest_tab.close()

        # 填写推特用户名并验证
        twitter_username = self.browser.browser_config.x_user
        username_input = page.latest_tab.ele(
            "x://input[@placeholder='Enter your Twitter Username']", timeout=10
        )
        if not username_input:
            logger.error(f"{self.browser_id} 查找Twitter用户名输入框失败")
            return False
        username_input.input(twitter_username)

        # 使用 JavaScript 点击第二个 Validate 按钮
        js_script = """
            const buttons = document.evaluate("//button[text()='Validate']", 
                document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
            if (buttons.snapshotLength >= 2) {
                const button = buttons.snapshotItem(1);
                if (button) {
                    button.click();
                    return true;
                }
            }
            return false;
        """

        sleep(5)  # 等待按钮可点击

        # 执行 JavaScript
        result = page.latest_tab.run_js(js_script)
        if not result:
            logger.error(f"{self.browser_id} JavaScript 点击 Validate 按钮失败")
            return False

        logger.info(f"{self.browser_id} 点击 Validate 按钮成功")

        return self._check_step_completed(page.latest_tab, 1, timeout=30)

    def _mint_poster_nft(self, contract_address: str, uri: str) -> bool:
        """Mint Poster NFT任务"""
        try:
            poster_nft = PosterNFT(
                contract_address,
                provider_url=ChainInfo.STORY_ODYSSEY["rpc"],
                proxy=self.browser.browser_config.proxy,
            )

            if poster_nft.balance_of(self.wallet_address) > 0:
                logger.info(f"{self.browser_id} Mint Poster NFT 已完成")
                return True

            tx_hash = poster_nft.mint(
                from_address=self.wallet_address,
                private_key=self.browser.browser_config.evm_private_key,
                recipient=self.wallet_address,
                uri=uri,  # 直接传入原始 URI
            )

            logger.info(f"{self.browser_id} 等待 Mint Poster NFT 交易完成: {tx_hash}")
            # 等待交易完成
            for _ in range(120):
                if poster_nft.balance_of(self.wallet_address) > 0:
                    logger.info(f"{self.browser_id} Mint Poster NFT 完成")
                    break
                sleep(1)

            return True

        except Exception as e:
            logger.error(f"{self.browser_id} Mint Poster NFT 失败: {str(e)}")
            return False

    def _task_telegram(self, page) -> bool:
        """Telegram任务"""
        # 点击Telegram按钮
        telegram_button = page.latest_tab.ele(
            "x://button[text()='Join Telegram']", timeout=10
        )
        if not telegram_button:
            logger.error(f"{self.browser_id} 查找Telegram按钮失败")
            return False
        telegram_button.click()
        sleep(3)
        page.latest_tab.close()
        sleep(3)

        # 点击Validate按钮
        validate_button = page.latest_tab.ele(
            "x://button[text()='Validate']", timeout=10
        )
        if not validate_button:
            logger.error(f"{self.browser_id} 查找Validate按钮失败")
            return False
        validate_button.click()
        sleep(3)
        return self._check_step_completed(page, 3, timeout=30)

    def _task_mint_official_poster_nft(self, page) -> bool:
        """Mint Official Poster NFT任务"""
        poster_nft = PosterNFT(
            self.poster_nft_contract_address_2,
            provider_url=ChainInfo.STORY_ODYSSEY["rpc"],
            proxy=self.browser.browser_config.proxy,
        )

        if poster_nft.balance_of(self.wallet_address) > 0:
            logger.info(f"{self.browser_id} Mint Official Poster NFT 已完成")
            return True

        # 尝试两次
        for _ in range(2):
            mint_button = page.latest_tab.ele(
                "x://button[text()='Mint NFT']", timeout=30
            )
            if not mint_button:
                logger.error(f"{self.browser_id} 查找Mint NFT按钮失败")
                page.latest_tab.refresh()
                continue
            tab = page.latest_tab
            mint_button.click()
            self._sign_okx_wallet()
            if self._check_step_completed(tab, 4, timeout=30):
                logger.info(f"{self.browser_id} Mint Official Poster NFT 完成")
                return True
        return False

    @retry(tries=3, delay=1)
    def task(self, clear_other_tabs: bool = True) -> bool:
        """执行1Combo任务"""
        if self._check_task_completed():
            return True

        if self._check_badge_minted():
            self._update_task_status(True)
            return True

        balance = self.get_nft_contract().get_balance()
        # logger.warning(f"{self.browser_id} 余额 {balance} ")
        if balance < 2:
            logger.warning(f"{self.browser_id} 余额 {balance} 不足，跳过铸造")
            return False

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)

            # 连接钱包
            if not self._connect_wallet(self.browser.page, timeout=20):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 执行 Follow Twitter 任务
            # if not self._task_follow_twitter(self.browser.page):
            #     logger.error(f"{self.browser_id} Follow Twitter 任务失败")
            #     return False

            # 执行 Mint Poster NFT 任务
            if not self._mint_poster_nft(
                self.poster_nft_contract_address, self.poster_nft_img_uri
            ):
                logger.error(f"{self.browser_id} Mint Poster NFT 任务失败")
                return False

            # 执行Mint Official Poster NFT 任务
            if not self._task_mint_official_poster_nft(self.browser.page):
                logger.error(f"{self.browser_id} Mint Official Poster NFT 任务失败")
                return False

            # mint badge
            self._mint_badge(self.browser.page, timeout=20)
            if not self._wait_for_mint_badge(self.browser.page):
                raise TaskExecutionError(f"{self.browser_id} Mint Badge失败")

            self._update_task_status(True)
            logger.success(f"{self.browser_id} Poster任务完成")
            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            # raise
        except Exception as e:
            logger.error(f"{self.browser_id} Poster任务失败: {e}")
            # raise TaskExecutionError(f"{self.browser_id} Poster任务失败")
        # finally:
        #     self.browser.page.latest_tab.close()
