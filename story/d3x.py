import random
import json
from loguru import logger
from time import sleep
from retry import retry
from src.wallets.okx_wallet import OKXWallet
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from src.browsers.operations import try_click
from config import MAX_GAS_PRICE


class StoryD3x(StoryBase):
    """D3x任务"""

    # JavaScript脚本常量
    JS_SCRIPT_CHOOSE_WALLET = """
        // 获取根元素
        const modalElement = document.querySelector('w3m-modal');
        if (!modalElement || !modalElement.shadowRoot) return null;
        
        // 使用更安全的选择器方式
        const shadowRoot = modalElement.shadowRoot;
        const wuiFlex = shadowRoot.querySelector('wui-flex');
        if (!wuiFlex) return null;
        
        const wuiCard = wuiFlex.querySelector('wui-card');
        if (!wuiCard) return null;
        
        const router = wuiCard.querySelector('w3m-router');
        if (!router || !router.shadowRoot) return null;
        
        const routerDiv = router.shadowRoot.querySelector('div');
        if (!routerDiv) return null;
        
        const connectView = routerDiv.querySelector('w3m-connect-view');
        if (!connectView || !connectView.shadowRoot) return null;
        
        const connectViewFlex = connectView.shadowRoot.querySelector('wui-flex');
        if (!connectViewFlex) return null;
        
        const loginList = connectViewFlex.querySelector('w3m-wallet-login-list');
        if (!loginList || !loginList.shadowRoot) return null;
        
        const loginListFlex = loginList.shadowRoot.querySelector('wui-flex');
        if (!loginListFlex) return null;
        
        const connectorList = loginListFlex.querySelector('w3m-connector-list');
        if (!connectorList || !connectorList.shadowRoot) return null;
        
        const connectorListFlex = connectorList.shadowRoot.querySelector('wui-flex');
        if (!connectorListFlex) return null;
        
        const featuredWidget = connectorListFlex.querySelector('w3m-connect-featured-widget');
        if (!featuredWidget || !featuredWidget.shadowRoot) return null;
        
        const featuredWidgetFlex = featuredWidget.shadowRoot.querySelector('wui-flex');
        if (!featuredWidgetFlex) return null;
        
        // 使用 querySelector 和属性选择器来查找 OKX Wallet
        const okxWallet = featuredWidgetFlex.querySelector('wui-list-wallet[name="OKX Wallet"]');
        return okxWallet;
    """

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "d3x"
        self.home_url = "https://story.d3x.exchange/odyssey-proposal"
        self.home_url_badge_list = (
            "https://story.d3x.exchange/odyssey-proposal?current=mint-badge-list"
        )
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"
        self.test_usdt_contract_address = "******************************************"

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = page.ele(
                "x://button[@aria-haspopup='dialog']",
                timeout=5,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            if self._check_wallet_connected(page):
                return True

            connect_button = page.ele("x://button[text()='Connect Wallet']", timeout=5)
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect Wallet 按钮失败"
                )

            connect_button.click()
            btn_okx_wallet = page.run_js(self.JS_SCRIPT_CHOOSE_WALLET)
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            self._connect_okx_wallet()

            # # 检查连接状态
            if self._check_wallet_connected(page):
                return True

            return False

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _find_task_button(self, tab, task_name: str, timeout: int = 5):
        """查找任务卡片中的按钮元素

        Args:
            page: 页面对象
            task_name: 任务名称，如 'Follow Solo Twitter'
            button_text: 按钮文本，如 'Claim'，不传则只匹配 cursor-pointer 类
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        xpath = f"x://div[contains(text(),'{task_name}')]"

        try:
            return tab.ele(xpath, timeout=timeout)
        except Exception as e:
            logger.error(f"未找到任务 '{task_name}' 的按钮: {str(e)}")
            return None

    def _click_task_button(
        self, tab, task_name: str, timeout: int = 5, is_close: bool = True
    ):
        """点击任务按钮

        Args:
            page: 页面对象
            task_name: 任务名称，如 'Follow Solo Twitter'
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        if self._check_is_completed(tab, task_name):
            return True

        task_button = self._find_task_button(tab, task_name, timeout)
        if not task_button:
            raise TaskExecutionError(f"{self.browser_id} 查找 {task_name} 按钮失败")

        if is_close:
            new_tab = task_button.click.for_new_tab()
            if new_tab and is_close:
                sleep(1)
                new_tab.close()
        else:
            task_button.click()

        return False

    def _check_is_completed(self, page, task_name: str, timeout: int = 5):
        """检查任务是否完成"""
        ele = page.ele(
            f"x://div[contains(text(), '{task_name}')]/following-sibling::div[contains(text(), 'Completed')]",
            timeout=timeout,
        )
        if ele:
            return True
        return False

    def _check_upgrade_copilot(self, page, timeout: int = 5):
        return self._check_is_completed(
            page, "membership plan for Insight Copilot", timeout
        )

    def _upgrade_copilot(self, page, timeout: int = 5):
        """点击升级Insight Copilot按钮

        Args:
            page: 页面对象
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        # 检查是否已经完成
        if self._check_upgrade_copilot(page):
            return True

        upgrade_button = page.ele("x://button[text()='Upgrade Now']", timeout=timeout)
        if not upgrade_button:
            return False

        upgrade_button.click()
        self._sign_okx_wallet()

        # 等待提交完成
        for _ in range(30):
            if self._check_upgrade_copilot(page):
                break
            sleep(1)

    def _get_test_usdt_tokens(self, page, timeout: int = 5) -> int:
        """点击获取TestUSDT按钮

        Args:
            page: 页面对象
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        balance = self._get_test_usdt_balance()
        if balance > 0:
            return balance

        get_test_usdt_button = page.latest_tab.ele(
            "x://button[text()='Get 500 TestUsdt']", timeout=timeout
        )
        if not get_test_usdt_button:
            raise TaskExecutionError(
                f"{self.browser_id} 查找 Get TestUSDT tokens 按钮失败"
            )
        get_test_usdt_button.click()
        self._approve_okx_wallet()

        # 等待TestUSDT余额大于0
        for _ in range(60):
            balance = self._get_test_usdt_balance()
            if balance > 0:
                return balance
            sleep(1)
        return 0

    def _wait_for_okx_wallet(self, page, timeout: int = 5) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title == "OKX Wallet":
                return True
            sleep(1)
        return False

    def _get_test_usdt_balance(self) -> int:
        """检查TestUSDT余额

        Returns:
            int: TestUSDT余额
        """
        nft_contract = self.get_nft_contract()
        balance = nft_contract.get_token_balance(self.test_usdt_contract_address)

        if balance > 0:
            logger.info(f"{self.browser_id} {self.project_name}TestUSDT余额: {balance}")
            return int(balance)  # 转换为整数返回

        logger.info(f"{self.browser_id} {self.project_name} TestUSDT余额: {balance}")
        return 0

    def _open_perpetual_trade(self, page, timeout: int = 5):
        """点击Open a Perpetual trade按钮"""
        # 获取test usdt
        balance = self._get_test_usdt_tokens(page)
        if balance == 0:
            raise TaskExecutionError(f"{self.browser_id} 获取TestUSDT失败")

        ele = page.ele("x://button[text()='Approve']", timeout=timeout)
        if ele:
            ele.click()

            if self._wait_for_okx_wallet(page, timeout=20):
                # 勾选无限授权
                ele = page.latest_tab.ele(
                    "x://input[@type='checkbox']", timeout=timeout
                )
                if ele:
                    ele.click()
                self._approve_okx_wallet()
            else:
                raise TaskExecutionError(
                    f"{self.browser_id} 打开Perpetual trade失败, 未弹出OKX钱包"
                )

        # 输入数量，最大50
        sleep(5)

        # 随机数量，10，20，30，40，50
        amount = random.choice([10, 20, 30, 40, 50])
        ele = page.latest_tab.ele(f"x://input[@max='10']", timeout=timeout)
        if ele:
            ele.input(str(amount))
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找输入数量失败")
        # 点击LongBTC按钮
        ele = page.latest_tab.ele(
            "x://button[normalize-space()='Long BTC']", timeout=timeout
        )
        if ele:
            ele.click()
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找 Long BTC 按钮失败")
        # 点击Confirm按钮
        ele = page.latest_tab.ele("x://button[text()='Confirm']", timeout=timeout)
        if ele:
            ele.click()
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找 Confirm 按钮失败")
        # OKx钱包签名
        self._approve_okx_wallet()

        page.back()

    def _deposit_test_usdt(self, page, timeout: int = 5):
        """点击Deposit any amount of TestUSDT into the D3X Vault.按钮"""
        balance = self._get_test_usdt_balance()
        if balance == 0:
            raise TaskExecutionError(f"{self.browser_id} TestUSDT余额为0")
        ele = page.ele("x://button[contains(text(), 'Approve')]", timeout=timeout)
        if ele:
            ele.click()
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找 Approve USDT按钮失败")
        if self._wait_for_okx_wallet(page, timeout=20):
            # 勾选无限授权
            ele = page.latest_tab.ele("x://input[@type='checkbox']", timeout=timeout)
            if ele:
                ele.click()
            self._approve_okx_wallet()
        else:
            raise TaskExecutionError(
                f"{self.browser_id} 打开Perpetual trade失败, 未弹出OKX钱包"
            )
        # 输入数量，最大50
        sleep(5)
        amount = balance if balance < 50 else 50
        ele = page.latest_tab.ele(f"x://input[@max='10']", timeout=timeout)
        if ele:
            ele.input(str(amount))
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找输入数量失败")
        # 点击Submit按钮
        ele = page.latest_tab.ele("x://button[normalize-space()='Submit']", timeout=20)
        if ele:
            ele.click()
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找 Submit 按钮失败")
        # OKx钱包签名
        if self._wait_for_okx_wallet(page, timeout=20):
            self._sign_okx_wallet()
        else:
            raise TaskExecutionError(
                f"{self.browser_id} 打开Perpetual trade失败, 未弹出OKX钱包"
            )

    def _mint_badge(self, page, timeout: int = 5):
        """点击Mint Badge按钮"""
        ele = page.latest_tab.ele("x://button[text()='Mint Badge']", timeout=timeout)
        if ele:
            ele.click()
        else:
            raise TaskExecutionError(f"{self.browser_id} 查找 Mint Badge 按钮失败")

        self._approve_okx_wallet()

    # 等待mint badge完成
    def _wait_for_mint_badge(self, page, timeout: int = 30):
        for _ in range(timeout):
            if self._check_badge_minted():
                return True
            sleep(1)
        return False

    @retry(tries=3, delay=1)
    def task(self, clear_other_tabs: bool = True) -> bool:
        """执行D3x任务"""
        if self._check_task_completed():
            return False

        if self._check_badge_minted():
            self._update_task_status(True)
            return False

        # # gas低了再启用
        gas_prices = self.get_gas_price()
        if gas_prices and gas_prices.average > MAX_GAS_PRICE:
            logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
            return False

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            sleep(3)

            # 切换到story链
            is_success = OKXWallet(self.browser.browser).change_connect_site(
                "Story Odyssey Testnet"
            )
            if not is_success:
                raise WalletConnectionError(f"{self.browser_id} 切换站点失败")

            # 连接钱包
            if not self._connect_wallet(self.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 点击 Get your badge now  按钮
            if not try_click(self.page, "x://button[text()='Get your badge now']"):
                raise TaskExecutionError(
                    f"{self.browser_id} 查找 Get your badge now 按钮失败"
                )

            # 执行 Follow D3X on X 任务
            self._click_task_button(self.page, "Follow D3X on X")

            # 执行 Join the D3X Telegram 任务
            self._click_task_button(self.page, "Join the D3X Telegram")

            # 执行 Claim Story Iliad test tokens (IP). 任务
            self._click_task_button(self.page, "Claim Story Iliad test tokens (IP).")

            # 执行 Subscribe to the limited-time free beta membership plan for Insight Copilot. 任务
            is_success = self._click_task_button(
                self.page,
                "Subscribe to the limited-time free beta membership plan for Insight Copilot.",
                is_close=False,
            )
            # 可能需要签名
            if not is_success and self._wait_for_okx_wallet(self.page, timeout=5):
                self._sign_okx_wallet()

            self._upgrade_copilot(self.page)

            if "upgrade" in self.page.url:
                self.page.back()
                sleep(1)

            # # 重新打开任务页面
            # self.browser.open_url(self.home_url_badge_list, new_tab=True)

            # # 执行 Visit the Insight Copilot page to check real-time Volume of Mention. 任务
            # self._click_task_button(
            #     self.browser.page.latest_tab,
            #     "Visit the Insight Copilot page to check real-time Volume of Mention.",
            # )
            # self.browser.page.latest_tab.back()

            # 执行 Open a Perpetual trade on D3X’s Perp DEX using the TestUSDT tokens 任务
            is_success = self._click_task_button(
                self.page,
                "Open a Perpetual trade on D3X’s Perp DEX using the TestUSDT tokens",
                is_close=False,
            )
            if not is_success:
                self._open_perpetual_trade(self.page)

            # self.browser.page.latest_tab.close()  # 这里会关闭任务页面

            # # 执行 Deposit any amount of TestUSDT into the D3X Vault. 任务
            is_success = self._click_task_button(
                self.page,
                "Deposit any amount of TestUSDT into the D3X Vault.",
                is_close=False,
            )
            if not is_success:
                self._deposit_test_usdt(self.page)

            # 重新打开任务页面
            self.browser.open_url(self.home_url_badge_list)

            # mint badge
            self._mint_badge(self.browser.page, timeout=20)
            if not self._wait_for_mint_badge(self.browser.page):
                raise TaskExecutionError(f"{self.browser_id} Mint Badge失败")

            self._update_task_status(True)
            logger.success(f"{self.browser_id} D3x任务完成")
            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            # raise
        except Exception as e:
            logger.error(f"{self.browser_id} D3x任务失败: {e}")
            # raise TaskExecutionError(f"{self.browser_id} D3x任务失败")
        # finally:
        #     self.browser.page.latest_tab.close()
