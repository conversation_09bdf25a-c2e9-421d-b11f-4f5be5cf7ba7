from loguru import logger
from retry import retry
from src.browsers import BrowserType
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from dotenv import load_dotenv
import os
import time
import random
from datetime import datetime, timezone, timedelta

load_dotenv()

class StoryGaiaChat(BrowserConfigInstance):
    """Gaia 聊天任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.channel_id = "1215253012939808810"  # 英文频道
        self.send_del_channel_id = "1275775788259737733"  # 俄语
        self.channel_id_gm = "1215256291165872128" #gm
        self.user_id = self.browser_config.dc_id

    @retry(tries=3, delay=1)
    def task(self, amount: int = 1, send_del: int = 0) -> bool:
        """执行Gaia 聊天任务"""
        os.environ["HTTP_PROXY"] = self.browser_config.proxy
        os.environ["HTTPS_PROXY"] = self.browser_config.proxy
        discord = DiscordChatBot(
            self.browser_config.proxy, self.browser_config.dc_token, self.user_id, 
            user_agent=self.browser_config.user_agent
        )
        
        # 自动聊天
        discord.auto_chat(channel_id=self.channel_id, language="auto", max_messages=amount, min_delay=30, max_delay=60)
        discord.send_del_message(channel_id=self.send_del_channel_id, count=send_del, msg="привет всем​", min_delay=5, max_delay=10, min_time_diff=1800)
        
        logger.success(f"{self.browser_id} 完成任务")
        return True