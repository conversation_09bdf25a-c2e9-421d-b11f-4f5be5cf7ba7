import os
import re
from time import sleep
import json
from loguru import logger
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from retry import retry
from typing import Optional

from src.utils.proxies import Proxies
from .contract.nft_utils import NFTContract
from src.emails import GmailWebClient
from config import ICLOUD_EMAIL, ICLOUD_EMAIL_PASSWORD, MAX_GAS_PRICE
from src.wallets.okx_wallet import OKXWallet


class StoryRights(StoryBase):
    """Rightsfually任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "rightsfually"
        self.home_url = "https://rightsfually.com/en/claim-rightsfually-badge/"
        self.wallet_address = self.browser.browser_config.evm_address
        self.badge_contract_address = "******************************************"
        self._nft_contract = None

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接"""
        try:
            connected_address = page.ele(
                "x://button[@aria-label='profile']",
                timeout=5,
            )
            if connected_address:
                return True
        except Exception:
            pass
        return False

    def _get_verification_code(self, page) -> Optional[str]:
        """获取验证码"""
        email = self.browser.browser_config.email
        if not email:
            raise TaskExecutionError(f"{self.browser_id} 邮箱为空")

        if "gmail" in email:
            return self._get_verification_code_gmail(page)
        elif "icloud" in email:
            return self._get_verification_code_icloud(page)

        return None

    def _get_verification_code_icloud(self, page) -> Optional[str]:
        """从iCloud获取验证码"""
        from src.emails import ICloudClient, SearchCriteria

        # icloud 采用隐藏邮箱模式，所以需要使用真实邮箱
        email = ICLOUD_EMAIL
        password = ICLOUD_EMAIL_PASSWORD

        if not email or not password:
            raise TaskExecutionError(
                f"{self.browser_id} 邮箱或密码为空, 请检查.env配置"
            )

        try:
            with ICloudClient(email, password).connect() as client:
                # 搜索验证码邮件
                folder = "INBOX"
                emails = client.search_emails_with_retry(
                    SearchCriteria(
                        folder=folder,
                        subject="Verify Your Account with RightsfuAlly",
                    )
                )

                if not emails:
                    folder = "Junk"
                    emails = client.search_emails_with_retry(
                        SearchCriteria(
                            folder=folder,
                            subject="Verify Your Account with RightsfuAlly",
                        )
                    )

                if not emails:
                    logger.error(f"{self.browser_id} 未找到验证邮件")
                    return None

                # 检查收件人是否匹配
                email_info = emails[0]
                to_email = email_info["to"]
                hidden_email = self.browser.browser_config.email.lower()
                if hidden_email not in to_email.lower():
                    logger.error(
                        f"{self.browser_id} 验证邮件收件人不匹配: {to_email} != {email}"
                    )
                    return None

                # 从邮件内容中提取验证码
                content = email_info["content"]

                match = re.search(r"\b(\d{6})\b", content)
                verify_code = match.group(1) if match else None
                if not verify_code:
                    logger.error(f"{self.browser_id} 提取验证码失败")
                    return None

                logger.success(f"{self.browser_id} 匹配到验证码: {verify_code}")

                # 删除邮件
                client.delete_emails([email_info["id"]], folder=folder)
                return verify_code

        except Exception as e:
            logger.error(f"获取验证码失败: {e}")
            return None

    def _get_verification_code_gmail(self, page) -> Optional[str]:
        """获取Gmail验证码"""

        def get_verify_code(tab) -> Optional[str]:
            verify_code = None
            try:
                verify_code_ele = tab.ele(
                    """x://p[contains(text(), 'Your One Time Password')]""",
                    timeout=5,
                )
                verify_code_text = verify_code_ele.text
                # 使用正则表达式匹配6位数字
                import re

                match = re.search(r"\b(\d{6})\b", verify_code_text)
                verify_code = match.group(1) if match else None
            except Exception:
                pass

            return verify_code

        gmail_client = GmailWebClient(self.browser)
        return gmail_client.get_gmail_verification_url(
            sender_email="<EMAIL>",
            get_link_func=get_verify_code,
        )

    def _check_captcha(self, page, timeout: int = 30) -> bool:
        """检查验证码"""
        for _ in range(timeout):
            sleep(1)
            try:
                captcha_container = page.ele(
                    "x://div[@class='form-group']//div[@class='g-recaptcha']",
                    timeout=10,
                )
                iframe = captcha_container.ele("tag:iframe")
                iframe_body = iframe.ele("tag:body")
                if not iframe_body:
                    continue

                checkbox = iframe_body.ele("x://span[@role='checkbox']")
                is_checked = checkbox.attr("aria-checked")
                if is_checked == "true":
                    logger.success(f"{self.browser_id} 验证码已通过")
                    return True
            except Exception:
                continue
        return False

    def _sign_in(self, page):
        """登录"""
        try:
            email = self.browser.browser_config.email
            page.ele(
                "x://input[@type='email']",
                timeout=5,
            ).input(email)

            if not self._check_captcha(page):
                logger.error(f"{self.browser_id} 验证码未通过")
                return False

            page.ele(
                "x://button[@type='submit' and text()='Send OTP']",
                timeout=5,
            ).click(True)

            verify_code = self._get_verification_code(page)
            if not verify_code:
                logger.error(f"{self.browser_id} 获取验证码失败")
                return False

            page.ele(
                "x://input[@id='otp_box']",
                timeout=5,
            ).input(verify_code)

            page.ele(
                "x://button[text()='Enter']",
                timeout=5,
            ).click()
        except Exception as e:
            logger.error(f"{self.browser_id} 登录失败: {e}")
            return False

        return True

    def _connect_wallet(self, page) -> bool:
        """连接钱包"""
        try:
            if self._check_wallet_connected(page):
                logger.success(f"{self.browser_id} 钱包已连接")
                return True

            # 点击连接钱包按钮
            connect_btn = page.ele(
                "x://a[text()='Sign In']",
                timeout=5,
            )
            if not connect_btn:
                logger.error(f"{self.browser_id} 未找到连接钱包按钮")
                return False

            connect_btn.click()
            sleep(2)
            try:
                page.ele("Sign in via Email", timeout=10).click()
            except Exception:
                pass
            result = self._sign_in(page)
            if not result:
                raise WalletConnectionError(f"{self.browser_id} 登录失败")

            # logger.success(f"{self.browser_id} 连接钱包成功")
            return True

        except Exception as e:
            logger.error(f"{self.browser_id} 连接钱包异常: {str(e)}")
            return False

    def mint_nft(self, signature: str) -> bool:
        """铸造NFT"""
        try:
            nft_contract = self.get_nft_contract()

            # 使用build_transaction_data构建调用数据
            input_data = nft_contract.build_transaction_data(
                "0xb510391f",  # mint方法ID
                self.browser.browser_config.evm_address,
                signature,
            )

            success = nft_contract.write_contract(
                self.badge_contract_address,
                input_data,
            )

            if success:
                logger.success(f"{self.browser_id} NFT铸造成功")
                self._update_task_status(True)
            else:
                logger.error(f"{self.browser_id} NFT铸造失败")

            return success

        except Exception as e:
            logger.error(f"{self.browser_id} NFT铸造异常: {str(e)}")
            return False

    def get_nft_contract(self) -> NFTContract:
        """获取NFT合约"""
        if self._nft_contract:
            return self._nft_contract

        rpc_url = os.getenv("RPC_URL")
        private_key = self.browser.browser_config.evm_private_key
        proxy = self.browser.browser_config.proxy
        if proxy:
            is_valid = Proxies(proxy).verify()
            if not is_valid:
                logger.error(f"{self.browser_id} {self.project_name} 代理无效")
                return False

        user_agent = self.browser.browser_config.user_agent
        nft_contract = NFTContract(
            self.browser_id, rpc_url, private_key, proxy, user_agent
        )
        self._nft_contract = nft_contract
        return self._nft_contract

    def _get_signature(self):
        """获取签名"""
        res = self.page.listen.wait(timeout=20)
        if not res or not res.response:
            logger.error(f"{self.browser_id} 获取签名失败")
            return False

        body = json.loads(res.response.raw_body)
        if body.get("result"):
            return body.get("signature")

        logger.error(f"{self.browser_id} 获取签名失败")
        return None

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Rightsfually任务"""
        if self._check_task_completed():
            return True

        # 检查NFT是否已铸造
        nft_contract = self.get_nft_contract()
        if nft_contract.is_minted(self.badge_contract_address):
            logger.success(f"{self.browser_id} 已经mint")
            self._update_task_status(True)
            return True

        # gas低了再启用
        gas_prices = self.get_gas_price()
        if gas_prices and gas_prices.average > MAX_GAS_PRICE:
            logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
            return False

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            # self._close_other_tabs(self.page)
            # 切换站点
            is_success = OKXWallet(self.browser.browser).change_connect_site(
                "Story Odyssey Testnet"
            )
            if not is_success:
                raise WalletConnectionError(f"{self.browser_id} 切换站点失败")
            self.browser.page.refresh()
            sleep(3)

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            self.page.listen.start("https://rightsfually.com/en/generate-ip-signature")
            self.page.get(self.home_url)
            sleep(1)
            # 等待OKX钱包
            if self._wait_for_okx_wallet(self.browser.page, timeout=10):
                self._sign_okx_wallet()
            try:
                self.page.ele("x://button[@onclick='claimBadge()']").click()
                # 铸造NFT
                signature = self._get_signature()
                if not signature:
                    raise TaskExecutionError(f"{self.browser_id} 获取签名失败")

                if not self.mint_nft(signature):
                    return False
                OKXWallet(self.browser.browser).cancel_sign()
                logger.success(f"{self.browser_id} Rightsfually任务完成")
                return True
            except Exception:
                pass

            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Rightsfually任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Rightsfually任务失败")

    def _wait_for_okx_wallet(self, page, timeout: int = 5) -> bool:
        for _ in range(timeout):
            if page.latest_tab.title == "OKX Wallet":
                return True
            sleep(1)
        return False
