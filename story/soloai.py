import random
import json
from loguru import logger
from time import sleep
from retry import retry
from .base import StoryBase, WalletConnectionError, TaskExecutionError
from src.browsers import BrowserType
from config import MAX_GAS_PRICE


class StorySoloai(StoryBase):
    """Soloai NFT任务"""

    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.project_name = "soloai"
        self.home_url = "https://soloai.io/reward"
        self.home_url_odyssey = "https://soloai.io/"
        self.badge_url = "https://story-start-badge-claim.soloai.io/"
        self.listen_api_url = "https://oapi.soloai.io/v1/tasks/badgesign"
        self.wallet_address = self.browser.browser_config.evm_address

    def get_status(self):
        """获取任务状态"""
        return self._get_task_status()

    def _check_wallet_connected(self, page) -> bool:
        """检查钱包是否已连接

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 钱包是否已连接
        """
        try:
            connected_address = page.ele(
                "x://button[@aria-haspopup='dialog']",
                timeout=5,
            )
            if connected_address:
                return True

        except Exception:
            pass

        return False

    def _connect_wallet(self, page) -> bool:
        """连接首页钱包"""
        try:
            # 检查是否已连接
            if self._check_wallet_connected(page):
                return True

            connect_button = page.ele("x://div[text()='Login']", timeout=5)
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect Wallet 按钮失败"
                )

            connect_button.click()

            # 如果点击了Connect Wallet后，出现Verify your account，则先签名即可
            auth_button = page.ele(
                "x://button[@data-testid='rk-auth-message-button']", timeout=3
            )
            if auth_button:
                auth_button.click()
                self._sign_okx_wallet()

                # 检查连接状态
                if self._check_wallet_connected(page):
                    return True

                raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

            btn_okx_wallet = page.ele(
                "x://div[contains(text(), 'OKX Wallet')]", timeout=3
            )
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            self._connect_okx_wallet()

            # Verify your account
            auth_button = page.ele(
                "x://button[@data-testid='rk-auth-message-button']", timeout=5
            )
            if auth_button:
                auth_button.click()
                self._sign_okx_wallet()

            # 检查连接状态
            if self._check_wallet_connected(page):
                return True

            raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _connect_wallet_badge_page(self, page) -> bool:
        """连接badge页面钱包"""
        try:

            # Wrong network
            try:
                wrong_network = page.ele(
                    "x://button[text()='Wrong network']", timeout=3
                )
                if wrong_network:
                    wrong_network.click(True)
                    page.ele(
                        "x://div[contains(text(),'Story Public Testnet')]", timeout=5
                    ).click()
            except Exception:
                pass

            # 检查是否已连接
            connected_address = page.ele(
                "x://button[contains(@class, 'rounded') and contains(text(), '0x')]",
                timeout=5,
            )
            if connected_address:
                logger.info(f"{self.browser_id} 钱包已连接")
                return True

            connect_button = page.ele(
                "x://button[contains(text(), 'Connect Wallet')]", timeout=5
            )
            if not connect_button:
                raise WalletConnectionError(
                    f"{self.browser_id} 查找 Connect Wallet 按钮失败"
                )

            connect_button.click()

            btn_okx_wallet = page.ele(
                "x://div[contains(text(), 'OKX Wallet')]", timeout=5
            )
            if not btn_okx_wallet:
                raise WalletConnectionError(f"{self.browser_id} 查找OKX钱包失败")

            btn_okx_wallet.click()
            self._connect_okx_wallet()

            # 检查连接状态
            connected_address = page.ele(
                "x://button[contains(@class, 'rounded') and contains(text(), '0x')]",
                timeout=5,
            )
            if connected_address:
                logger.success(f"{self.browser_id} 钱包连接成功")
                return True

            raise WalletConnectionError(f"{self.browser_id} 钱包连接失败")

        except Exception as e:
            raise WalletConnectionError(f"{self.browser_id} 连接钱包失败: {str(e)}")

    def _find_task_button(
        self, page, task_name: str, button_text: str = None, timeout: int = 5
    ):
        """查找任务卡片中的按钮元素

        Args:
            page: 页面对象
            task_name: 任务名称，如 'Follow Solo Twitter'
            button_text: 按钮文本，如 'Claim'，不传则只匹配 cursor-pointer 类
            timeout: 超时时间，默认 5 秒

        Returns:
            Element: 按钮元素，如果未找到则返回 None
        """
        base_xpath = f"x://div[contains(text(),'{task_name}')]/../../div[last()]/div[contains(@class,'cursor-pointer')"

        if button_text:
            xpath = f"{base_xpath} and contains(text(),'{button_text}')]"
        else:
            xpath = f"{base_xpath}]"

        try:
            return page.ele(xpath, timeout=timeout)
        except Exception as e:
            logger.debug(f"未找到任务 '{task_name}' 的按钮: {str(e)}")
            return None

    def _find_claimed_ele(self, page, title: str, timeout: int = 60) -> bool:
        claimed_ele = page.ele(
            f"x://div[contains(text(),'{title}')]/../../div[last()]/div[contains(text(),'Claimed')]",
            timeout=timeout,
        )
        return bool(claimed_ele)

    def _task_follow_twitter(self, page) -> bool:
        """关注推特任务"""
        try:
            _task_name = "Follow Solo Twitter"
            if self._find_claimed_ele(page, _task_name, timeout=3):
                logger.info(f"{self.browser_id} {_task_name}任务已领取")
                return True

            # 点击 Go 按钮
            go_button = self._find_task_button(page, _task_name, "GO", timeout=5)

            if not go_button:
                raise TaskExecutionError(
                    f"{self.browser_id} {_task_name}查找 Go 按钮失败"
                )

            new_tab = go_button.click.for_new_tab()
            sleep(3)
            new_tab.close()

            # 点击 Claim 按钮
            claim_button = self._find_task_button(page, _task_name, "Claim", timeout=5)
            if not claim_button:
                raise TaskExecutionError(
                    f"{self.browser_id} {_task_name} 查找 Claim 按钮失败"
                )

            claim_button.click()

            if not self._find_claimed_ele(page, _task_name):
                raise TaskExecutionError(f"{self.browser_id} 领取任务失败")

            return True

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 关注推特任务失败: {str(e)}")

    def _check_flame_clicked(self, page, timeout: int = 5) -> bool:
        """
        检查火焰图标是否已经被点击过

        Args:
            page: 页面对象

        Returns:
            bool: True 表示有被点击的元素，False 表示没有
        """
        try:
            # 检查是否有已点击的样式类
            clicked_flame = page.ele(
                "x://*[name()='svg'][contains(@class, 'lucide-flame') and @fill='currentColor']/*[name()='path']",
                timeout=timeout,
            )
            return bool(clicked_flame)
        except:
            return False

    def _random_choose_trending_ele(self, page) -> bool:
        flame_eles = page.eles(
            "x://div[contains(text(), 'Trending')]/following-sibling::div//div[contains(@class, 'rounded-[16px]')]/div[last()]/div[last()]",
            timeout=5,
        )
        if not flame_eles:
            raise TaskExecutionError(
                f"{self.browser_id} 查找 Trending 下的卡片元素失败"
            )

        # 只取前5个元素，如果元素总数少于5个则取所有元素
        available_eles = flame_eles[:5]
        return random.choice(available_eles)

    def _claim_task(self, page, task_name: str) -> bool:
        """领取任务奖励的通用方法

        Args:
            page: 页面对象
            task_name: 任务名称

        Returns:
            bool: 是否成功领取

        Raises:
            TaskExecutionError: 当领取失败时抛出
        """
        # 返回任务页面
        page.get(self.home_url)
        sleep(3)
        self._select_story_starts(self.browser.page)
        sleep(3)

        # 点击Claim按钮
        claim_button = self._find_task_button(page, task_name, "Claim", timeout=5)
        if not claim_button:
            raise TaskExecutionError(
                f"{self.browser_id} {task_name} 查找 Claim 按钮失败"
            )
        claim_button.click()

        if not self._find_claimed_ele(page, task_name):
            raise TaskExecutionError(f"{self.browser_id} 领取任务失败")

        return True

    def _random_choose_unclaimed_ele(self, page) -> bool:
        flame_eles = page.eles(
            "x://div[contains(text(), 'Trending')]/following-sibling::div//div[contains(@class, 'rounded-[16px]')]/div[last()]/div[last()]",
            timeout=5,
        )
        if not flame_eles:
            raise TaskExecutionError(
                f"{self.browser_id} 查找 Trending 下的卡片元素失败"
            )
        random.shuffle(flame_eles)
        # 如果火焰图标未点击，则返回火焰图标元素
        for ele in flame_eles:
            clicked_flame = ele.ele(
                "x://*[name()='svg'][contains(@class, 'lucide-flame') and @fill='currentColor']/*[name()='path']",
                timeout=0.5,
            )
            if clicked_flame:
                continue

            return ele

        raise TaskExecutionError(f"{self.browser_id} 未找到未点击的火焰图标")

    def _click_flame(self, page) -> bool:
        """点击火焰图标

        Args:
            page: 页面对象
            timeout: 验证超时时间，默认60秒

        Returns:
            bool: 是否成功点击并验证
        """

        # 选择并点击元素
        trending_ele = self._random_choose_unclaimed_ele(page)
        trending_ele.click()
        sleep(8)
        self._sign_okx_wallet()

        # 验证点击结果
        clicked_flame = trending_ele.ele(
            "x://*[name()='svg'][contains(@class, 'lucide-flame') and @fill='currentColor']/*[name()='path']",
            timeout=60,
        )
        if clicked_flame:
            logger.info(f"{self.browser_id} 火焰图标被点击成功")
            return True

        return False

    def _click_and_verify_flame(self, page) -> bool:
        """点击火焰图标并验证是否点击成功

        Args:
            page: 页面对象
            timeout: 验证超时时间，默认60秒

        Returns:
            bool: 是否成功点击并验证
        """
        # 检查是否已经点击过
        if self._check_flame_clicked(page, timeout=3):
            logger.info(f"{self.browser_id} 已经有火焰图标被点击")
            return True

        # 选择并点击元素
        trending_ele = self._random_choose_trending_ele(page)
        trending_ele.click()
        sleep(3)
        self._sign_okx_wallet()

        # 验证点击结果
        if self._check_flame_clicked(page, timeout=60):
            logger.info(f"{self.browser_id} 火焰图标被点击成功")
            return True

        return False

    def _task_fire_icon(self, page) -> bool:
        """点击火焰图标任务"""
        try:
            _task_name = "First HIT"
            if self._find_claimed_ele(page, _task_name, timeout=3):
                logger.success(f"{self.browser_id} {_task_name}任务已领取")
                return True

            _task_button = self._find_task_button(page, _task_name, timeout=5)
            if not _task_button:
                raise TaskExecutionError(
                    f"{self.browser_id} {_task_name} 查找任务按钮失败"
                )

            _task_text = _task_button.text
            if _task_text == "Claimed":
                logger.success(f"{self.browser_id} {_task_name}任务已领取")
                return True

            if _task_text == "GO":
                _task_button.click()
                if not self._click_and_verify_flame(page):
                    raise TaskExecutionError(f"{self.browser_id} 火焰图标点击失败")
                return self._claim_task(page, _task_name)

            if _task_text == "Claim":
                _task_button.click()
                if not self._find_claimed_ele(page, _task_name):
                    raise TaskExecutionError(f"{self.browser_id} 领取任务失败")
                return True

            raise TaskExecutionError(f"{self.browser_id} {_task_name} 任务按钮状态错误")

        except Exception as e:
            raise TaskExecutionError(
                f"{self.browser_id} 点击火焰图标任务失败: {str(e)}"
            )

    def _get_random_song_prompt(self) -> str:
        """随机生成创建歌曲的提示词"""
        themes = [
            "happy",
            "energetic",
            "relaxing",
            "romantic",
            "dreamy",
            "upbeat",
            "nostalgic",
            "mysterious",
            "magical",
            "adventurous",
            "cosmic",
            "ethereal",
            "tropical",
            "urban",
            "cinematic",
            "dramatic",
            "spiritual",
            "festive",
            "meditative",
            "epic",
        ]
        genres = [
            "pop",
            "rock",
            "jazz",
            "electronic",
            "classical",
            "folk",
            "hip-hop",
            "R&B",
            "indie",
            "ambient",
            "soul",
            "funk",
            "blues",
            "country",
            "reggae",
            "metal",
            "techno",
            "house",
            "trap",
            "dubstep",
            "latin",
            "world music",
            "fusion",
        ]
        moods = [
            "cheerful",
            "peaceful",
            "exciting",
            "emotional",
            "inspiring",
            "melancholic",
            "euphoric",
            "serene",
            "passionate",
            "playful",
            "contemplative",
            "triumphant",
            "whimsical",
            "intense",
            "dreamy",
            "ethereal",
            "mysterious",
            "energetic",
            "relaxed",
            "romantic",
            "nostalgic",
            "powerful",
            "gentle",
            "dramatic",
        ]
        instruments = [
            "piano",
            "guitar",
            "violin",
            "drums",
            "synthesizer",
            "saxophone",
            "strings",
            "bass",
            "trumpet",
            "flute",
            "cello",
            "harp",
            "percussion",
            "electric guitar",
            "acoustic guitar",
            "organ",
            "clarinet",
            "xylophone",
            "marimba",
            "steel drums",
            "sitar",
            "banjo",
            "mandolin",
            "accordion",
            "harmonica",
        ]
        tempos = [
            "slow",
            "medium",
            "fast",
            "upbeat",
            "gentle",
            "dynamic",
            "moderate",
            "energetic",
            "relaxed",
            "driving",
            "pulsing",
            "flowing",
            "steady",
            "varying",
            "progressive",
            "building",
            "accelerating",
            "rhythmic",
        ]
        styles = [
            "modern",
            "retro",
            "futuristic",
            "minimalist",
            "orchestral",
            "acoustic",
            "electronic",
            "experimental",
            "traditional",
            "avant-garde",
            "progressive",
            "psychedelic",
            "symphonic",
            "tribal",
            "industrial",
            "atmospheric",
            "lo-fi",
            "hi-fi",
            "vintage",
            "contemporary",
        ]
        time_signatures = ["4/4", "3/4", "6/8", "5/4", "7/8"]
        effects = [
            "reverb",
            "echo",
            "delay",
            "distortion",
            "chorus",
            "phaser",
            "flanger",
            "compression",
            "filter",
            "modulation",
            "ambient",
            "spacious",
        ]

        detailed_prompts = [
            f"Create a {random.choice(themes)} {random.choice(genres)} song with prominent {random.choice(instruments)} and {random.choice(moods)} atmosphere, featuring {random.choice(effects)} effects",
            f"Compose a {random.choice(tempos)} {random.choice(genres)} track in {random.choice(time_signatures)} time featuring {random.choice(instruments)}, perfect for a {random.choice(moods)} moment",
            f"Generate a {random.choice(styles)} {random.choice(genres)} melody with {random.choice(instruments)} that feels {random.choice(moods)} and {random.choice(themes)}, incorporating {random.choice(effects)}",
            f"Make a {random.choice(moods)} {random.choice(genres)} song with {random.choice(instruments)} lead, {random.choice(tempos)} tempo, and {random.choice(themes)} vibes in {random.choice(time_signatures)}",
            f"Create a {random.choice(styles)} fusion of {random.choice(genres)} and {random.choice(genres)} with {random.choice(moods)} emotions and {random.choice(instruments)} solos using {random.choice(effects)}",
            f"Compose an immersive {random.choice(genres)} experience with {random.choice(instruments)}, {random.choice(moods)} undertones, and a {random.choice(themes)} atmosphere in {random.choice(time_signatures)}",
            f"Design a {random.choice(tempos)} {random.choice(genres)} journey featuring {random.choice(instruments)} and {random.choice(instruments)}, expressing {random.choice(moods)} feelings",
            f"Craft a {random.choice(themes)} soundscape blending {random.choice(genres)} elements with {random.choice(instruments)}, creating a {random.choice(moods)} atmosphere with {random.choice(effects)}",
            f"Produce a {random.choice(styles)} {random.choice(genres)} composition with {random.choice(instruments)} and {random.choice(effects)}, evoking a {random.choice(moods)} feeling",
            f"Create an innovative {random.choice(genres)} piece featuring {random.choice(instruments)} and {random.choice(instruments)}, with {random.choice(effects)} in a {random.choice(time_signatures)} rhythm",
            f"Compose a {random.choice(moods)} {random.choice(genres)} track that combines {random.choice(instruments)} with {random.choice(effects)} in a {random.choice(styles)} style",
            f"Generate a {random.choice(themes)} musical journey in {random.choice(time_signatures)}, featuring {random.choice(instruments)} and {random.choice(effects)} effects",
        ]

        return random.choice(detailed_prompts)

    def _task_create_song(self, page) -> bool:
        """创建歌曲任务"""
        try:
            _task_name = "Welcome, DJ"
            if self._find_claimed_ele(page, _task_name, timeout=3):
                logger.success(f"{self.browser_id} {_task_name}任务已领取")
                return True

            _task_button = self._find_task_button(page, _task_name, timeout=5)
            if not _task_button:
                raise TaskExecutionError(
                    f"{self.browser_id} {_task_name} 查找任务按钮失败"
                )

            _task_text = _task_button.text
            if _task_text == "Claimed":
                logger.success(f"{self.browser_id} {_task_name}任务已领取")
                return True

            if _task_text == "Claim":
                _task_button.click()
                if not self._find_claimed_ele(page, _task_name):
                    raise TaskExecutionError(f"{self.browser_id} 领取任务失败")
                return True

            if self._find_claimed_ele(page, _task_name, timeout=3):
                logger.info(f"{self.browser_id} {_task_name}任务已领取")
                return True

            if _task_text == "GO":
                _task_button.click()
                sleep(3)

                # 输入内容
                input_field = page.ele("x://textarea", timeout=10)
                if not input_field:
                    raise TaskExecutionError(f"{self.browser_id} 查找输入框失败")

                input_field.input(self._get_random_song_prompt())

                # 点击 Create 按钮
                create_song_button = page.ele(
                    "x://button/div[text()='Create']", timeout=5
                )
                if not create_song_button:
                    raise TaskExecutionError(f"{self.browser_id} 查找创建歌曲按钮失败")

                create_song_button.click()

                generating_text = page.ele("Music generated successfully", timeout=120)
                if not generating_text:
                    raise TaskExecutionError(f"{self.browser_id} 歌曲生成超时")

                logger.success(f"{self.browser_id} 歌曲生成成功")

                return self._claim_task(page, _task_name)

            raise TaskExecutionError(f"{self.browser_id} {_task_name} 任务按钮状态错误")

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 创建歌曲任务失败: {str(e)}")

    def _check_badge_claimed(self, page, timeout: int = 10) -> bool:
        """检查徽章是否已领取"""
        try:
            res = page.listen.wait(timeout=timeout)
            if res and res.response:
                body = json.loads(res.response.raw_body)
                return body.get("data").get("claimStatus")
        except Exception as e:
            logger.error(f"{self.browser_id} 检查徽章领取状态失败: {str(e)}")
            return False

    def _claim_badge(self, page) -> bool:
        """领取徽章"""
        try:
            # 打开徽章领取页面
            page.listen.start(self.listen_api_url)
            page.get(self.badge_url)
            sleep(3)

            # 连接钱包
            if not self._connect_wallet_badge_page(page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 检查徽章是否已领取
            if self._check_badge_claimed(page, timeout=3):
                logger.success(f"{self.browser_id} 徽章已领取")
                self._update_task_status(True)
                return True

            # 点击领取按钮
            claim_button = page.ele("x://span[text()='Claim Badge']", timeout=5)
            if not claim_button:
                raise TaskExecutionError(f"{self.browser_id} 查找领取徽章按钮失败")

            claim_button.click()
            self._sign_okx_wallet()

            # 等待领取结果
            success_text = page.ele("You've claimed a badge", timeout=60)
            if not success_text:
                raise TaskExecutionError(f"{self.browser_id} 领取徽章失败")

            self._update_task_status(True)
            return True

        except Exception as e:
            raise TaskExecutionError(f"{self.browser_id} 领取徽章失败: {str(e)}")

    def _execute_task(self, task_func, task_name: str) -> bool:
        """执行单个任务的通用方法"""
        try:
            result = task_func(self.browser.page)
            if result:
                logger.success(f"{self.browser_id} {task_name} 成功")
            return result
        except Exception as e:
            logger.error(f"{self.browser_id} {task_name}失败: {e}")
            return False

    def _odyssey_star(self, page) -> bool:
        """奥德赛歌曲点赞"""
        try:
            page.get(self.home_url_odyssey)
            return self._click_flame(page)
        except Exception as e:
            logger.error(f"{self.browser_id} 奥德赛歌曲点赞失败: {e}")
            return False

    def _odyssey_check_in(self, page) -> bool:
        """奥德赛签到"""
        try:
            self.browser.open_url("https://soloai.io/reward/")

            _task_name = "Daily Beat-Drop"
            if self._find_claimed_ele(page, _task_name, timeout=3):
                logger.success(f"{self.browser_id} {_task_name}任务已领取")
                return True

            check_in_button = page.ele(
                "x://div[contains(text(), 'Check In')]", timeout=5
            )

            if not check_in_button:
                raise TaskExecutionError(f"{self.browser_id} 查找签到按钮失败")
            check_in_button.click()
            sleep(6)
            self._sign_okx_wallet()
            sleep(3)

            if self._find_claimed_ele(page, _task_name, timeout=3):
                logger.success(f"{self.browser_id} {_task_name} 成功")
                return True

            logger.warning(f"{self.browser_id} 签到失败")
            return False

        except Exception as e:
            logger.error(f"{self.browser_id} 奥德赛签到失败: {e}")
            return False

    def _select_story_starts(self, page) -> bool:
        """选择 STORY STARTS 活动

        Args:
            page: 浏览器页面对象

        Returns:
            bool: 是否成功选择

        Raises:
            TaskExecutionError: 当找不到或点击按钮失败时抛出
        """

        story_starts = self._find_task_button(page, "STORY STARTS", timeout=5)
        if not story_starts:
            raise TaskExecutionError(f"{self.browser_id} 查找 STORY STARTS 按钮失败")

        if story_starts.text == "Mint Badge":
            story_starts.next().click()
            return True

        story_starts.click()
        return True

    @retry(tries=3, delay=1)
    def task_odyssey(self) -> bool:
        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.browser.page)

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 定义任务列表
            tasks = [
                (self._odyssey_check_in, "奥德赛签到"),
                (self._odyssey_star, "奥德赛歌曲点赞"),
            ]

            # 随机执行
            random.shuffle(tasks)
            for task_func, task_name in tasks:
                self._execute_task(task_func, task_name)

            return True

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Soloai任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Soloai任务失败")

    @retry(tries=3, delay=1)
    def task(self) -> bool:
        """执行Soloai任务"""
        if self._check_task_completed():
            return True

        try:
            # 登录钱包
            if not self._login_wallet():
                raise WalletConnectionError(f"{self.browser_id} 登录钱包失败")

            # 打开任务页面
            self.browser.page.listen.start(self.listen_api_url)
            self.browser.open_url(self.home_url)
            self._close_other_tabs(self.browser.page)

            # 连接钱包
            if not self._connect_wallet(self.browser.page):
                raise WalletConnectionError(f"{self.browser_id} 连接钱包失败")

            # 检查徽章是否已领取
            if self._check_badge_claimed(self.browser.page):
                logger.success(f"{self.browser_id} 徽章已领取")
                self._update_task_status(True)
                return True

            # gas低了再启用
            gas_prices = self.get_gas_price()
            if gas_prices and gas_prices.average > MAX_GAS_PRICE:
                logger.warning(f"{self.browser_id} gas价格高，跳过铸造")
                return False

            mint_badge = self.browser.page.ele("Mint Badge", timeout=3)
            if mint_badge:
                logger.info(f"{self.browser_id} 任务已经完成，进行徽章领取")
                mint_badge.click()
                self._claim_badge(self.browser.page)
                return True

            # 选择 STORY STARTS 活动
            self._select_story_starts(self.browser.page)

            # 定义任务列表
            tasks = [
                (self._task_follow_twitter, "关注推特"),
                (self._task_fire_icon, "点击火焰图标"),
                (self._task_create_song, "创建歌曲"),
                (self._claim_badge, "领取徽章"),
            ]

            # 执行任务列表
            for task_func, task_name in tasks:
                if task_name == "领取徽章":
                    result = self._execute_task(task_func, task_name)
                    if result:
                        logger.success(f"{self.browser_id} Soloai任务完成")
                    else:
                        logger.warning(f"{self.browser_id} Soloai任务未完成")
                    return result
                else:
                    self._execute_task(task_func, task_name)

            return False

        except WalletConnectionError as e:
            logger.error(f"{self.browser_id} 钱包连接失败: {e}")
            raise
        except Exception as e:
            logger.error(f"{self.browser_id} Soloai任务失败: {e}")
            raise TaskExecutionError(f"{self.browser_id} Soloai任务失败")
