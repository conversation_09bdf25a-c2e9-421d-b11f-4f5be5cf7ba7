import os
from time import sleep

import click
from DrissionPage import Chromium
from loguru import logger
import io

from PIL import Image
import base64
from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.operations import try_click
from src.controllers import Browser<PERSON>ontroller
from src.socials.discord import Discord
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element, get_elements
from src.utils.remove_bg import remove_background
import requests
from pathlib import Path
from datetime import datetime
import random
from src.controllers.mixins.discord_mixin import DiscordMixin

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/zama_discord.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")
servers_add = [{"name": "galxe", "server_id": "824767871183355954", "invite_code": "galxe"}]
servers_leave = [{"name": "galxe", "server_id": "824767871183355954", "invite_code": "galxe"}]


def get_cap_code(lasted_tab, id):
    try:
        i = 0
        for index, packet in enumerate(lasted_tab.listen.steps(timeout=60)):
            logger.info(f"【{id}】 获取网络数据包{index}")
            body = packet.response.raw_body
            png_dir = Path("examples") / "zama" / "png"
            png_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {png_dir}")

            if packet.response._is_base64_body:
                img_path = os.path.join(png_dir, f"{id}_{i}.png")
                with open(img_path, "wb") as f:
                    if isinstance(body, str):
                        body = base64.b64decode(body)
                    f.write(body)
                logger.info(f"【{id}】已保存验证码图片: {img_path}")
                i = i + 1
                logger.info(f"【{id}】开始图像识别")
                from examples.cap.yolo_captcha_solver import YOLOCaptchaSolver

                # 识别第一张图片
                img_path = os.path.join(png_dir, f"{id}_0.png")
                model_path = r"examples\cap\model\dc_zama.pt"
                abs_img_path = os.path.abspath(img_path)
                remove_background(abs_img_path, abs_img_path)
                image = open(img_path, "rb").read()
                solver = YOLOCaptchaSolver(model_path=model_path, img_size=320, conf_threshold=0.5)
                result = solver.predict(img_path)
                result = result.lower()
                logger.info(f"【{id}】识别结果: {result}")
                # 重命名图片为识别结果.png
                new_img_path = os.path.join(png_dir, f"{result}.png")
                try:
                    os.rename(abs_img_path, new_img_path)
                    logger.info(f"【{id}】图片已重命名为: {new_img_path}")
                except Exception as e:
                    logger.error(f"【{id}】图片重命名失败: {e}")
                return result
            else:
                logger.warning(f"【{id}】Received non-PNG image data.")
    except Exception as e:
        logger.error(f"获取验证码报错:{e}")
        return False


def cap_solve(self):
    """
    处理 Discord 验证码，browser 为当前 BrowserController 实例。
    """
    try:
        logger.info("开始进入cap")
        lasted_tab = self._page.latest_tab
        lasted_tab.listen.start(
            [
                "https://media.discordapp.net/ephemeral-attachments",
            ]
        )
        id = self._page.id
        if "https://discord.com/channels/1287736665103798433" not in lasted_tab.url:
            lasted_tab.get("https://discord.com/channels/1287736665103798433")

        lasted_tab.get("https://discord.com/channels/1287736665103798433/1290609001473507329")

        if not try_click(lasted_tab, "x://div[contains(text(), 'Verify')]"):
            logger.error(f"【{id}】未找到 Verify 按钮")
            return False
        code = get_cap_code(lasted_tab, id)
        if len(code) > 6:
            code = code[:6]
        if not try_click(lasted_tab, "x://div[contains(text(), 'Answer')]"):
            logger.error(f"【{id}】未找到 Answer 区域")
            return False
        sleep(3)
        answer_ele = lasted_tab.ele("x://div[contains(text(), 'Answer')]")
        if not answer_ele.input(code):
            logger.error(f"【{id}】验证码输入失败")
            return False
        sleep(3)
        sub_ele = get_elements(lasted_tab, "x://div[contains(text(), 'Submit')]")
        found_clickable = False
        for ele in sub_ele:
            if ele.states.is_clickable:
                found_clickable = True
                if not try_click(lasted_tab, ele):
                    logger.error(f"【{id}】点击提交按钮失败")
                    return False
                break  # 找到可点击元素并点击成功后跳出循环
        if not found_clickable:
            logger.error(f"【{id}】未找到可点击的提交按钮")
            return False
        logger.success(f"【{id}】验证码处理流程完成")
        if not lasted_tab.ele("x://span[contains(text(), 'You have been verified!')]"):
            # 记录失败的验证码信息到cap.log文件
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cap_log_path = Path("examples") / "zama" / "cap.log"
            cap_log_path.parent.mkdir(parents=True, exist_ok=True)

            with open(cap_log_path, "a", encoding="utf-8") as f:
                f.write(f"【{id}】验证码认证失败 - 时间: {current_time}, 验证码: {code}, 浏览器ID: {id}\n")

            logger.error(f"【{id}】验证码认证失败 - 时间: {current_time}, 验证码: {code}, 浏览器ID: {id}")
            return False
        sleep(5)
        return True
    except Exception as e:
        logger.error(f"获取验证码报错:{e}")
        return False


def _handle_join_server(
    browser: BrowserController,
    invite_code: str,
    need_wait_captcha: bool,
    server_id: str,
    name: str,
):
    # 方案一：zama服务器特殊处理
    if server_id == "1287736665103798433" or invite_code == "zama":
        for i in range(4):
            try:
                from examples.zama.discord import ZamaDiscord

                index = browser.id
                zama_discord = ZamaDiscord(browser.id, browser.page, browser.browser_type)
                result = zama_discord.join_server(need_wait_captcha, i > 0)  # 第二次开始使用again_flag
                is_success = result.get("success")
                if is_success:
                    return {"success": True, "message": "加入成功"}
                result_code = result.get("code")
                if result_code == "NO_LOGIN":
                    result = browser.login_discord()
                    if not result:
                        return {"success": False, "message": "dc未登录，登录失败"}
                elif result_code in ["PRE_VERIFY_FAILED", "VERIFY_FAILED", "GET_ROLES_FAILED", "SEND_GM_FAILED"]:
                    if i < 1:
                        _leave_dc_server(browser.browser_type, browser.id, 1, False)
                if i == 1:
                    # 加其它群
                    row = random.choice(servers_add)
                    invite_code = row.get("invite_code")
                    server_id = row.get("server_id")
                    name = row.get("name")
                    need_wait_captcha = False
                    result = browser.join_dc_server(invite_code, need_wait_captcha, server_id, name)
                    is_success = result.get("success")
                    if is_success:
                        return {"success": True, "message": "加入成功"}
                    result_code = result.get("code")
                    if result_code == "NO_LOGIN":
                        result = browser.login_discord()
                        if not result:
                            return {"success": False, "message": "dc未登录，登录失败"}
                    elif result_code == "NEED_CAPTCHA":
                        _leave_dc_server(browser.browser_type, index, 1, False)
                if i == 2:
                    if not browser.update_password_discord():
                        logger.info("更新密码失败")

                # 待优化
                # if i == 3:
                #     if not browser.reset_password():
                #       logger.info("重置密码失败")
            except Exception as e:
                logger.error(f"【{browser.id}】Zama服务器加群异常: {e}")
                _leave_dc_server(browser.browser_type, browser.id, 1, False)
        return {"success": False, "message": "Zama服务器加入失败"}
    # 其他服务器保持原有逻辑
    # for i in range(3):
    #     index = browser.id
    #     result = browser.join_dc_server(invite_code, need_wait_captcha, server_id, name)
    #     is_success = result.get("success")
    #     if is_success:
    #         return {"success": True, "message": "加入成功"}
    #     result_code = result.get("code")
    #     if result_code == "NO_LOGIN":
    #         result = browser.login_discord()
    #         if not result:
    #             return {"success": False, "message": "dc未登录，登录失败"}
    #     elif result_code == "NEED_CAPTCHA":
    #         _leave_dc_server(browser.browser_type, index, 1, False)
    #     if i == 1:
    #         result = browser.join_dc_server(invite_code, need_wait_captcha, server_id, name, True)
    #         is_success = result.get("success")
    #         if is_success:
    #             return {"success": True, "message": "加入成功"}
    #         result_code = result.get("code")
    #         if result_code == "NO_LOGIN":
    #             result = browser.login_discord()
    #             if not result:
    #                 return {"success": False, "message": "dc未登录，登录失败"}
    #         elif result_code == "NEED_CAPTCHA":
    #             _leave_dc_server(browser.browser_type, index, 1, False)
    return {"success": False, "message": "加入失败"}


def _join_dc_server(
    type: BrowserType,
    index: str,
    need_wait_captcha: bool = True,
    close_page: bool = True,
):
    browser = BrowserController(type, str(index))
    address = browser.browser_config.evm_address

    data_dir = os.path.join(get_project_root_path(), "examples", "zama")
    csv_path = os.path.join(data_dir, f"zama_{type.name.lower()}.csv")
    data_util = DataUtil(csv_path)

    record = data_util.get(address) or {}
    servers = [
        # {
        #     "name": "Pixcape Games",
        #     "server_id": "1367481863270174811",
        #     "invite_code": "pixcapegames",
        # },
        {"name": "zama", "server_id": "1287736665103798433", "invite_code": "zama"}
    ]

    fail_servers = []
    for server in servers:
        invite_code = server.get("invite_code")
        server_id = server.get("server_id")
        name = server.get("name")
        try:
            if record.get(f"Discord {name}", "") == "1":
                logger.success(f"【{index}】已加入 {name} 服务器")
                continue

            result = _handle_join_server(browser, invite_code, need_wait_captcha, server_id, name)
            if not result.get("success"):
                logger.error(f"【{index}】加入 {name} 服务器失败: {result.get('message')}")
                fail_servers.append(invite_code)
                continue

            data_util.update(address, {f"Discord {name}": "1", "index": index, "type": type.name})
            logger.success(f"【{index}】加入 {name} 服务器成功")

        except Exception as e:
            logger.error(f"{index} 加群 {name} 失败: {e}")
            fail_servers.append(invite_code)
    if close_page:
        browser.close_page()
    return fail_servers


def _leave_dc_server(type: BrowserType, index: str, count=1, is_close=True):
    browser = BrowserController(type, str(index))
    servers = [
        "Stable Jack",
        "Salt",
        "Haifu",
        "QRusader",
        "WarpGate Official",
        "EnsoFi",
    ]
    success_count = 0
    for name in servers:
        try:
            discord = Discord(index, browser.page, browser.browser_type)
            result = discord.leave_guild(name)
            if result:
                success_count = success_count + 1
                # logger.info("success_count is: " + str(success_count))
                if count == success_count:
                    if is_close:
                        browser.close_page()
                    return True
        except Exception as e:
            logger.error(f"{index} 退出群组 {name} 失败: {e}")
    if is_close:
        browser.close_page()
    return False


class ZamaDiscord(DiscordMixin, Discord):
    """
    专用于Zama服务器的Discord自动加群与验证码处理类
    """

    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "zama"
        server_id = "1287736665103798433"
        super().__init__(id, page, browser_type, invite_code, "Zama")
        self.server_id = server_id

    def join_server(self, need_wait_captcha: bool = True, again_flag: bool = False) -> dict:
        """
        加入Zama服务器并自动处理验证码
        Returns: dict, 包含success和message
        """
        # 1. 打开邀请链接并点击加入
        if again_flag:
            result = self._accept_invite_two()
        else:
            result = self._accept_invite(need_wait_captcha, self.server_id)
        is_success = result.get("success")
        if not is_success:
            # 第一步失败时退群清理
            if not again_flag:
              _leave_dc_server(self.browser_type, self.id, 1, False)
            return result

        # 2. 处理验证前置条件
        if not self._pre_verify():
            # 验证前置条件失败时退群清理
            # _leave_dc_server(self.browser_type, self.id, 1, False)
            return {"success": False, "message": "处理验证前置条件失败", "code": "PRE_VERIFY_FAILED"}

        # 3. 处理验证（验证码处理已集成到_verify）
        if not self._verify():
            # 验证失败时退群清理
            #  _leave_dc_server(self.browser_type, self.id, 1, False)
            return {"success": False, "message": "处理验证失败", "code": "VERIFY_FAILED"}

        # 4. 获取角色
        if not self._get_roles():
            # 获取角色失败时退群清理
            #  _leave_dc_server(self.browser_type, self.id, 1, False)
            return {"success": False, "message": "获取角色失败", "code": "GET_ROLES_FAILED"}

        # 5. 发送GM
        if not self._gm():
            # 发送GM失败时退群清理
            #  _leave_dc_server(self.browser_type, self.id, 1, False)
            return {"success": False, "message": "发送GM失败", "code": "SEND_GM_FAILED"}

        logger.success(f"【{self.id}】加入 {self.dc_name} 服务器成功")
        return {"success": True, "message": "加入成功", "code": "SUCCESS"}

    def _verify(self) -> bool:
        from examples.zama.discord import cap_solve

        return cap_solve(self)


@click.group()
def cli():
    pass


@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--wait", is_flag=True, default=True, flag_value=False, help="是否等待验证码")
@click.option("-c", "--close", is_flag=True, default=True, flag_value=False, help="是否关闭浏览器")
def join_server(index, type, wait, close):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        try:
            fail_servers = _join_dc_server(type, _index, wait, close)
            if fail_servers and len(fail_servers) > 0:
                fail_indices.append(str(_index))
        except Exception as e:
            logger.error(f"{_index} 加群失败: {e}")
            fail_indices.append(str(_index))
    if fail_indices:
        idxs = ",".join(fail_indices)
        logger.error(f"加群失败：{idxs}")


@cli.command("leave")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def leave_server(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            result = _leave_dc_server(type, _index)
            if result:
                logger.success(f"{_index} 退出群组成功")
            else:
                logger.error(f"{_index} 退出群组失败")
        except Exception as e:
            logger.error(f"{_index} 退出群组失败: {e}")


# python3 examples/zama/discord.py mc -i 1 -t chrome
# python3 examples/zama/discord.py kl -i 1 -t chrome
# python3 examples/zama/discord.py join -t chrome -w  -i 1

if __name__ == "__main__":
    #  cli()
    _join_dc_server(BrowserType.CHROME, "196", False)

# _join_kraft_lab(BrowserType.CHROME, "4")
# _join_mullet_cop(BrowserType.CHROME, "3")
