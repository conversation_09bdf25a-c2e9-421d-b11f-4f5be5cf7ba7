"""
YOLO验证码识别工具类
用于封装YOLO模型进行验证码字符识别
"""

from typing import List, Union, Optional
from pathlib import Path
import logging
from ultralytics import YOLO

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class YOLOCaptchaSolver:
    """YOLO验证码识别工具类"""

    def __init__(self, model_path: Union[str, Path], img_size: int = 320, conf_threshold: float = 0.9):
        """
        初始化YOLO验证码识别器

        Args:
            model_path: YOLO模型文件路径(.pt文件)
            img_size: 输入图片尺寸，默认320
            conf_threshold: 置信度阈值，默认0.5
        """
        self.model_path = Path(model_path)
        self.img_size = img_size
        self.conf_threshold = conf_threshold

        # 验证模型文件是否存在
        if not self.model_path.exists():
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        try:
            # 加载YOLO模型
            self.model = YOLO(str(self.model_path))
            logger.info(f"成功加载模型: {self.model_path}")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise Exception(f"模型加载失败: {e}")

    def predict(self, image_path: Union[str, Path]) -> str:
        """
        预测单张图片的验证码

        Args:
            image_path: 图片文件路径

        Returns:
            识别出的验证码字符串
        """
        image_path = Path(image_path)

        # 验证图片文件是否存在
        if not image_path.exists():
            raise FileNotFoundError(f"图片文件不存在: {image_path}")

        try:
            # 执行预测
            results = self.model(str(image_path), imgsz=self.img_size)[0]

            # 处理结果
            captcha_text = self._process_results(results)

            logger.info(f"图片 {image_path.name} 识别结果: {captcha_text}")
            return captcha_text

        except Exception as e:
            logger.error(f"预测失败 {image_path}: {e}")
            raise Exception(f"预测失败: {e}")

    def batch_predict(self, image_paths: List[Union[str, Path]]) -> List[str]:
        """
        批量预测多张图片的验证码

        Args:
            image_paths: 图片文件路径列表

        Returns:
            识别结果列表，与输入图片顺序对应
        """
        results = []

        for i, image_path in enumerate(image_paths):
            try:
                result = self.predict(image_path)
                results.append(result)
                logger.info(f"批量处理进度: {i+1}/{len(image_paths)}")
            except Exception as e:
                logger.error(f"批量处理失败 {image_path}: {e}")
                results.append("")  # 失败时返回空字符串

        return results

    def _process_results(self, results) -> str:
        """
        处理YOLO预测结果，转换为验证码字符串

        Args:
            results: YOLO模型预测结果

        Returns:
            处理后的验证码字符串
        """
        try:
            # 按x坐标排序检测框（从左到右）
            boxes = sorted(results.boxes, key=lambda b: b.xyxy[0][0])

            # 将类别ID转换为字符（A-Z）
            chars = []
            for box in boxes:
                # 检查置信度阈值
                if hasattr(box, "conf") and box.conf[0] < self.conf_threshold:
                    continue

                class_id = int(box.cls[0])
                char = chr(class_id + 65)  # 0->A, 1->B, ..., 25->Z
                chars.append(char)

            return "".join(chars)

        except Exception as e:
            logger.error(f"结果处理失败: {e}")
            return ""

    def get_model_info(self) -> dict:
        """
        获取模型信息

        Returns:
            包含模型路径、图片尺寸、置信度阈值等信息的字典
        """
        return {
            "model_path": str(self.model_path),
            "img_size": self.img_size,
            "conf_threshold": self.conf_threshold,
            "model_loaded": hasattr(self, "model"),
        }


##
if __name__ == "__main__":
    solver = YOLOCaptchaSolver(model_path=r"examples\cap\model\dc_zama.pt", img_size=320, conf_threshold=0.5)
    result = solver.predict(r"examples\zama\png\tlxvvmn.png")
    result = result.lower()
    logger.info(result)