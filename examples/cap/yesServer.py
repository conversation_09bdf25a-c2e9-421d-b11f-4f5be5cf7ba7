import base64
import io
import json
import logging
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import unquote

import matplotlib.pyplot as plt
import pandas as pd
import requests
from flask import Flask, jsonify, request
from openai import OpenAI
from PIL import Image, ImageDraw
from prompts import (
    RECAPTCHA_QUESTION_MAP,
    RECAPTCHA_3x3_QUESTION_TEMPLATE,
    RECAPTCHA_4x4_QUESTION_TEMPLATE,
    get_system_prompt,
)
from retry import retry

NEWAPI_BASE_URL = os.getenv("NEWAPI_BASE_URL", "")
NEWAPI_KEY = os.getenv("NEWAPI_KEY", "")
# 配置主日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 配置请求日志
request_logger = logging.getLogger("request_logger")
request_logger.setLevel(logging.INFO)
request_handler = logging.FileHandler("request.log")
request_formatter = logging.Formatter("%(asctime)s - %(message)s")
request_handler.setFormatter(request_formatter)
request_logger.addHandler(request_handler)


# 配置响应日志
response_logger = logging.getLogger("response_logger")
response_logger.setLevel(logging.INFO)
response_handler = logging.FileHandler("response.log")
response_formatter = logging.Formatter("%(asctime)s - %(message)s")
response_handler.setFormatter(response_formatter)
response_logger.addHandler(response_handler)

app = Flask(__name__)

# 设置OpenAI客户端
client = OpenAI(base_url=NEWAPI_BASE_URL, api_key=NEWAPI_KEY)

# 任务类型常量
TASK_TYPES = {
    "GRID_CLASSIFY": "GRID_CLASSIFY",
    "GRID_CLICK": "GRID_CLICK",
    "POINT_CLICK": "POINT_CLICK",
    "DRAG_DROP": "DRAG_DROP",
    "DRAG_ALL": "DRAG_ALL",
    "RECAPTCHA_CLASSIFY": "RECAPTCHA_CLASSIFY",
}

# 网格坐标映射表
GRID_COORDINATES = {
    (1, 1): (278, 503),
    (1, 2): (492, 503),
    (1, 3): (706, 503),
    (2, 1): (278, 719),
    (2, 2): (492, 719),
    (2, 3): (706, 719),
    (3, 1): (279, 932),
    (3, 2): (493, 933),
    (3, 3): (707, 933),
}

# ReCaptchaV2 16位坐标映射表 (4x4网格)
RECAPTCHA_GRID_COORDINATES = {
    (1, 1): (75, 75),
    (1, 2): (150, 75),
    (1, 3): (225, 75),
    (1, 4): (300, 75),
    (2, 1): (75, 150),
    (2, 2): (150, 150),
    (2, 3): (225, 150),
    (2, 4): (300, 150),
    (3, 1): (75, 225),
    (3, 2): (150, 225),
    (3, 3): (225, 225),
    (3, 4): (300, 225),
    (4, 1): (75, 300),
    (4, 2): (150, 300),
    (4, 3): (225, 300),
    (4, 4): (300, 300),
}

# 调用次数限制
FLASH_LIMIT = 9500
PRO_LIMIT = 900

# 任务类型统计
TASK_TYPE_STATS = {"DRAG_DROP": 0, "OTHER": 0}

# CSV文件路径
CSV_PATH = r"examples\cap\cap.csv"


def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(CSV_PATH):
        df = pd.DataFrame(columns=["date", "flash", "pro"])
        df.to_csv(CSV_PATH, index=False)


def get_today_counts():
    """获取今天的调用次数"""
    today = datetime.now().strftime("%Y-%m-%d")
    if not os.path.exists(CSV_PATH):
        return 0, 0

    df = pd.read_csv(CSV_PATH)
    today_data = df[df["date"] == today]

    if today_data.empty:
        return 0, 0

    return today_data["flash"].iloc[0], today_data["pro"].iloc[0]


def update_counts(flash_count: int, pro_count: int):
    """更新调用次数"""
    today = datetime.now().strftime("%Y-%m-%d")

    if not os.path.exists(CSV_PATH):
        df = pd.DataFrame({"date": [today], "flash": [flash_count], "pro": [pro_count]})
    else:
        df = pd.read_csv(CSV_PATH)
        today_data = df[df["date"] == today]

        if today_data.empty:
            new_row = pd.DataFrame({"date": [today], "flash": [flash_count], "pro": [pro_count]})
            df = pd.concat([df, new_row], ignore_index=True)
        else:
            df.loc[df["date"] == today, "flash"] = flash_count
            df.loc[df["date"] == today, "pro"] = pro_count

    df.to_csv(CSV_PATH, index=False)


def check_limits(model_type: str) -> bool:
    """检查是否超过限额"""
    flash_count, pro_count = get_today_counts()

    if model_type == "flash" and flash_count >= FLASH_LIMIT:
        logger.error(f"Flash model daily limit ({FLASH_LIMIT}) exceeded")
        return False
    elif model_type == "pro" and pro_count >= PRO_LIMIT:
        logger.error(f"Pro model daily limit ({PRO_LIMIT}) exceeded")
        return False

    return True


def increment_count(model_type: str):
    """增加调用计数"""
    flash_count, pro_count = get_today_counts()

    if model_type == "flash":
        flash_count += 1
    else:
        pro_count += 1

    update_counts(flash_count, pro_count)


def map_grid_coordinates(grid_positions: list[tuple[int, int]]) -> list[str]:
    """将网格位置映射为实际坐标"""
    mapped_coordinates = []
    for row, col in grid_positions:
        if (row, col) in GRID_COORDINATES:
            x, y = GRID_COORDINATES[(row, col)]
            mapped_coordinates.append(str(x))
            mapped_coordinates.append(str(y))
    return mapped_coordinates


def map_recaptcha_coordinates(grid_positions: list[tuple[int, int]]) -> list[str]:
    """将ReCaptchaV2网格位置映射为实际坐标（16位）"""
    mapped_coordinates = []
    for row, col in grid_positions:
        if (row, col) in RECAPTCHA_GRID_COORDINATES:
            x, y = RECAPTCHA_GRID_COORDINATES[(row, col)]
            mapped_coordinates.append(str(x))
            mapped_coordinates.append(str(y))
    return mapped_coordinates


def parse_grid_positions(response_text: str) -> list[tuple[int, int]]:
    """从响应文本中解析网格位置"""
    try:
        # 尝试解析JSON响应
        response_data = json.loads(response_text)
        if not isinstance(response_data, dict):
            return []

        # 获取solution字段
        solution = response_data.get("solution", {})
        if not solution:
            return []

        # 获取box字段
        box = solution.get("box", [])
        if not box:
            return []

        # 解析坐标对
        positions = []
        for item in box:
            try:
                if isinstance(item, list) and len(item) == 2:
                    # 处理列表格式 [row, col]
                    row, col = item
                    if 1 <= row <= 3 and 1 <= col <= 3:
                        positions.append((row, col))
                elif isinstance(item, (str, int)):
                    # 处理字符串或数字格式
                    if len(box) >= 2:
                        for i in range(0, len(box), 2):
                            if i + 1 < len(box):
                                try:
                                    row = int(box[i])
                                    col = int(box[i + 1])
                                    if 1 <= row <= 3 and 1 <= col <= 3:
                                        positions.append((row, col))
                                except (ValueError, TypeError):
                                    continue
                        break
            except (ValueError, TypeError):
                continue

        return positions
    except json.JSONDecodeError:
        return []


def process_grid_click_response(response_text: str) -> dict[str, Any]:
    """处理网格点击任务的响应"""
    try:
        # 解析原始响应
        response_data = json.loads(response_text)

        # 获取网格位置
        grid_positions = parse_grid_positions(response_text)

        # 映射为实际坐标
        mapped_coordinates = map_grid_coordinates(grid_positions)

        # 更新响应数据
        if "solution" in response_data:
            response_data["solution"]["box"] = mapped_coordinates

        return response_data
    except Exception as e:
        logger.error(f"Error processing grid click response: {str(e)}")
        raise


def validate_task_type(question: str, queries: list[str], anchors: list[str], task_type: str = None) -> str:
    """根据输入参数确定任务类型"""
    # 如果提供了task_type参数，优先使用它
    if task_type == "ReCaptchaV2Classification":
        return TASK_TYPES["RECAPTCHA_CLASSIFY"]

    # 检查是否为ReCaptchaV2Classification任务（单图片 + Google question格式）
    if len(queries) == 1 and not anchors and question.startswith("/m/"):
        return TASK_TYPES["RECAPTCHA_CLASSIFY"]
    elif len(queries) > 1 and not anchors:
        return TASK_TYPES["GRID_CLASSIFY"]
    elif len(queries) == 1 and anchors:  # 您的图片属于此类型
        return TASK_TYPES["GRID_CLICK"]
    # elif len(queries) == 1 and not anchors:  # 您的图片属于此类型
    #     return TASK_TYPES["GRID_CLICK"]
    elif len(queries) == 1 and ("drag" in question.lower() or "拖" in question.lower()):
        if "缺失" in question.lower():
            return TASK_TYPES["DRAG_ALL"]
        return TASK_TYPES["DRAG_DROP"]
    elif len(queries) == 1:
        return TASK_TYPES["POINT_CLICK"]
    return TASK_TYPES["GRID_CLASSIFY"]  # 默认返回标准网格分类


def validate_response_format(response: dict[str, Any], task_type: str) -> bool:
    """验证响应格式是否符合要求"""
    try:
        # 验证基本字段
        required_fields = ["errorId", "errorCode", "status", "solution"]
        for field in required_fields:
            if field not in response:
                logger.error(f"Missing required field: {field}")
                return False

        solution = response["solution"]
        if not isinstance(solution, dict):
            logger.error("Solution field is not a valid object")
            return False

        # 根据任务类型验证特定字段
        if task_type == TASK_TYPES["GRID_CLASSIFY"]:
            required_solution_fields = ["label", "objects", "top_k", "confidences"]
            for field in required_solution_fields:
                if field not in solution:
                    logger.error(f"Missing required field in solution: {field}")
                    return False

            # 验证 objects 数组
            if not isinstance(solution["objects"], list) or len(solution["objects"]) != 9:
                logger.error("Objects array must contain exactly 9 boolean values")
                return False
            if not all(isinstance(x, bool) for x in solution["objects"]):
                logger.error("Objects array must contain only boolean values")
                return False

            # 验证 top_k 数组
            if not isinstance(solution["top_k"], list):
                logger.error("top_k must be a list")
                return False

            # 验证 confidences 数组
            if not isinstance(solution["confidences"], list) or len(solution["confidences"]) != 9:
                logger.error("Confidences array must contain exactly 9 float values")
                return False
            if not all(isinstance(x, (int, float)) for x in solution["confidences"]):
                logger.error("Confidences array must contain only numeric values")
                return False

        elif task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
            # 新版API兼容：type:multi（objects为索引数组）和type:single（hasObject为bool）
            solution_type = solution.get("type")
            if solution_type == "multi":
                objects = solution.get("objects")
                if not isinstance(objects, list):
                    logger.error("[RECAPTCHA_CLASSIFY] type=multi: objects must be a list of indices")
                    return False
                # 校验索引类型和范围（允许0~15，兼容3x3/4x4）
                for idx in objects:
                    if not isinstance(idx, int) or not (0 <= idx <= 15):
                        logger.error(
                            f"[RECAPTCHA_CLASSIFY] type=multi: invalid object index {idx}, must be int in 0~15"
                        )
                        return False
                # 其它字段可选，不强制校验
                return True
            elif solution_type == "single":
                has_object = solution.get("hasObject")
                if not isinstance(has_object, bool):
                    logger.error("[RECAPTCHA_CLASSIFY] type=single: hasObject must be a boolean value")
                    return False
                return True
            else:
                logger.error(f"[RECAPTCHA_CLASSIFY] unknown or missing type: {solution_type}")
                return False

        elif task_type in ["GRID_CLICK", "POINT_CLICK"]:
            if "box" not in solution or not isinstance(solution["box"], list):
                logger.error("Invalid box format for point-click task")
                return False
        elif task_type == TASK_TYPES["DRAG_DROP"]:
            if not all(key in solution for key in ["type", "question", "box"]):
                logger.error("Missing required fields for drag-and-drop task")
                return False
            if solution["type"] != "drag":
                logger.error("Invalid type for drag-and-drop task")
                return False
            if not isinstance(solution["box"], list) or not all(isinstance(x, dict) for x in solution["box"]):
                logger.error("Invalid box format for drag-and-drop task")
                return False

        return True
    except Exception as e:
        logger.error(f"Error validating response format: {str(e)}")
        return False


def process_grid_classify_response(response_text: str, question: str) -> dict[str, Any]:
    """处理网格分类任务的响应"""
    try:
        # 解析原始响应
        response_data = json.loads(response_text)

        # 确保 solution 字段包含所有必需的字段
        if "solution" not in response_data:
            response_data["solution"] = {}

        solution = response_data["solution"]

        # 使用 question 作为 label
        solution["label"] = question

        # 确保其他必需字段存在
        if "objects" not in solution:
            solution["objects"] = [False] * 9
        if "top_k" not in solution:
            solution["top_k"] = []
        if "confidences" not in solution:
            solution["confidences"] = [0.0] * 9

        # 确保 objects 数组长度为 9
        if len(solution["objects"]) != 9:
            solution["objects"] = solution["objects"][:9] + [False] * (9 - len(solution["objects"]))

        # 确保 confidences 数组长度为 9
        if len(solution["confidences"]) != 9:
            solution["confidences"] = solution["confidences"][:9] + [0.0] * (9 - len(solution["confidences"]))

        return response_data
    except Exception as e:
        logger.error(f"Error processing grid classify response: {str(e)}")
        raise


def process_drag_drop_response(response_text: str) -> dict[str, Any]:
    """处理拖拽任务的响应，将坐标值除以1.5"""
    try:
        # 解析原始响应
        response_data = json.loads(response_text)

        # 确保 solution 字段存在
        if "solution" not in response_data:
            response_data["solution"] = {}

        solution = response_data["solution"]

        # # 处理 box 字段中的坐标
        # if "box" in solution and isinstance(solution["box"], list):
        #     for box_item in solution["box"]:
        #         if isinstance(box_item, dict):
        #             # 处理 start 和 end 坐标
        #             if "start" in box_item and isinstance(box_item["start"], list):
        #                 try:
        #                     box_item["start"][0] = str(int(float(box_item["start"][0]) / 1.5))
        #                     box_item["start"][1] = str(int(float(box_item["start"][1]) / 1.5))
        #                 except (ValueError, TypeError):
        #                     pass
        #             if "end" in box_item and isinstance(box_item["end"], list):
        #                 try:
        #                     box_item["end"][0] = str(int(float(box_item["end"][0]) / 1.5))
        #                     box_item["end"][1] = str(int(float(box_item["end"][1]) / 1.5))
        #                 except (ValueError, TypeError):
        #                     pass

        return response_data
    except Exception as e:
        logger.error(f"Error processing drag drop response: {str(e)}")
        raise


def get_image_grid_type(image: Image.Image) -> str:
    """根据图片尺寸自动判断ReCaptchaV2类型：1x1/3x3/4x4"""
    w, h = image.size
    if w == 100 and h == 100:
        return "1x1"
    elif w == 300 and h == 300:
        return "3x3"
    elif w == 450 and h == 450:
        return "4x4"
    else:
        return "unknown"


def process_recaptcha_classify_response(response_text: str, question: str, image_b64: str = None) -> dict[str, Any]:
    """处理ReCaptchaV2Classification任务的响应，自动适配1x1/3x3/4x4，保留AI返回的所有solution字段"""
    try:
        response_data = json.loads(response_text)
        if "solution" not in response_data:
            response_data["solution"] = {}
        solution = response_data["solution"]
        # 自动识别图片类型
        grid_type = None
        if image_b64:
            try:
                image_data = base64.b64decode(image_b64)
                image = Image.open(io.BytesIO(image_data))
                if image.width == 100 and image.height == 100:
                    grid_type = "1x1"
                elif image.width == 300 and image.height == 300:
                    grid_type = "3x3"
                elif image.width == 450 and image.height == 450:
                    grid_type = "4x4"
            except Exception as e:
                logger.error(f"Image decode/type error: {str(e)}")
                grid_type = None
        # 兼容无图片时默认3x3
        if not grid_type:
            grid_type = "3x3"
        # 组装solution
        if grid_type == "1x1":
            # 1x1输出所有标准字段
            has_object = solution.get("hasObject")
            if not isinstance(has_object, bool):
                if str(has_object).lower() == "true" or has_object == 1:
                    has_object = True
                else:
                    has_object = False
            # 补全label、confidences、size
            label = solution.get("label")
            if not label:
                label = question if question else ""
            confidences = solution.get("confidences")
            if not isinstance(confidences, list) or len(confidences) != 1:
                confidences = [1.0 if has_object else 0.0]
            response_data["solution"] = {
                "size": 100,
                "label": label,
                "confidences": confidences,
                "hasObject": has_object,
                "type": "single",
            }
        elif grid_type in ("3x3", "4x4"):
            # objects必须为索引数组，type: multi，其它字段原样保留
            objects = solution.get("objects", [])
            if not isinstance(objects, list):
                objects = []
            max_idx = 8 if grid_type == "3x3" else 15
            valid_objects = []
            for idx in objects:
                try:
                    idx_int = int(idx)
                    if 0 <= idx_int <= max_idx:
                        valid_objects.append(idx_int)
                except Exception:
                    continue
            # 保留其它字段并补全缺失字段
            new_solution = dict(solution)
            new_solution["objects"] = valid_objects
            new_solution["type"] = "multi"
            # 补全size字段
            if "size" not in new_solution:
                new_solution["size"] = 300 if grid_type == "3x3" else 450
            # 补全label字段
            if "label" not in new_solution:
                new_solution["label"] = question if question else ""
            # 补全confidences字段
            conf_len = 9 if grid_type == "3x3" else 16
            if (
                "confidences" not in new_solution
                or not isinstance(new_solution["confidences"], list)
                or len(new_solution["confidences"]) != conf_len
            ):
                new_solution["confidences"] = [0.0] * conf_len
            response_data["solution"] = new_solution
        else:
            # 非法尺寸，返回错误
            response_data = {
                "errorId": 1,
                "errorCode": "ERROR_ILLEGAL_IMAGE",
                "errorDescription": "Image size does not meet the requirements (100x100/300x300/450x450)",
                "status": "error",
                "solution": {},
            }
        return response_data
    except Exception as e:
        logger.error(f"Error processing recaptcha classify response: {str(e)}")
        raise


def analyze_with_openai(task_type, question: str, queries: list[str], anchors: list[str]) -> dict[str, Any]:
    """分析验证码任务"""
    try:
        logger.info(f"Task type determined: {task_type}")

        # 初始化CSV文件
        init_csv()

        # 针对ReCaptchaV2Classification，构造专用user_message_content
        if task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
            # 解码question
            question_text = RECAPTCHA_QUESTION_MAP.get(question, question)
            if question_text == question and question:
                logger.warning(f"Unknown ReCaptcha question code, using original: {question}")

            # 自动识别图片尺寸
            image_size_str = "unknown"
            grid_type = None
            image = None
            try:
                image_data = base64.b64decode(queries[0])
                image = Image.open(io.BytesIO(image_data))
                image_size_str = f"{image.width}x{image.height}"
                if image.width == 100 and image.height == 100:
                    grid_type = "1x1"
                elif image.width == 300 and image.height == 300:
                    grid_type = "3x3"
                elif image.width == 450 and image.height == 450:
                    grid_type = "4x4"
            except Exception:
                pass
            # 根据grid_type拼接英文模板
            if grid_type == "3x3":
                question_prompt = RECAPTCHA_3x3_QUESTION_TEMPLATE.format(question=question_text)
            elif grid_type == "4x4":
                question_prompt = RECAPTCHA_4x4_QUESTION_TEMPLATE.format(question=question_text)
            else:
                question_prompt = f"Select all images with {question_text}"
                logger.warning(f"Unknown grid_type for ReCaptcha: {grid_type}")

            # 切割小图
            small_images_b64 = []
            if image and grid_type in ("3x3", "4x4"):
                small_images_b64 = split_grid_images(image, grid_type)

            # 组装user_message_content：文本+带分隔线大图+结束文本（3x3/4x4只插一张大图）
            if grid_type in ("3x3", "4x4"):
                grid_img_b64 = draw_grid_lines(image, grid_type) if image else queries[0]
                user_message_content = [
                    {
                        "type": "text",
                        "text": f"Task type: ReCaptchaV2Classification\nQuestion: {question_prompt}\nImage size: {image_size_str}\n你将收到一张带白色分隔线的{grid_type}宫格大图（顺序编号0~{8 if grid_type == '3x3' else 15}，行优先）。请严格按照系统提示词输出objects索引数组（如[0,2,6]）。",
                    },
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{grid_img_b64}"}},
                    {
                        "type": "text",
                        "text": "\nPlease provide your analysis in the exact JSON format specified in the system prompt for the identified task type.",
                    },
                ]
            else:
                # 1x1或未知，插原图
                user_message_content = [
                    {
                        "type": "text",
                        "text": f"Task type: ReCaptchaV2Classification\nQuestion: {question_prompt}\nImage size: {image_size_str}\n你将收到原图。请严格按照系统提示词输出hasObject布尔值。",
                    },
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{queries[0]}"}},
                    {
                        "type": "text",
                        "text": "\nPlease provide your analysis in the exact JSON format specified in the system prompt for the identified task type.",
                    },
                ]
            if task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
                system_prompt = get_system_prompt(task_type, grid_type)
            else:
                system_prompt = get_system_prompt(task_type)

            if task_type == TASK_TYPES["DRAG_DROP"]:
                # 检查pro模型限额
                if not check_limits("pro"):
                    raise ValueError("Pro model daily limit exceeded")

                # 调用OpenAI API
                # model="gemini-2.5-flash-preview",
                response = client.chat.completions.create(
                    model="gemini-2.5-flash-preview-05-20",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message_content},
                    ],
                    temperature=0.6,
                    max_tokens=200000,
                )
                # 增加pro调用计数
                increment_count("pro")
            else:
                # 检查flash模型限额
                if not check_limits("flash"):
                    raise ValueError("Flash model daily limit exceeded")

                # 调用OpenAI API
                response = client.chat.completions.create(
                    model="gemini-2.5-flash-preview-05-20",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message_content},
                    ],
                    temperature=1,
                    max_tokens=200000,
                )
                # 增加flash调用计数
                increment_count("flash")

            # 处理响应
            if not hasattr(response, "choices") or not response.choices:
                raise Exception("No choices in response")

            response_content = response.choices[0].message.content
            if not response_content:
                raise Exception("Empty response content")

            # 清理响应内容
            response_content = response_content.strip()
            if response_content.startswith("```"):
                first_line = response_content.split("\n", 1)[0]
                if first_line.startswith("```json"):
                    response_content = response_content[len("```json") :]
                else:
                    response_content = response_content[len("```") :]
                response_content = response_content.strip()
            if response_content.endswith("```"):
                response_content = response_content[:-3].strip()

            # 根据任务类型处理响应
            if task_type == TASK_TYPES["GRID_CLICK"]:
                result = process_grid_click_response(response_content)
            elif task_type == TASK_TYPES["GRID_CLASSIFY"]:
                result = process_grid_classify_response(response_content, question)
            elif task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
                result = process_recaptcha_classify_response(response_content, question, queries[0])
            elif task_type == TASK_TYPES["DRAG_DROP"]:
                # pass
                result = process_drag_drop_response(response_content)
            else:
                result = json.loads(response_content)

            # 验证响应格式
            if not validate_response_format(result, task_type):
                raise ValueError("Invalid response format")
            print("****************************")
            print(result)
            return result

        else:
            # 其它类型保持原有逻辑
            user_message_content = [
                {"type": "text", "text": f"{question}"},
            ]
            # 添加锚点图片
            if anchors:
                user_message_content.append({"type": "text", "text": "Anchor images (find common theme):"})
                for i, anchor_b64 in enumerate(anchors):
                    if anchor_b64.startswith("data:"):
                        anchor_b64_unquoted = unquote(anchor_b64)
                        user_message_content.append({"type": "image_url", "image_url": {"url": anchor_b64_unquoted}})
                    else:
                        user_message_content.append(
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{anchor_b64}"}}
                        )
            # 添加查询图片
            if queries:
                if task_type == TASK_TYPES["GRID_CLASSIFY"] or task_type == TASK_TYPES["GRID_CLICK"]:
                    user_message_content.append(
                        {"type": "text", "text": "Query grid images (find matches in this 3x3 grid):"}
                    )
                elif task_type == TASK_TYPES["POINT_CLICK"] or task_type == TASK_TYPES["DRAG_DROP"]:
                    user_message_content.append(
                        {
                            "type": "text",
                            "text": """Two pictures are provided. Please analyze the problem and find the target in the first one, and then determine the exact geometric coordinates in the second one.""",
                        }
                    )
                for i, query_b64 in enumerate(queries):
                    if query_b64.startswith("data:"):
                        query_b64_unquoted = unquote(query_b64)
                        user_message_content.append({"type": "image_url", "image_url": {"url": query_b64_unquoted}})
                    else:
                        user_message_content.append(
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{query_b64}"}}
                        )
            # 在图片后面添加期望的 JSON 格式提示
            user_message_content.append(
                {
                    "type": "text",
                    "text": "\nPlease provide your analysis in the exact JSON format specified in the system prompt for the identified task type.",
                }
            )
            if task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
                system_prompt = get_system_prompt(task_type, grid_type)
            else:
                system_prompt = get_system_prompt(task_type)

            if task_type == TASK_TYPES["DRAG_DROP"]:
                # 检查pro模型限额
                if not check_limits("pro"):
                    raise ValueError("Pro model daily limit exceeded")

                # 调用OpenAI API
                # model="gemini-2.5-flash-preview",
                response = client.chat.completions.create(
                    model="gemini-2.5-flash-preview-05-20",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message_content},
                    ],
                    temperature=0.6,
                    max_tokens=200000,
                )
                # 增加pro调用计数
                increment_count("pro")
            else:
                # 检查flash模型限额
                if not check_limits("flash"):
                    raise ValueError("Flash model daily limit exceeded")

                # 调用OpenAI API
                response = client.chat.completions.create(
                    model="gemini-2.5-flash-preview-05-20",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message_content},
                    ],
                    temperature=1,
                    max_tokens=2000000,
                )
                # 增加flash调用计数
                increment_count("flash")

            # 处理响应
            if not hasattr(response, "choices") or not response.choices:
                raise Exception("No choices in response")

            response_content = response.choices[0].message.content
            if not response_content:
                raise Exception("Empty response content")

            # 清理响应内容
            response_content = response_content.strip()
            if response_content.startswith("```"):
                first_line = response_content.split("\n", 1)[0]
                if first_line.startswith("```json"):
                    response_content = response_content[len("```json") :]
                else:
                    response_content = response_content[len("```") :]
                response_content = response_content.strip()
            if response_content.endswith("```"):
                response_content = response_content[:-3].strip()

            # 根据任务类型处理响应
            if task_type == TASK_TYPES["GRID_CLICK"]:
                result = process_grid_click_response(response_content)
            elif task_type == TASK_TYPES["GRID_CLASSIFY"]:
                result = process_grid_classify_response(response_content, question)
            elif task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
                result = process_recaptcha_classify_response(response_content, question, queries[0])
            elif task_type == TASK_TYPES["DRAG_DROP"]:
                # pass
                result = process_drag_drop_response(response_content)
            else:
                result = json.loads(response_content)

            # 验证响应格式
            if not validate_response_format(result, task_type):
                raise ValueError("Invalid response format")
            print("****************************")
            print(result)
            return result

    except Exception as e:
        logger.error(f"Error in analyze_with_openai: {str(e)}")
        raise ValueError("Invalid response format")


def create_error_response(
    error_id: int, error_code: str, error_description: str, task_id: str, task_type: str = None
) -> dict[str, Any]:
    """创建统一的错误响应格式"""
    response = {
        "errorId": error_id,
        "errorCode": error_code,
        "errorDescription": error_description,
        "taskId": task_id,
        "solution": {},
    }

    # 根据任务类型设置不同的默认solution
    if task_type == TASK_TYPES["GRID_CLASSIFY"]:
        response["solution"] = {"label": "", "objects": [False] * 9, "top_k": [], "confidences": [0.0] * 9}
    elif task_type == TASK_TYPES["RECAPTCHA_CLASSIFY"]:
        response["solution"] = {"label": "", "objects": [False], "top_k": [], "confidences": [0.0]}
    elif task_type in [TASK_TYPES["GRID_CLICK"], TASK_TYPES["POINT_CLICK"]]:
        response["solution"] = {"type": "click", "question": "", "box": []}
    elif task_type == TASK_TYPES["DRAG_DROP"]:
        response["solution"] = {"type": "drag", "question": "", "box": []}
    elif task_type == TASK_TYPES["DRAG_ALL"]:
        response["solution"] = {
            "type": "drag",
            "question": "",
            "box": [],
        }  # DRAG_ALL will also use 'drag' type but expect multiple boxes

    return response


@app.route("/createTask", methods=["POST"])
def recognize_captcha():
    """处理验证码识别请求"""
    task_id = str(uuid.uuid4())
    logger.info(f"Received new task request. Task ID: {task_id}")

    try:
        # 获取并验证请求数据
        request_data = json.loads(request.get_data())

        if not request_data.get("task"):
            request_logger.error(f"Task ID: {task_id} - Invalid request format: missing task field")
            return jsonify(
                create_error_response(
                    error_id=1,
                    error_code="INVALID_REQUEST",
                    error_description="Invalid request format: missing task field",
                    task_id=task_id,
                )
            ), 400

        # 提取任务参数
        task = request_data["task"]
        question = task.get("question", "")
        queries, anchors = extract_task_images_and_anchors(task)
        save_to_local(task_id, question, queries, anchors)
        current_task_type = validate_task_type(question, queries, anchors, task.get("type"))

        # 更新任务类型统计
        if current_task_type == TASK_TYPES["DRAG_DROP"]:
            TASK_TYPE_STATS["DRAG_DROP"] += 1
        else:
            TASK_TYPE_STATS["OTHER"] += 1

        # 记录统计信息
        logger.info(
            f"Task Type Statistics - DRAG_DROP: {TASK_TYPE_STATS['DRAG_DROP']}, OTHER: {TASK_TYPE_STATS['OTHER']}"
        )

        logger.info(f"Processing task - Type: {task.get('type')}, Question: {question}")
        logger.info(f"Queries: {len(queries)}, Anchors: {len(anchors)}")

        # 分析任务
        try:
            if current_task_type == TASK_TYPES["DRAG_DROP"]:
                return proxy(task_id)
            analysis_result = analyze_with_openai(current_task_type, question, queries, anchors)
            if not analysis_result:
                response_logger.error(f"Task ID: {task_id} - No target detected in analysis result.")
                return jsonify(
                    create_error_response(
                        error_id=1,
                        error_code="ERROR_NO_TARGET_DETECTED",
                        error_description="No target detected, please check your request or report to us.",
                        task_id=task_id,
                        task_type=validate_task_type(question, queries, anchors, task.get("type")),
                    )
                ), 200

            # 提取并返回结果
            solution = analysis_result.get("solution", {})
            response_logger.info(f"Task ID: {task_id}, Response: {json.dumps(solution, ensure_ascii=False)}")
            return jsonify(
                {
                    "errorId": 0,
                    "errorCode": "",
                    "errorDescription": "",
                    "status": "ready",
                    "solution": solution,
                    "taskId": task_id,
                }
            ), 200

        except ValueError as ve:
            logger.error(f"Analysis error: {str(ve)}")
            request_logger.error(f"Task ID: {task_id} - Analysis ValueError: {str(ve)}")
            return jsonify(
                create_error_response(
                    error_id=1,
                    error_code="ERROR_NO_TARGET_DETECTED",
                    error_description="No target detected, please check your request or report to us.",
                    task_id=task_id,
                    task_type=validate_task_type(question, queries, anchors, task.get("type")),
                )
            ), 200

    except json.JSONDecodeError:
        logger.error("Invalid JSON in request")
        request_logger.error(f"Task ID: {task_id} - Invalid JSON format in request.")
        return jsonify(
            create_error_response(
                error_id=1,
                error_code="INVALID_JSON",
                error_description="Invalid JSON format in request",
                task_id=task_id,
            )
        ), 400
    except Exception as e:
        logger.error(f"Error processing task {task_id}: {str(e)}")
        request_logger.error(f"Task ID: {task_id} - Unexpected error: {str(e)}")
        return jsonify(
            create_error_response(
                error_id=1,
                error_code="INTERNAL_ERROR",
                error_description=f"Internal server error: {str(e)}",
                task_id=task_id,
            )
        ), 500


@app.route("/getBalance", methods=["GET", "POST"])
def get_balance():
    """获取账户余额信息"""
    return jsonify({"errorId": 0, "balance": 999999, "softBalance": 0, "inviteBalance": 0, "inviteBy": "0"}), 200


def save_to_local(task_id: str, question: str, queries: list, anchors: list):
    """Save images from request to local storage"""
    try:
        # Create base directory path
        base_dir = Path("examples/cap/image")
        # Create question directory (sanitize question for filesystem)
        question_dir = base_dir / "".join(c for c in question if c.isalnum() or c in (" ", "-", "_")).strip()
        question_dir.mkdir(parents=True, exist_ok=True)

        # Save anchors
        for i, anchor in enumerate(anchors, 1):
            try:
                if anchor.startswith("data:"):
                    anchor = unquote(anchor)
                    image_data = base64.b64decode(anchor.split(",")[1])
                else:
                    image_data = base64.b64decode(anchor)

                # Save as jpg
                image_path = question_dir / f"anchor_{i}_{task_id}.jpg"
                with open(image_path, "wb") as f:
                    f.write(image_data)
            except Exception as e:
                logger.error(f"Error saving anchor image {i}: {str(e)}")
        # Save anchors
        for i, queries in enumerate(queries, 1):
            try:
                if queries.startswith("data:"):
                    queries = unquote(queries)
                    image_data = base64.b64decode(queries.split(",")[1])
                else:
                    image_data = base64.b64decode(queries)

                # Save as jpg
                image_path = question_dir / f"queries_{i}_{task_id}.jpg"
                with open(image_path, "wb") as f:
                    f.write(image_data)
            except Exception as e:
                logger.error(f"Error saving anchor image {i}: {str(e)}")
    except Exception as e:
        logger.error(f"Error in save_to_local: {str(e)}")


@app.route("/report", methods=["GET", "POST"])
def record_report():
    try:
        base_dir = Path("examples/cap/image")
        # 确保目录存在
        base_dir.mkdir(parents=True, exist_ok=True)
        # 获取请求数据
        request_data = json.loads(request.get_data())
        # 直接写入原始JSON数据
        report_file = base_dir / "report.txt"
        with open(report_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(request_data, ensure_ascii=False) + "\n")
        return jsonify({"errorId": 0, "message": "Report recorded successfully"}), 200
    except Exception as e:
        logger.error(f"Error in record_result: {str(e)}")
        return jsonify({"errorId": 1, "message": f"Error recording report: {str(e)}"}), 500


def record_result(task_id, result):
    try:
        base_dir = Path("examples/cap/image")
        # 确保目录存在
        base_dir.mkdir(parents=True, exist_ok=True)
        # 获取请求数据
        request_data = json.loads(result)
        # 直接写入原始JSON数据
        report_file = base_dir / "result.txt"
        with open(report_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(request_data, ensure_ascii=False) + "\n")
        return jsonify({"errorId": 0, "message": "Report recorded successfully"}), 200
    except Exception as e:
        logger.error(f"Error in record_result: {str(e)}")
        return jsonify({"errorId": 1, "message": f"Error recording report: {str(e)}"}), 500


def proxy(task_id: str = None):
    """Forward request to YesCaptcha API"""
    try:
        # 直接使用 Flask 的 request 对象
        method = request.method
        headers = dict(request.headers)
        # 修改 host 头
        headers["Host"] = "api.yescaptcha.com"
        data = request.get_data()

        # 记录请求数据
        try:
            request_data = json.loads(data)

        except json.JSONDecodeError:
            request_logger.info(f"Task ID: {task_id}, Request Data: {data}")

        # 构建目标URL
        target_url = "https://api.yescaptcha.com" + request.path

        # 发送请求到YesCaptcha API
        response = requests.request(method=method, url=target_url, headers=headers, data=data)

        # 记录响应数据
        try:
            response_data = json.loads(response.content)
            # 修改taskId
            if task_id and isinstance(response_data, dict):
                response_data["taskId"] = task_id
            response_logger.info(f"Task ID: {task_id}, Response: {json.dumps(response_data, ensure_ascii=False)}")
            # 更新response content
            response_content = json.dumps(response_data, ensure_ascii=False).encode("utf-8")
        except json.JSONDecodeError:
            response_logger.info(f"Task ID: {task_id}, Response: {response.content}")
            response_content = response.content

        record_result(task_id, response_content)
        return response_content

    except Exception as e:
        logger.error(f"Error in proxy: {str(e)}")
        return jsonify({"errorId": 1, "message": f"Proxy error: {str(e)}"}), 500


@app.route("/", defaults={"path": ""}, methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])
@app.route("/<path:path>", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])
def catch_all(path):
    """Catch all routes and proxy to YesCaptcha API"""
    return proxy()


def extract_task_images_and_anchors(task: dict) -> tuple[list, list]:
    """统一提取queries和anchors，兼容ReCaptchaV2Classification和其它类型。"""
    # 兼容ReCaptchaV2Classification类型
    if task.get("type") == "ReCaptchaV2Classification" and task.get("image"):
        queries = [task.get("image")]
    else:
        queries_raw = task.get("queries")
        if isinstance(queries_raw, str):
            queries = [queries_raw]
        elif isinstance(queries_raw, list):
            queries = queries_raw
        else:
            queries = []
    anchors_raw = task.get("anchors")
    if isinstance(anchors_raw, str):
        anchors = [anchors_raw]
    elif isinstance(anchors_raw, list):
        anchors = anchors_raw
    else:
        anchors = []
    return queries, anchors


def split_grid_images(image: Image.Image, grid_type: str) -> list[str]:
    """
    将原图切割为9(3x3)或16(4x4)张小图，顺序严格按行优先编号，返回base64数组。
    支持100x100(1x1)、300x300(3x3)、450x450(4x4)。
    """
    small_images_b64 = []
    if grid_type == "3x3":
        grid_size = 3
        cell_w = image.width // 3
        cell_h = image.height // 3
    elif grid_type == "4x4":
        grid_size = 4
        cell_w = image.width // 4
        cell_h = image.height // 4
    else:
        # 1x1或未知直接返回原图
        buf = io.BytesIO()
        image.save(buf, format="JPEG")
        small_images_b64.append(base64.b64encode(buf.getvalue()).decode())
        return small_images_b64
    for row in range(grid_size):
        for col in range(grid_size):
            left = col * cell_w
            upper = row * cell_h
            right = left + cell_w
            lower = upper + cell_h
            # 边界修正，最后一行/列取到边界
            if col == grid_size - 1:
                right = image.width
            if row == grid_size - 1:
                lower = image.height
            crop = image.crop((left, upper, right, lower))
            buf = io.BytesIO()
            crop.save(buf, format="JPEG")
            small_images_b64.append(base64.b64encode(buf.getvalue()).decode())
    return small_images_b64


def draw_grid_lines(image: Image.Image, grid_type: str) -> str:
    """
    在原图上绘制白色分隔线（3x3/4x4），返回带线大图base64。
    """
    img = image.copy().convert("RGB")
    draw = ImageDraw.Draw(img)
    w, h = img.size
    line_width = max(2, w // 150)  # 适配不同分辨率
    if grid_type == "3x3":
        # 画2条横线、2条竖线
        for i in range(1, 3):
            # 横线
            y = h * i // 3
            draw.line([(0, y), (w, y)], fill=(255, 255, 255), width=line_width)
            # 竖线
            x = w * i // 3
            draw.line([(x, 0), (x, h)], fill=(255, 255, 255), width=line_width)
    elif grid_type == "4x4":
        for i in range(1, 4):
            # 横线
            y = h * i // 4
            draw.line([(0, y), (w, y)], fill=(255, 255, 255), width=line_width)
            # 竖线
            x = w * i // 4
            draw.line([(x, 0), (x, h)], fill=(255, 255, 255), width=line_width)
    # 输出base64
    buf = io.BytesIO()
    img.save(buf, format="JPEG")
    return base64.b64encode(buf.getvalue()).decode()


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000, debug=False)
