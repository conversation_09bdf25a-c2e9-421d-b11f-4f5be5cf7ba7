import os
import random
from time import sleep

import click
from DrissionPage import Chromium
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.operations import try_click
from src.controllers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.socials.discord import Discord
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.element_util import get_element, get_elements
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
from src.utils.common import parse_indices

logger.add("logs/optimum_discord.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")


class DCChatBot(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)

    def send_message(self, channel_id: str, message: str) -> bool:
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(channel_id, message):
            raise Exception("发送消息失败")
        return True


class OptimumDiscord(Discord):
    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "getoptimum"
        super().__init__(id, page, browser_type, invite_code, "optimum")

    def _handle_option_selection(self, tab, select_count=0):
        """处理选项选择

        Args:
            tab: 浏览器标签页
            select_count: 需要选择的选项数量，0表示全选
        """
        try:
            eles = get_elements(tab, "x://div[contains(@class, 'optionButtonWrapper')]", 5)
            if not eles:
                return

            if select_count == 0:
                # 全选
                for ele in eles:
                    ele.click()
                    sleep(0.5)
            else:
                # 随机选择指定数量
                selected = random.sample(eles, min(select_count, len(eles)))
                for ele in selected:
                    ele.click()

            try_click(tab, "x://button[contains(.,'Finish') or contains(.,'Next')]")
            sleep(2)
        except Exception as e:
            logger.error(f"{self.id} 处理选项选择时发生错误: {str(e)}")

    def _verify(self) -> bool:
        tab = self._page.latest_tab
        # 点击验证按钮
        try_click(tab, "x://button[.='Verify']")
        sleep(3)

        try_click(tab, "x://button[.='Solve']")

        # 等待验证完成
        ele = get_element(tab, "x://span[.='@verified']", 600)
        if ele:
            logger.success(f"{self.id} {self.dc_name} 验证成功")
            return True

        logger.error(f"{self.id} {self.dc_name} 验证失败")
        return False

    def _pre_verify(self) -> bool:
        tab = self._page.latest_tab

        # 检测是否有弹窗
        ele = get_element(tab, "x://div[contains(@class, 'focusLock__')]", 5)
        if ele:
            # 点击弹窗
            try_click(tab, "x://button[@type='button' and .='Apply']")
            sleep(2)

        return True

    def _gm(self) -> bool:
        channel_id = "1356245748056064111"
        dc_chat_bot = DCChatBot(self.browser_type, self.id)
        dc_chat_bot.send_message(channel_id, "gmum")
        return True


def _join_optimum(type, index):
    controller = None
    try:
        controller = BrowserController(type, index)
        discord = OptimumDiscord(controller.id, controller.page, controller.browser_type)
        discord.join_server()
    except Exception as e:
        logger.error(f"{controller.id} {controller.browser_type} 加入Mullet Cop失败: {e}")
    finally:
        if controller:
            controller.close_page()


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def join_optimum(index, type, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            _join_optimum(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=1200,  # 20分钟超时
            retries=3,
            interval=10,
            task_name=f"optimum-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# python3 examples/optimum/discord.py run -i 1 -t chrome

if __name__ == "__main__":
    cli()

    # _join_optimum(BrowserType.ADS, '2')

    # _join_kraft_lab(BrowserType.CHROME, "4")
    # _join_mullet_cop(BrowserType.CHROME, "3")
