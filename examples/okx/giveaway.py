import json
import os
import shutil
from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from os import environ
from pathlib import Path
from time import sleep
from urllib.parse import urlparse

import click
from dotenv import load_dotenv
from loguru import logger

from config import DEFAULT_BROWSER_TYPE, USER_DATA_PATH
from src.browsers import BROWSER_TYPES, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.thread_executor import ThreadExecutor

load_dotenv()

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/okx_giveaway.log", rotation="10MB", level="SUCCESS")


class GiveawayConfig(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)

    def get_proxy(self):
        return self.browser_config.proxy

    def set_proxy(self, proxy):
        self.browser_config.proxy = proxy
        self._repository.update(self.browser_config)


class OKXGiveaway:
    def __init__(self, browser_type: BrowserType, id: str, project_name: str, data_util: DataUtil):
        self.id = id
        self.browser_type = browser_type
        self.project_name = project_name
        self.task_url = f"https://web3.okx.com/zh-hans/giveaway/{project_name}"
        self.browser_controller = BrowserController(browser_type, id)
        self.address = self.browser_controller.browser_config.evm_address
        self.page = self.browser_controller.page
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        """初始化数据"""
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
                "status": "0",
                "message": "初始化",
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】初始化数据失败: {e}")

    @staticmethod
    def _is_login(tab):
        """检查是否已登录"""
        return get_element(tab, "x://div[@class='wallet-address-container']", 5)

    def _connect_wallet(self, tab):
        """连接钱包"""
        try:
            logger.info(f"【{self.id}】开始连接钱包")

            connect_wallet_btn = get_element(tab, "x://div[contains(@class,'wallet-pc-connect-button')]", 5)
            if not connect_wallet_btn:
                logger.error(f"【{self.id}】 查找钱包按钮超时")
                return False

            connect_wallet_btn.click()

            connect_okx_btn = get_element(tab, "x://button[@class='wallet wallet-btn btn-md btn-fill-highlight']", 5)
            if not connect_okx_btn:
                logger.error(f"【{self.id}】多次点击链接钱包无反应")
                return False

            connect_okx_btn.click()

            if not self.browser_controller.okx_wallet_connect():
                logger.error(f"【{self.id}】钱包链接失败")
                return False

            sleep(10)
            if self._is_login(tab):
                logger.success(f"【{self.id}】登录成功")
                return True

            logger.error(f"【{self.id}】登录失败")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】连接钱包异常: {e}")
            return False

    def _execute_twitter_task(self, task_index: int = 1) -> bool:
        """执行Twitter任务，包含重试逻辑"""
        for attempt in range(3):
            logger.info(f"【{self.id}】第{attempt + 1}次尝试执行Twitter任务")

            try:
                tab = self.browser_controller.page.latest_tab
                task_div_xpath = f"x://div[contains(@class, 'index_giveaway-task')][{task_index}]"
                task_div = get_element(tab, task_div_xpath, 5)

                if not task_div:
                    logger.error(f"【{self.id}】未找到第{task_index}个任务")
                    continue

                # 检查任务是否已完成
                if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 2):
                    logger.success(f"【{self.id}】Twitter任务已完成")
                    return True

                # 移动鼠标到任务行以显示按钮
                tab.actions.move_to(task_div)
                sleep(1)

                # 查找连接Twitter按钮
                connect_btn_selectors = [
                    "x://button[contains(@class, 'index_twitter-connect')]",
                    "x://button[contains(text(), '连接账号')]",
                    "x://button[contains(text(), 'Connect Account')]",
                ]

                connect_btn = None
                for selector in connect_btn_selectors:
                    connect_btn = get_element(task_div, selector, 2)
                    if connect_btn:
                        logger.info(f"【{self.id}】找到连接Twitter按钮: {selector}")
                        break

                if connect_btn:
                    logger.info(f"【{self.id}】点击连接Twitter按钮")
                    connect_btn.click()

                    # 等待Twitter授权页面
                    if self.browser_controller.page.wait.new_tab(timeout=20):
                        auth_tab = self.browser_controller.page.latest_tab

                        # 检查是否是Twitter授权页面
                        if "x.com" in auth_tab.url or "twitter.com" in auth_tab.url:
                            logger.info(f"【{self.id}】检测到Twitter授权页面")

                            # 查找授权按钮
                            auth_btn = auth_tab.ele("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
                            if auth_btn:
                                auth_btn.click()
                                logger.info(f"【{self.id}】点击Twitter授权按钮")
                                sleep(5)
                            else:
                                logger.warning(f"【{self.id}】未找到Twitter授权按钮")

                        # 关闭授权页面
                        self.browser_controller.page.close_tabs([auth_tab])
                        sleep(3)

                # 检查连接是否成功 - 查找开始按钮
                start_btn_selectors = [
                    "x://button[contains(@class, 'index_start__')]",
                    "x://button[contains(@class, 'dc-btn')]",
                    "x://button[contains(text(), '现在开始')]",
                ]

                start_btn = None
                for selector in start_btn_selectors:
                    start_btn = get_element(task_div, selector, 3)
                    if start_btn:
                        logger.info(f"【{self.id}】Twitter连接成功，找到开始按钮")
                        break

                if start_btn:
                    # 点击开始按钮
                    start_btn.click()

                    # 等待新标签页打开并关闭
                    if self.browser_controller.page.wait.new_tab(timeout=10):
                        sleep(3)
                        self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])
                        sleep(2)

                    # 检查任务是否完成
                    if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                        logger.success(f"【{self.id}】Twitter任务完成")
                        return True
                else:
                    logger.warning(f"【{self.id}】Twitter连接失败，未找到开始按钮")
                    # 刷新页面重试
                    tab.refresh()
                    sleep(5)
                    continue

            except Exception as e:
                logger.error(f"【{self.id}】执行Twitter任务异常: {e}")

        logger.error(f"【{self.id}】Twitter任务重试3次后仍然失败")
        return False

    def _execute_task_by_index(self, task_index: int) -> bool:
        """根据任务索引执行任务"""
        # 如果是第1个任务（通常是Twitter任务），使用专门的Twitter处理逻辑
        if task_index == 1:
            return self._execute_twitter_task(task_index)

        # 其他任务的处理逻辑
        try:
            tab = self.browser_controller.page.latest_tab
            task_div_xpath = f"x://div[contains(@class, 'index_giveaway-task')][{task_index}]"
            task_div = get_element(tab, task_div_xpath, 5)

            if not task_div:
                logger.error(f"【{self.id}】未找到第{task_index}个任务")
                return False

            # 检查任务是否已完成
            if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 2):
                logger.success(f"【{self.id}】第{task_index}个任务已完成")
                return True

            # 移动鼠标到任务行以显示按钮
            tab.actions.move_to(task_div)
            sleep(1)

            # 查找开始按钮
            start_btn_selectors = [
                "x://button[contains(@class, 'index_start__')]",
                "x://div[contains(@class, 'index_common-task')]/button",
                "x://button[contains(@class, 'dc-btn')]",
                "x://button[contains(text(), '现在开始')]",
                "x://button[contains(text(), '开始')]",
            ]

            start_btn = None
            for selector in start_btn_selectors:
                start_btn = get_element(task_div, selector, 2)
                if start_btn:
                    logger.info(f"【{self.id}】找到开始按钮: {selector}")
                    break

            if start_btn:
                logger.info(f"【{self.id}】点击第{task_index}个任务的开始按钮")
                start_btn.click()

                # 等待新标签页打开
                if self.browser_controller.page.wait.new_tab(timeout=10):
                    sleep(3)  # 等待页面加载
                    # 关闭新打开的标签页
                    self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])
                    sleep(2)

                # 检查任务是否完成
                if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 3):
                    logger.success(f"【{self.id}】第{task_index}个任务完成")
                    return True

            return False

        except Exception as e:
            logger.error(f"【{self.id}】执行第{task_index}个任务失败: {e}")
            return False

    def _verify_all_tasks(self) -> bool:
        """验证所有任务完成"""
        try:
            logger.info(f"【{self.id}】开始验证所有任务")
            tab = self.browser_controller.page.latest_tab

            # 查找最终验证按钮
            verify_btn = get_element(
                tab, "x://button[contains(@class,'dc dc-btn btn-xl btn-fill-primary index_bottom-btn')]", 10
            )

            if not verify_btn:
                # 检查是否已经完成
                completed_btn = get_element(
                    tab,
                    "x://button[contains(@class, 'dc dc-btn btn-xl btn-outline-primary btn-disabled"
                    " index_bottom-btn')]",
                    10,
                )
                if completed_btn:
                    logger.success(f"【{self.id}】所有任务已完成")
                    return True

                logger.error(f"【{self.id}】未找到验证按钮")
                return False

            # 监听最终验证API
            tab.listen.start("https://web3.okx.com/priapi/v1/dapp/giveaway/claimFinished")
            verify_btn.click()
            res = tab.listen.wait(timeout=15)

            if not res or not res.response.body:
                logger.error(f"【{self.id}】任务验证失败,未收到网络响应")
                return False

            data = res.response.body.get("data", {}).get("verifySucceed", False)
            if data:
                logger.success(f"【{self.id}】所有任务验证成功")
                return True
            else:
                logger.warning(f"【{self.id}】任务验证失败")
                return False

        except Exception as e:
            logger.error(f"【{self.id}】验证任务异常: {e}")
            return False

    def login(self, tab):
        """登录"""

        for i in range(3):
            logger.info(f"【{self.id}】第 {i + 1} 次登录")
            try:
                tab.refresh()
                if self._is_login(tab):
                    logger.success(f"【{self.id}】已登录")
                    return True

                result = self._connect_wallet(tab)
                if result:
                    return True

            except Exception as e:
                logger.error(f"【{self.id}】登录异常: {e}")

        logger.warning(f"【{self.id}】登录重试三次仍未成功")
        return False

    def _execute_balance_task(self, task: dict, task_index: int) -> bool:
        """执行余额验证任务"""
        try:
            logger.info(f"【{self.id}】执行余额验证任务: {task.get('name')}")

            tab = self.browser_controller.page.latest_tab
            task_div_xpath = f"x://div[contains(@class, 'index_giveaway-task')][{task_index}]"
            task_div = get_element(tab, task_div_xpath, 5)

            if not task_div:
                logger.error(f"【{self.id}】未找到第{task_index}个任务")
                return False

            # 检查任务是否已完成
            if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 2):
                logger.success(f"【{self.id}】余额验证任务已完成")
                return True

            # 移动鼠标到任务行
            tab.actions.move_to(task_div)
            sleep(1)

            # 查找验证按钮
            verify_btn_selectors = [
                "x://i[contains(@class, 'okx-defi-nft-filter-refresh dc-a11y-button')]",
                "x://button[contains(@class, 'refresh')]",
                "x://button[contains(@class, 'verify')]",
                "x://*[contains(@class, 'refresh')]",
                "x://*[contains(text(), '刷新')]",
                "x://*[contains(text(), '验证')]",
            ]

            verify_btn = None
            for selector in verify_btn_selectors:
                verify_btn = get_element(task_div, selector, 2)
                if verify_btn:
                    logger.info(f"【{self.id}】找到验证按钮: {selector}")
                    break

            if verify_btn:
                logger.info(f"【{self.id}】点击余额验证按钮")
                # 监听验证API
                tab.listen.start("https://web3.okx.com/priapi/v1/dapp/giveaway/task/check")
                verify_btn.click()
                res = tab.listen.wait(timeout=15)

                if res and res.response.body:
                    code = res.response.body.get("code", 0)
                    if code == 1:
                        logger.success(f"【{self.id}】余额验证任务完成")
                        return True
                    else:
                        logger.warning(f"【{self.id}】余额验证失败，code: {code}")
                else:
                    logger.error(f"【{self.id}】余额验证无响应")
            else:
                logger.warning(f"【{self.id}】未找到余额验证按钮")

            return False

        except Exception as e:
            logger.error(f"【{self.id}】执行余额验证任务失败: {e}")
            return False

    def _execute_social_task(self, task: dict, task_index: int) -> bool:
        """执行社交任务（Discord等）"""
        try:
            task_name = task.get("name", f"任务{task_index}")
            logger.info(f"【{self.id}】执行社交任务: {task_name}")

            tab = self.browser_controller.page.latest_tab
            task_div_xpath = f"x://div[contains(@class, 'index_giveaway-task')][{task_index}]"
            task_div = get_element(tab, task_div_xpath, 5)

            if not task_div:
                logger.error(f"【{self.id}】未找到第{task_index}个任务")
                return False

            # 检查任务是否已完成
            if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 2):
                logger.success(f"【{self.id}】{task_name} 已完成")
                return True

            # 移动鼠标到任务行
            tab.actions.move_to(task_div)
            sleep(1)

            # 查找开始按钮
            start_btn_selectors = [
                "x://button[contains(@class, 'index_start__')]",
                "x://div[contains(@class, 'index_common-task')]/button",
                "x://button[contains(@class, 'dc-btn')]",
                "x://button[contains(text(), '现在开始')]",
                "x://button[contains(text(), '开始')]",
            ]

            start_btn = None
            for selector in start_btn_selectors:
                start_btn = get_element(task_div, selector, 2)
                if start_btn:
                    logger.info(f"【{self.id}】找到开始按钮: {selector}")
                    break

            if start_btn:
                logger.info(f"【{self.id}】点击开始按钮")
                start_btn.click()

                # 等待新标签页打开并关闭
                if self.browser_controller.page.wait.new_tab(timeout=10):
                    sleep(3)
                    self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])
                    sleep(2)

                # 检查任务是否完成
                if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                    logger.success(f"【{self.id}】{task_name} 完成")
                    return True
            else:
                logger.warning(f"【{self.id}】未找到开始按钮")

            return False

        except Exception as e:
            logger.error(f"【{self.id}】执行社交任务失败: {e}")
            return False

    def execute(self) -> bool:
        """执行所有任务"""
        try:
            if self.data_util.get(self.address).get("status", "0") == "1":
                logger.success(f"【{self.id}】任务已全部完成")
                return True

            self.browser_controller.window_max()
            self.browser_controller.okx_wallet_login()

            tab = self.browser_controller.page.new_tab()
            tab.listen.start("giveaway/getDetailV2")
            tab.get(self.task_url)

            if not self.login(tab):
                logger.error(f"【{self.id}】登录失败")
                self.data_util.update(self.address, {"message": "login_error"})
                return False
            else:
                self.data_util.update(self.address, {"message": "", "fail_count": 0})

            logger.info(f"【{self.id}】开始执行任务")

            res = tab.listen.wait(timeout=15)

            # 解析API返回的任务列表
            if not res or not res.response.body:
                logger.error(f"【{self.id}】获取任务列表失败，未收到API响应")
                self.data_util.update(self.address, {"status": "0", "message": "获取任务列表失败"})
                return False

            data = res.response.body.get("data", {})
            task_list = data.get("taskList", [])

            if not task_list:
                logger.warning(f"【{self.id}】任务列表为空")
                self.data_util.update(self.address, {"status": "0", "message": "任务列表为空"})
                return False

            logger.info(f"【{self.id}】获取到 {len(task_list)} 个任务")

            # 动态执行任务
            task_results = {}
            twitter_completed = False

            for i, task in enumerate(task_list, 1):
                task_id = task.get("id")
                task_name = task.get("name", f"任务{i}")
                task_type = task.get("taskType", 0)
                bind_status = task.get("bindStatus", False)

                logger.info(
                    f"【{self.id}】任务{i}: {task_name} (类型:{task_type}, 状态:{'已完成' if bind_status else '未完成'})"
                )

                # 如果任务已完成，跳过
                if bind_status:
                    logger.success(f"【{self.id}】{task_name} 已完成，跳过")
                    task_results[f"task_{task_id}"] = "1"
                    if task_type == 1:
                        twitter_completed = True
                    continue

                # 执行任务
                logger.info(f"【{self.id}】开始执行: {task_name}")

                if task_type == 1:
                    # Twitter 任务
                    result = self._execute_twitter_task(i)
                    if result:
                        twitter_completed = True
                        logger.success(f"【{self.id}】Twitter任务完成")
                    else:
                        logger.error(f"【{self.id}】Twitter任务失败，直接退出")
                        task_results[f"task_{task_id}"] = "0"
                        task_results["status"] = "0"
                        task_results["message"] = "Twitter任务失败"
                        self.data_util.update(self.address, task_results)
                        return False

                elif task_type == 2:
                    # 余额验证任务
                    result = self._execute_balance_task(task, i)

                elif task_type == 4:
                    # Discord/其他社交任务
                    result = self._execute_social_task(task, i)

                else:
                    logger.warning(f"【{self.id}】未知任务类型 {task_type}，跳过")
                    result = False

                # 记录任务结果
                task_results[f"task_{task_id}"] = "1" if result else "0"

                if result:
                    logger.success(f"【{self.id}】{task_name} 完成")
                else:
                    logger.warning(f"【{self.id}】{task_name} 未完成")

                sleep(2)  # 任务间隔

            # 更新最终状态
            completed_count = sum(1 for v in task_results.values() if v == "1" and "task_" in str(v))
            total_count = len(task_list)

            if completed_count == total_count:
                task_results["status"] = "1"
                task_results["message"] = "所有任务完成"
                logger.success(f"【{self.id}】所有任务执行成功")
            else:
                task_results["status"] = "0"
                task_results["message"] = f"完成{completed_count}/{total_count}个任务"
                logger.warning(f"【{self.id}】只完成了{completed_count}/{total_count}个任务")

            self.data_util.update(self.address, task_results)

            logger.success(f"【{self.id}】任务执行完成")
            return True

        except Exception as e:
            logger.error(f"【{self.id}】执行任务过程出错: {e}")
            self.data_util.update(self.address, {"status": "0", "message": f"执行出错: {str(e)}"})
            return False


def _execute_task(browser_type: BrowserType, index: str, data_util: DataUtil, project_name: str) -> bool:
    """执行单个任务"""
    try:
        giveaway = OKXGiveaway(browser_type, index, project_name, data_util)
        return giveaway.execute()
    except Exception as e:
        logger.error(f"【{index}】执行任务失败: {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-p", "--project", type=str, prompt="请输入项目名", help="项目名称（如：sharex, manta, chainbase）")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-c", "--count", type=int, default=3, help="重试次数")
def run(type, index, project, workers, count):
    try:
        indices = parse_indices(index)

        project_name = project.lower()
        data_dir = os.path.join(get_project_root_path(), "examples", "okx")
        csv_path = os.path.join(data_dir, f"giveaway_{project_name}_{type.lower()}.csv")

        # 定义CSV头部
        headers = [
            "index",
            "type",
            "address",
            "project_name",
            "task_1",
            "task_2",
            "task_3",
            "task_4",
            "status",
            "message",
        ]
        data_util = DataUtil(csv_path, headers)

        successful_indices = []
        failed_indices = []

        for i in range(count):
            separator = "=" * 20
            logger.info(f"{separator} 开始第 {i+1} 次执行任务 {separator}")

            # 已经执行成功的任务
            executed_indices = [int(data["index"]) for data in data_util.list() if data["status"] == "1"]

            # 从全部任务中去除已经执行成功的任务
            execute_indices = [x for x in indices if x not in executed_indices]

            if len(execute_indices) == 0:
                logger.success("所有任务全部执行完成")
                return True

            def process_task(index):
                result = _execute_task(type, str(index), data_util, project_name)
                if result:
                    successful_indices.append(index)
                else:
                    failed_indices.append(index)
                return result

            # 创建线程执行器
            executor = ThreadExecutor(
                workers=workers,
                timeout=6 * 3600,  # 6小时超时
                retries=3,
                interval=10,
                task_name=f"giveaway_{project_name}_{type}",
                raise_exception=False,
            )

            # 批量执行任务
            executor.run_batch(process_task, execute_indices)

            # 计算失败列表
            failed_indices = list(set(execute_indices) - set(successful_indices))
            failed_indices = [str(index) for index in failed_indices]

            logger.success(
                f"本次共执行 {len(execute_indices)} 个账号，成功 {len(successful_indices)} 个，失败"
                f" {len(failed_indices)} 个"
            )
            if len(failed_indices) > 0:
                logger.success(f"失败的账号列表: {','.join(failed_indices)}")

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 使用示例:
# python3 examples/okx/giveaway_new.py run -t chrome -i 290 -p chainbase
# python3 examples/okx/giveaway_new.py run -t ads -i 1-100 -p sharex
if __name__ == "__main__":
    # cli()

    type = BrowserType.CHROME
    index = "290"

    project_name = "jaspervault"
    data_dir = os.path.join(get_project_root_path(), "examples", "okx")
    csv_path = os.path.join(data_dir, f"giveaway_{project_name}_{type.lower()}.csv")

    # 定义CSV头部
    headers = ["index", "type", "address", "status", "message"]
    data_util = DataUtil(csv_path, headers)

    _execute_task(type, str(index), data_util, project_name)
