import os
import random
from datetime import datetime
from threading import Lock
from time import sleep

from faker import Faker
from loguru import logger
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BrowserType
from src.controllers import BrowserController
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/somnia.log", rotation="10MB", level="SUCCESS")

# INVITE_CODES = ["110FB896", "E5A5E2C4"]


INVITE_CODES = os.getenv("SOMNIC_INVITE_CODE", "").split(",") if os.getenv("SOMNIC_INVITE_CODE") else []


def safe_int(value, default=0):
    """安全地将值转换为整数，处理空字符串和其他异常情况."""
    if value == "":
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


class SomniaAccount:
    _csv_lock = Lock()

    def __init__(self, browser_controller: BrowserController, data: DataUtil):
        self.id = browser_controller.id
        self.browser_controller = browser_controller
        self.page = self.browser_controller.page
        self.browser_type = self.browser_controller.browser_type
        self.address = self.browser_controller.browser_config.evm_address
        self.data = data
        self._init_data()

    def _init_data(self):
        try:
            data = self.data.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _is_login(self, address):
        try:
            tab = self.page.latest_tab
            short_address = address[:4] + "..." + address[-4:]
            short_address = short_address.lower()
            if get_element(
                tab,
                f"x://div[@class='menu-actions']//button[4]//span[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{short_address}')]",
                10,
            ):
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} _is_login error: {str(e)}")
            return False

    def _connect_wallet(self, tab):
        try_count = 3
        for i in range(try_count):
            logger.info(f"【{self.id}】钱包进行第 {i + 1}/{try_count}次连接")

            wallet_element = get_element(tab, "x://button[contains(.,'0x')]", 5)
            if wallet_element:
                logger.info(f"【{self.id}】 钱包完成连接")
                return True

            # connect_btn = get_element(tab, "x://div[@class='root-content']//button", 3)
            connect_btn = get_element(tab, "x://span[@role='img' and @aria-label='poweroff' ]/parent::button", 3)
            if connect_btn:
                connect_btn.click()
                sleep(2)

            okx_wallet = get_element(tab, "x://button[.//span[text()='OKX Wallet']]", 3)
            if okx_wallet:
                okx_wallet.click()
                sleep(2)

            try:
                self.browser_controller.okx_wallet_connect()
            except Exception:
                pass

            sleep(2)
            latest_tab = self.page.latest_tab
            wallet_element = get_element(latest_tab, "x://button[contains(.,'0x')]", 5)
            if wallet_element:
                logger.info(f"【{self.id}】 钱包完成连接")
                return True

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _login(self):
        """
        登录Somnia并处理邀请码绑定.

        Returns
        -------
            Tab | None: 成功返回当前标签页，失败返回None
        """
        try:
            # 确定目标URL和邀请码
            url, invite_code = self._get_login_url()

            # 打开新标签页
            logger.info(f"【{self.id}】{self.address} 正在打开登录页面: {url}")
            current_tab = self.page.new_tab(url)

            # if not self._is_login(self.address):

            if not self._connect_wallet(current_tab):
                logger.error(f"【{self.id}】{self.address} 钱包连接失败")
                return None

            # 处理邀请码绑定
            if invite_code:
                self._link_invite_code(current_tab, invite_code)
            current_tab.get("https://quest.somnia.network/account")
            return current_tab

        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 登录失败: {str(e)}")
            return None

    def _get_login_url(self):
        """
        获取登录URL和邀请码.

        Returns
        -------
            tuple: (url, invite_code)
        """
        base_url = "https://quest.somnia.network/account"
        record = self.data.get(self.address)

        # 检查是否需要处理邀请码
        if record and record.get("invite_by_code"):
            logger.success(f"【{self.id}】{self.address} 已绑定邀请码，使用普通登录页面")
            return base_url, None

        # 处理邀请码逻辑
        if not INVITE_CODES:
            logger.warning(f"【{self.id}】{self.address} 未配置邀请码")
            return base_url, None

        invite_code = random.choice(INVITE_CODES)
        referral_url = f"https://quest.somnia.network/referrals/{invite_code}"
        logger.info(f"【{self.id}】{self.address} 使用邀请码登录页面")
        return referral_url, invite_code

    def _link_x(self, lasted_tab, force=False):
        try:
            lasted_tab.listen.start("https://quest.somnia.network/api/auth/socials")
            connect_btn = get_element(lasted_tab, "x://button[.//span[@class='anticon anticon-twitter']]", 3)
            if not connect_btn:
                logger.error(f"【{self.id}】{self.address}未找到Twitter连接按钮")
                return False

            # 如果已经绑定Twitter，跳过绑定
            if not force and get_element(connect_btn, "x://span[text()='Reconnect']", 5):
                logger.success(f"【{self.id}】{self.address}绑定Twitter成功")
                self.data.update(self.address, {"linked_x": "1"})
                return True

            connect_btn.click()
            tab = lasted_tab.wait.url_change(text="twitter.com/i/flow/login", timeout=20)
            if tab:
                self.browser_controller.login_x_with_auth()

            lasted_tab.wait.url_change(text="twitter.com/i/oauth2/authorize", timeout=20)
            lasted_tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=20)
            allow_btn = get_element(lasted_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not allow_btn:
                logger.error(f"【{self.id}】{self.address}未找到Twitter授权按钮")
                return False

            allow_btn.click()
            sleep(3)
            res = lasted_tab.listen.wait(timeout=90)
            if res.response.body and res.response.body["success"]:
                logger.success(f"【{self.id}】{self.address}绑定Twitter成功")
                self.data.update(self.address, {"linked_x": "1"})
                return True

            logger.error(f"【{self.id}】{self.address}绑定Twitter失败")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】{self.address}Twitter绑定异常，error={str(e)}")
            return False

    def _link_dc(self, lasted_tab):
        try:
            lasted_tab.listen.start("https://quest.somnia.network/api/auth/socials")
            connect_btn = get_element(lasted_tab, "x://button[.//span[@class='anticon anticon-discord']]", 3)
            if get_element(connect_btn, "x://span[text()='Reconnect']", 5):
                logger.success(f"【{self.id}】{self.address}绑定Twitter成功")
                self.data.update(self.address, {"linked_dc": "1"})
                return True

            connect_btn.click()
            lasted_tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=20)
            lasted_tab.set.window.max()
            lasted_tab.wait.ele_displayed("x:(//button)[2]", timeout=20)
            allow_btn = get_element(lasted_tab, "x:(//button)[2]", 5)
            if allow_btn:
                allow_btn.click()
                sleep(2)

            res = lasted_tab.listen.wait(timeout=90)
            if res.response.body and res.response.body["success"]:
                logger.success(f"【{self.id}】{self.address}绑定Discord成功")
                self.data.update(self.address, {"linked_dc": "1"})
                return True

            logger.error(f"【{self.id}】{self.address}绑定Discord失败")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】{self.address}Discord绑定异常，error={str(e)}")
            return False

    def _link_telegram(self, lasted_tab):
        try:
            lasted_tab.listen.start("https://quest.somnia.network/api/auth/socials")
            connect_btn = get_element(lasted_tab, "x://button[.//span//img[@alt='Telegram']]", 5)
            if get_element(connect_btn, "x://span[text()='Reconnect']", 5):
                logger.success(f"【{self.id}】{self.address} 已经绑定了Telegram, 跳过...")
                self.data.update(self.address, {"linked_tg": "1"})
                return True

            if not self.browser_controller.browser_config.tg_cellphone:
                logger.success(f"【{self.id}】{self.address} 未配置Telegram, 跳过...")
                self.data.update(self.address, {"linked_tg": "-1"})
                return True

            connect_btn.click()
            lasted_tab.wait.url_change(text="oauth.telegram.org", timeout=15)
            lasted_tab.wait.ele_displayed("x://button[@class='button-item ripple-handler']", timeout=15)
            accept_btn = get_element(lasted_tab, "x://button[@class='button-item ripple-handler']", 5)
            if accept_btn:
                accept_btn.click()
                sleep(2)

            res = lasted_tab.listen.wait(timeout=90)
            if res.response.body and res.response.body["success"]:
                logger.success(f"【{self.id}】{self.address}绑定Telegram成功")
                self.data.update(self.address, {"linked_tg": "1"})
                return True

            logger.error(f"【{self.id}】{self.address}绑定Telegram失败")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】{self.address}Telegram绑定异常，error={str(e)}")
            return False

    @retry(tries=3, delay=3)
    def _link_invite_code(self, tab, invite_code):
        tab.listen.start("quest.somnia.network/api/users/referrals")
        referral_btn = get_element(tab, "x://button[.//span[text()='Add referral']]", 5)
        if not referral_btn:
            logger.warning(f"【{self.id}】{self.address} 未找到邀请码按钮")
            return False

        referral_btn.click()
        sleep(2)

        try:
            self.browser_controller.okx_wallet_sign()
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 钱包签名失败: {e}")
            return False

        res = tab.listen.wait(timeout=90)
        if not res:
            logger.error(f"【{self.id}】{self.address} 绑定邀请码请求超时")
            return False

        if res.response.status == 200:
            logger.success(f"【{self.id}】{self.address} 绑定邀请码【{invite_code}】成功")
            self.data.update(self.address, {"invite_by_code": invite_code})
            return True
        elif res.response.status == 500:
            logger.success(f"【{self.id}】{self.address} 已经绑定邀请码")
            self.data.update(self.address, {"invite_by_code": "1"})  # 标记为已绑定
            return True
        else:
            logger.error(f"【{self.id}】{self.address} 绑定邀请码失败，状态码: {res.response.status}")
            return False

    def _get_streak_count(self, tab):
        try:
            streak_count_ele = tab.ele("x://h5[text()='Streak Count']/following-sibling::h3", timeout=3)
            if streak_count_ele:
                return int(streak_count_ele.text)
            return 0
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 获取连续签到天数失败: {str(e)}")
            return 0

    def _gm(self, lasted_tab):
        try:

            @retry(tries=3, delay=3)
            def click_gm(tab):
                stats_ele = tab.wait.ele_displayed("x://div[@class='stats-items']", timeout=60)
                if not stats_ele:
                    logger.error(f"【{self.id}】{self.address} stats-items 元素未找到, 页面初始化异常")
                    return False

                if tab.ele("x://button[contains(.,'Next')]", timeout=3):
                    logger.info(f"【{self.id}】{self.address} GM已完成")
                    _data = self.data.get(self.address) or {}
                    streak_count = self._get_streak_count(tab)
                    last_streak_count = safe_int(_data.get("streak_count", 0))
                    self.data.update(self.address, {"streak_count": streak_count})
                    self.data.update(self.address, {"streak_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
                    self.data.update(self.address, {"last_streak_count": last_streak_count})
                    logger.success(f"【{self.id}】{self.address} GM已完成, 连续签到天数: {streak_count}")
                    return True

                tab.listen.start("https://quest.somnia.network/api/users/gm")
                get_element(tab, "x://button[.='gSomnia']", 3).click()

                res = tab.listen.wait(timeout=90)
                if not res:
                    logger.error(f"【{self.id}】{self.address} GM请求超时")
                    return False

                if res.response.body:
                    streak_count = res.response.body.get("streakCount", 0)
                    if int(streak_count) > 0:
                        _data = self.data.get(self.address) or {}
                        last_streak_count = _data.get("streak_count", 0)
                        self.data.update(self.address, {"streak_count": streak_count})
                        self.data.update(
                            self.address, {"streak_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
                        )
                        self.data.update(self.address, {"last_streak_count": last_streak_count})
                        logger.success(f"【{self.id}】{self.address} GM已完成, 连续签到天数: {streak_count}")
                        return True

                return False

            click_gm(lasted_tab)

        except Exception as e:
            logger.error(f"【{self.id}】{self.address}执行GM发生异常, error={str(e)}")

    def _username(self, lasted_tab):
        try:
            username_i = get_element(lasted_tab, "x://div[@id='username']//input", 5)
            if username_i.value:
                self.data.update(self.address, {"username": "1"})
                logger.success(f"【{self.id}】{self.address} 已经设置用户名，跳过...")
                return True

            save_btn = get_element(lasted_tab, "x://div[@id='username']//button", 3)

            faker = Faker()
            max_retries = 5
            base_username = faker.name()

            for attempt in range(max_retries):
                lasted_tab.listen.start(targets="quest.somnia.network/api/users/username", method="PATCH")

                # 第一次尝试使用base_username，后续尝试添加数字1-5
                current_username = base_username if attempt == 0 else f"{attempt}"
                username_i.input(current_username)
                save_btn.click()

                res = lasted_tab.listen.wait(timeout=90)
                if not res:
                    logger.error(f"【{self.id}】{self.address} 设置用户名请求超时")
                    continue

                if res.response.body and res.response.body["ok"]:
                    referral_code = res.response.body["data"]["referralCode"]
                    self.data.update(self.address, {"username": "1", "invite_code": referral_code})
                    logger.success(f"【{self.id}】{self.address} 设置用户名 {username_i.value} 成功")
                    return True
                else:
                    logger.warning(
                        f"【{self.id}】{self.address} 用户名 {username_i.value} 已存在，尝试添加数字 ({attempt + 1}/{max_retries})"
                    )
                    sleep(1)

            logger.error(f"【{self.id}】{self.address} 设置用户名失败，已达到最大重试次数 {max_retries}")
            return False
        except Exception as e:
            logger.error(e)
            return False

    def get_points(self):
        try:
            tab = self.page.latest_tab
            tab.listen.start("https://quest.somnia.network/api/stats")
            tab.get("https://quest.somnia.network/account")

            self.data.flush()
            final_points = "-1.0"
            for packet in tab.listen.steps(timeout=30):
                body = packet.response.body
                points = body.get("finalPoints", -1.0)
                try:
                    # 如果是整数，转为整数字符串；如果是小数，保留一位小数
                    final_points = str(int(points)) if points.is_integer() else f"{points:.1f}"
                    if points > -1:
                        break
                except Exception as e:
                    final_points = points
                    logger.error(f"【{self.id}】{self.address} 获取积分失败: {str(e)}")

            else:
                logger.error(f"【{self.id}】{self.address} 获取积分失败")
                return False

            _data = self.data.get(self.address) or {}
            last_points = _data.get("points", "0")
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if _data:
                self.data.update(
                    self.address,
                    {
                        "points": final_points,
                        "last_points": last_points,
                        "points_last_date_time": current_time,
                    },
                )
            else:
                self.data.add(
                    {
                        "index": self.id,
                        "type": self.browser_type.value,
                        "address": self.address,
                        "points": final_points,
                        "last_points": last_points,
                        "points_last_date_time": current_time,
                    }
                )

            logger.success(f"【{self.id}】{self.address} 最新积分: {final_points}, 上一次积分: {last_points}")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 获取积分失败: {str(e)}")
            return False

    def get_social_name(self):
        try:
            tab = self._login()
            if not tab:
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return

            tab = self.page.latest_tab
            tab.listen.start("https://quest.somnia.network/api/users/me")
            tab.get("https://quest.somnia.network/account")

            self.data.flush()
            res = tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】{self.address} 设置用户名请求超时")
                return False

            if res.response.body:
                discord_name = self.browser_controller.browser_config.dc_username
                bind_discord_name = res.response.body.get("discordName", "")
                twitter_name = self.browser_controller.browser_config.x_user
                bind_twitter_name = res.response.body.get("twitterName", "")
                self.data.update(
                    self.address,
                    {
                        "discord_name": discord_name,
                        "bind_discord_name": bind_discord_name,
                        "bind_discord_error": bind_discord_name is None
                        or bind_discord_name.lower() != discord_name.lower(),
                        "twitter_name": twitter_name,
                        "bind_twitter_name": bind_twitter_name,
                        "bind_twitter_error": bind_twitter_name is None
                        or bind_twitter_name.lower() != twitter_name.lower(),
                    },
                )
                logger.success(
                    f"【{self.id}】 bind_discord_name={bind_discord_name}, bind_twitter_name={bind_twitter_name}"
                )
                return True

            return False
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 更新用户信息失败: {str(e)}")
            return False

    def reconnect_x(self):
        try:
            tab = self._login()
            if not tab:
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return

            # 重新连接Twitter
            self._link_x(tab, force=True)
            logger.success(f"【{self.id}】{self.address} 重新连接Twitter成功")
        except Exception as e:
            logger.error(f"【{self.id}】重新连接Twitter异常，error={str(e)}")

    def social(self):
        try:
            tab = self._login()
            if not tab:
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return

            # self.get_points(moment="before")

            data = self.data.get(self.address)

            # 定义需要执行的任务列表
            tasks = []

            # 根据条件添加需要执行的任务
            if not data or not data.get("linked_x") or data["linked_x"] == "0":
                tasks.append(("Twitter", lambda: self._link_x(tab)))

            if not data or not data.get("linked_dc") or data["linked_dc"] == "0":
                tasks.append(("Discord", lambda: self._link_dc(tab)))

            if not data or not data.get("linked_tg") or data["linked_tg"] == "0":
                tasks.append(("Telegram", lambda: self._link_telegram(tab)))

            if not data or not data.get("username") or data["username"] == "0":
                tasks.append(("用户名", lambda: self._username(tab)))

            # 随机打乱任务顺序
            random.shuffle(tasks)

            # 执行任务
            for task_name, task_func in tasks:
                logger.info(f"【{self.id}】{self.address} 开始{task_name}任务...")
                task_func()

            logger.success(f"【{self.id}】{self.address} 任务执行完成")
        except Exception as e:
            logger.error(f"【{self.id}】执行social任务异常，error={str(e)}")

    def execute(self):
        try:
            tab = self._login()
            if not tab:
                logger.error(f"【{self.id}】{self.address} 登录失败")
                return

            # data = self.data.get(self.address)

            # 定义需要执行的任务列表
            # tasks = []

            # # 根据条件添加需要执行的任务
            # if not data or not data.get("linked_x") or data["linked_x"] == "0":
            #     tasks.append(("Twitter", lambda: self._link_x(tab)))
            #
            # if not data or not data.get("linked_dc") or data["linked_dc"] == "0":
            #     tasks.append(("Discord", lambda: self._link_dc(tab)))
            #
            # if not data or not data.get("linked_tg") or data["linked_tg"] == "0":
            #     tasks.append(("Telegram", lambda: self._link_telegram(tab)))
            #
            # if not data or not data.get("username") or data["username"] == "0":
            #     tasks.append(("用户名", lambda: self._username(tab)))

            # GM任务总是需要执行
            # tasks.append(("GM", lambda: self._gm(tab)))
            #
            # # 随机打乱任务顺序
            # random.shuffle(tasks)
            #
            # # 执行任务
            # for task_name, task_func in tasks:
            #     logger.info(f"【{self.id}】{self.address} 开始{task_name}任务...")
            #     task_func()
            self._gm(tab)
            logger.success(f"【{self.id}】{self.address} 任务执行完成")
        except Exception as e:
            logger.error(f"【{self.id}】执行GM任务异常，error={str(e)}")
