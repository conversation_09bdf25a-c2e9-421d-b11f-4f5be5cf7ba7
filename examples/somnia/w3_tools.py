from eth_account import Account


class WebTools:
    def __init__(self):
        pass

    @staticmethod
    def generate_wallet() -> dict:
        account = Account.create()
        return {
            "address": account.address,
            "private_key": "0x" + account.key.hex()
        }

    @staticmethod
    def get_private_key(address: str) -> str:
        account = Account.from_address(address)
        return "0x" + account.key.hex()
