from loguru import logger
from typing import Dict, Optional, Any
from web3 import Web3
from fake_useragent import UserAgent
from src.utils.proxies import Proxies


# ERC1155 标准的 ABI
nft_abi_erc1155 = [
    {
        "inputs": [
            {"internalType": "address", "name": "account", "type": "address"},
            {"internalType": "uint256", "name": "id", "type": "uint256"},
        ],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    }
]


# ERC721 标准的 ABI
nft_abi_erc721 = [
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
]


class Web3Manager:
    """
    基础交易管理类
    """

    def __init__(
        self,
        index: int,
        private_key: str = None,
        rpc_url: str = None,
        proxy: str = None,
        user_agent: str = None,
        timeout: int = 60,
    ):

        if not private_key:
            logger.error(f"钱包 {index} 私钥为空")
            raise ValueError(f"钱包 {index} 私钥为空")

        if not self._validate_private_key(private_key):
            logger.error(f"钱包 {index} 私钥格式不正确: {private_key}")
            raise ValueError(f"钱包 {index} 私钥格式不正确: {private_key}")

        request_kwargs = self._get_request_kwargs(proxy, user_agent, timeout)
        self.web3 = Web3(Web3.HTTPProvider(rpc_url, request_kwargs=request_kwargs))

        if not self.web3.is_connected():
            logger.error(f"钱包 {index} 无法连接到网络: {rpc_url}")
            raise ValueError(f"钱包 {index} 无法连接到网络: {rpc_url}")

        self.index = index
        self.account = self.web3.eth.account.from_key(private_key)
        self.from_address = self.account.address
        self.private_key = private_key

    def _validate_private_key(self, private_key: str) -> bool:
        try:
            key = private_key.replace("0x", "")
            if len(key) != 64:
                return False

            int(key, 16)
            return True
        except ValueError:
            return False

    def _get_request_kwargs(
        self,
        proxy: Optional[str] = None,
        user_agent: Optional[str] = None,
        timeout: int = 60,
    ) -> Dict[str, Any]:

        request_kwargs = {
            "timeout": timeout,
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": user_agent or UserAgent().chrome,
            },
        }

        if proxy:
            try:
                request_kwargs["proxies"] = Proxies(proxy).get_proxies()
            except Exception as e:
                logger.error(f"代理设置失败: {str(e)}")
        return request_kwargs

    def to_wei(self, amount: float) -> int:
        """
        将以太币转换为 Wei
        Args:
            amount (float): 以太币金额
        Returns:
            int: Wei金额
        """
        return self.web3.to_wei(amount, "ether")

    def to_eth(self, amount_wei: int) -> float:
        """
        将 Wei 转换为以太币
        Args:
            amount_wei (int): Wei金额
        Returns:
            float: 以太币金额
        """
        return self.web3.from_wei(amount_wei, "ether")

    def get_balance(self, address: str = None) -> float:
        """
        检查账户余额
        Args:
            address: 要检查的钱包地址, 如果为空则检查当前钱包地址
        Returns:
            float: 账户余额(单位: ETH)
        """
        try:
            if not address:
                address = self.from_address
            checksum_address = self.web3.to_checksum_address(address)
            balance_wei = self.web3.eth.get_balance(checksum_address)
            balance_eth = self.web3.from_wei(balance_wei, "ether")
            return float(balance_eth)
        except Exception as e:
            logger.error(f"检查余额时发生错误: {str(e)}")
            return 0.0

    def get_balance_erc721(self, nft_contract_address: str, wallet_address: str) -> int:
        """
        获取ERC721合约的NFT余额
        """
        try:
            checksum_contract = self.web3.to_checksum_address(nft_contract_address)
            checksum_wallet = self.web3.to_checksum_address(wallet_address)

            contract = self.web3.eth.contract(
                address=checksum_contract, abi=nft_abi_erc721
            )
            balance = contract.functions.balanceOf(checksum_wallet).call()
            return balance
        except Exception as e:
            logger.error(f"获取NFT余额失败: {str(e)}, 合约地址: {nft_contract_address}")
            return 0

    def get_balance_erc1155(
        self, nft_contract_address: str, wallet_address: str, token_id: int
    ) -> int:

        try:
            checksum_contract = self.web3.to_checksum_address(nft_contract_address)
            checksum_wallet = self.web3.to_checksum_address(wallet_address)

            # 验证合约是否存在
            code = self.web3.eth.get_code(checksum_contract)
            if code == b"" or code == "0x":
                logger.error(f"地址 {nft_contract_address} 不是有效的合约地址")
                return 0

            contract = self.web3.eth.contract(
                address=checksum_contract, abi=nft_abi_erc1155
            )

            balance = contract.functions.balanceOf(checksum_wallet, token_id).call()
            return balance

        except Exception as e:
            logger.error(f"获取NFT余额失败: {str(e)}, 合约地址: {nft_contract_address}")
            return 0

    def estimate_gas(self, transaction: dict) -> int:
        """估算gas并添加缓冲区"""
        try:
            estimated = self.web3.eth.estimate_gas(transaction)
            # 添加10%的缓冲区以确保安全
            return int(estimated * 1.1)
        except Exception as e:
            logger.warning(f"[{self.index}] 估算gas失败: {e}. 使用默认gas限制")
            estimated_gas = 0
            if transaction.get("data"):
                estimated_gas = 100000
            else:
                estimated_gas = 21000
            return estimated_gas

    def get_gas_params(self) -> Dict[str, int]:
        """获取当前gas参数"""
        latest_block = self.web3.eth.get_block("latest")
        base_fee = latest_block["baseFeePerGas"]
        max_priority_fee = self.web3.eth.max_priority_fee

        # 计算 maxFeePerGas (基础费用 + 优先费用)
        max_fee = base_fee + max_priority_fee

        return {
            "maxFeePerGas": max_fee,
            "maxPriorityFeePerGas": max_priority_fee,
        }

    def send_transaction(
        self,
        to_address: str,
        value_in_wei: int,
        data: str = "",
    ) -> Dict[str, Any]:
        try:
            transaction = {
                "from": self.web3.to_checksum_address(self.from_address),
                "nonce": self.web3.eth.get_transaction_count(self.from_address),
                "to": self.web3.to_checksum_address(to_address),
                "value": value_in_wei,
                "chainId": self.web3.eth.chain_id,
                "data": data,
            }

            # 估算gas
            gas_params = self.get_gas_params()
            try:
                estimated_gas = self.estimate_gas(transaction)
                transaction.update({"gas": estimated_gas, **gas_params})
            except Exception as e:
                logger.error(f"[{self.index}] 估算gas失败: {str(e)}")

            # 签名交易
            signed_txn = self.web3.eth.account.sign_transaction(
                transaction, self.private_key
            )
            tx_hash = self.web3.eth.send_raw_transaction(signed_txn.raw_transaction)
            logger.info(f"[{self.index}] 交易已发送: {tx_hash.hex()}")

            # 等待交易确认
            receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash)
            success = receipt["status"] == 1
            if success:
                logger.success(f"[{self.index}] 交易成功确认: 0x{tx_hash.hex()}")
                return {
                    "success": True,
                    "message": "交易成功",
                    "data": {"tx_hash": tx_hash.hex(), "tx_receipt": receipt},
                }
            else:
                logger.error(f"[{self.index}] 交易执行失败: {tx_hash.hex()}")
                return {"success": False, "message": "交易失败"}

        except Exception as e:
            logger.error(f"[{self.index}] 交易发送失败: {str(e)}")
            return {"success": False, "message": "交易发送失败"}

    def _get_transaction_receipt(self, tx_hash):
        """
        nft mint
        """
        try:
            tx_receipt = self.web3.eth.wait_for_transaction_receipt(
                tx_hash, timeout=10, poll_latency=1
            )
            return {"tx_hash": tx_hash, "status": True, "tx_receipt": tx_receipt}
        except Exception as e:
            logger.info(e)
            return {"tx_hash": tx_hash, "status": False, "remark": str(e)}

    def get_nonce(self) -> int:
        """获取nonce"""
        return self.web3.eth.get_transaction_count(self.from_address, "pending")
