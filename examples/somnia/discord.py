import os
from time import sleep

import click
from DrissionPage import Chromium
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.operations import try_click
from src.controllers import Browser<PERSON><PERSON>roller
from src.socials.discord import Discord
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/somnia_discord.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")


class DCChatBot(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)

    def send_message(self, channel_id: str, message: str) -> bool:
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(channel_id, message):
            raise Exception("发送消息失败")
        return True


class DarkTableDiscord(Discord):
    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "darktableccg"
        self.channel_id = "1342142136778489876"
        super().__init__(id, page, browser_type, invite_code, "DarkTable")

    def _pre_verify(self) -> bool:
        return super()._pre_verify()

    def _verify(self) -> bool:
        return super()._verify()

    def _get_roles(self) -> bool:
        return super()._get_roles()

    def _gm(self) -> bool:
        return super()._gm()


class NetherakDemonDiscord(Discord):
    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "netherakdemons"
        self.channel_id = ""
        super().__init__(id, page, browser_type, invite_code, "Netherak Demon")

    def _pre_verify(self) -> bool:
        tab = self._page.latest_tab

        # 检测是否有弹窗
        ele = get_element(tab, "x://div[contains(@class, 'focusLock__')]", 5)
        if ele:
            # 点击弹窗
            try_click(tab, "x://button[@type='button' and .='Apply']")
            sleep(2)

        self._handle_option_selection(tab, 0)
        sleep(2)
        self._handle_option_selection(tab, 0)
        sleep(2)

        try_click(tab, "x://button[contains(.,'Finish') or contains(.,'Next')]")
        sleep(2)

        return True

    def _verify(self) -> bool:
        return super()._verify()

    def _get_roles(self) -> bool:
        return super()._get_roles()

    def _gm(self) -> bool:
        return super()._gm()


class KraftLabDiscord(Discord):
    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "eS5q4U4f"
        self.channel_id = "1342142136778489876"
        super().__init__(id, page, browser_type, invite_code, "KraftLab")

    def _pre_verify(self) -> bool:
        tab = self._page.latest_tab

        # 检测是否有弹窗
        ele = get_element(tab, "x://div[contains(@class, 'focusLock__')]", 5)
        if ele:
            # 点击弹窗
            try_click(tab, "x://button[@type='button' and .='Apply']")
            sleep(2)

        return True

    def _verify(self) -> bool:
        tab = self._page.latest_tab
        # 点击验证按钮
        try_click(tab, "x://button[.='Verify']")
        sleep(2)

        ele = get_element(tab, "x://span[contains(.,'Added role')]", 5)
        if ele:
            logger.success(f"{self.id} {self.dc_name} 验证成功")
            return True

        logger.error(f"{self.id} {self.dc_name} 验证失败")
        return False

    def _get_roles(self) -> bool:
        return True

    def _gm(self) -> bool:
        channel_id = "1311082101415874694"
        dc_chat_bot = DCChatBot(self.browser_type, self.id)
        dc_chat_bot.send_message(channel_id, "gm")
        return True


class MulletCopDiscord(Discord):
    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "aYb46pKNCv"
        super().__init__(id, page, browser_type, invite_code, "Mullet Cop")

    def _pre_verify(self) -> bool:
        tab = self._page.latest_tab

        # 检测是否有弹窗
        ele = get_element(tab, "x://div[contains(@class, 'focusLock__')]", 5)
        if ele:
            # 点击弹窗
            try_click(tab, "x://button[@type='button' and .='Apply']")
            sleep(2)

        self._handle_option_selection(tab, 0)
        sleep(2)
        self._handle_option_selection(tab, 0)
        sleep(2)

        return True


def _handle_join_server(
    browser: BrowserController,
    invite_code: str,
    need_wait_captcha: bool,
    server_id: str,
    name: str,
):
    for _ in range(3):
        index = browser.id
        result = browser.join_dc_server(invite_code, need_wait_captcha, server_id, name)
        is_success = result.get("success")
        if is_success:
            return {"success": True, "message": "加入成功"}

        result_code = result.get("code")
        if result_code == "NO_LOGIN":
            result = browser.login_discord()
            if not result:
                return {"success": False, "message": "dc未登录，登录失败"}

        elif result_code == "NEED_CAPTCHA":
            _leave_dc_server(browser.browser_type, index, 1, False)

    return {"success": False, "message": "加入失败"}


def _join_dc_server(
    type: BrowserType,
    index: str,
    need_wait_captcha: bool = True,
    close_page: bool = True,
):
    browser = BrowserController(type, str(index))
    address = browser.browser_config.evm_address

    data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
    csv_path = os.path.join(data_dir, f"somnia_{type.name.lower()}.csv")
    data_util = DataUtil(csv_path)

    record = data_util.get(address) or {}
    servers = [
        # {
        #     "name": "Pixcape Games",
        #     "server_id": "1367481863270174811",
        #     "invite_code": "pixcapegames",
        # },
        {"name": "Somnia", "server_id": "1209923224620761088", "invite_code": "somnia"}
    ]
    fail_servers = []
    for server in servers:
        invite_code = server.get("invite_code")
        server_id = server.get("server_id")
        name = server.get("name")
        try:
            if record.get(f"Discord {name}", "") == "1":
                logger.success(f"【{index}】已加入 {name} 服务器")
                continue

            result = _handle_join_server(
                browser, invite_code, need_wait_captcha, server_id, name
            )
            if not result.get("success"):
                logger.error(
                    f"【{index}】加入 {name} 服务器失败: {result.get('message')}"
                )
                fail_servers.append(invite_code)
                continue

            data_util.update(address, {f"Discord {name}": "1"})
            logger.success(f"【{index}】加入 {name} 服务器成功")

        except Exception as e:
            logger.error(f"{index} 加群 {name} 失败: {e}")
            fail_servers.append(invite_code)
    if close_page:
        browser.close_page()
    return fail_servers


def _leave_dc_server(type: BrowserType, index: str, count=1, is_close=True):
    browser = BrowserController(type, str(index))
    servers = [
        "Stable Jack",
        "Salt",
        "Haifu",
        "QRusader",
        "WarpGate Official",
        "EnsoFi",
    ]
    success_count = 0
    for name in servers:
        try:
            discord = Discord(index, browser.page, browser.browser_type)
            result = discord.leave_guild(name)
            if result:
                success_count = success_count + 1
                # logger.info("success_count is: " + str(success_count))
                if count == success_count:
                    if is_close:
                        browser.close_page()
                    return True
        except Exception as e:
            logger.error(f"{index} 退出群组 {name} 失败: {e}")
    if is_close:
        browser.close_page()
    return False


@click.group()
def cli():
    pass


@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--wait", is_flag=True, help="是否等待验证码")
@click.option(
    "-c", "--close", is_flag=True, default=True, flag_value=False, help="是否关闭浏览器"
)
def join_server(index, type, wait, close):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        try:
            fail_servers = _join_dc_server(type, _index, wait, close)
            if fail_servers and len(fail_servers) > 0:
                fail_indices.append(str(_index))
        except Exception as e:
            logger.error(f"{_index} 加群失败: {e}")
            fail_indices.append(str(_index))
    if fail_indices:
        idxs = ",".join(fail_indices)
        logger.error(f"加群失败：{idxs}")


@cli.command("leave")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def leave_server(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            result = _leave_dc_server(type, _index)
            if result:
                logger.success(f"{_index} 退出群组成功")
            else:
                logger.error(f"{_index} 退出群组失败")
        except Exception as e:
            logger.error(f"{_index} 退出群组失败: {e}")


# python3 examples/somnia/discord.py mc -i 1 -t chrome
# python3 examples/somnia/discord.py kl -i 1 -t chrome
# python3 examples/somnia/discord.py join -t chrome -c -w -i 1

if __name__ == "__main__":
    cli()
    # _join_dc_server(BrowserType.CHROME, "3", False)

    # _join_kraft_lab(BrowserType.CHROME, "4")
    # _join_mullet_cop(BrowserType.CHROME, "3")
