#!/usr/bin/env python3
"""
Funquiz API 使用示例

这个文件展示了如何使用 FunquizApiClient 和相关工具类
"""

import os
import random
from time import sleep

from funquiz_api import FunquizApiClient
from funquiz_data_utils import <PERSON><PERSON>zDataUtil
from loguru import logger

from src.controllers import BrowserController
from src.browsers import BrowserType
from src.utils.common import get_project_root_path, parse_indices


def example_verify_player():
    """查询玩家状态示例"""
    print("\n=== 查询玩家状态示例 ===")

    # 示例钱包地址
    address = "******************************************"
    private_key = ""
    try:
        # 创建客户端
        client = FunquizApiClient(address=address, index=1, private_key=private_key)

        # 查询玩家状态
        result = client.verify_player()

        if result["status"] == "success":
            player_data = result["data"]
            logger.success(f"玩家状态查询成功！")
            logger.info(f"地址: {address}")
            logger.info(f"分数: {player_data.get('score', 0)}")
            logger.info(f"完成状态: {player_data.get('completed', False)}")
            logger.info(f"API响应: {result['data']}")
        else:
            logger.error(f"玩家状态查询失败: {result['error']}")

    except Exception as e:
        logger.error(f"查询示例执行失败: {e}")


def example_single_address():
    """单个地址提交分数示例"""
    print("\n=== 单个地址提交分数示例 ===")

    # 示例钱包地址
    address = "******************************************"

    # 可选：设置代理和自定义User-Agent
    proxy = None  # "http://username:<EMAIL>:8080"
    user_agent = None  # 使用默认的Chrome 138 User-Agent

    try:
        # 创建客户端
        client = FunquizApiClient(address=address, proxy=proxy, user_agent=user_agent, private_key="", index=1)

        # 测试连接
        if client.test_connection():
            logger.success("API连接测试成功")
        else:
            logger.warning("API连接测试失败，但继续尝试提交")

        # 提交分数（会自动检查是否已完成且分数超过1000，跳过合约交互）
        result = client.save_score(skip_contract=True)

        if result["status"] == "success":
            score_data = result["score_data"]
            logger.success(f"分数提交成功！")
            logger.info(f"地址: {address}")
            logger.info(f"分数: {score_data['score']}")
            logger.info(f"正确答案: {score_data['questionsCorrect']}/20")
            logger.info(f"API响应: {result['data']}")
        elif result["status"] == "skipped":
            logger.info(f"任务已跳过: {result['reason']}")
            logger.info(f"当前状态: {result['data']}")
        else:
            logger.error(f"分数提交失败: {result['error']}")

    except Exception as e:
        logger.error(f"示例执行失败: {e}")


def example_with_contract_interaction():
    """包含合约交互的完整示例"""
    print("\n=== 包含合约交互的完整示例 ===")

    # 示例钱包地址和私钥（请替换为真实的）
    address = "******************************************"
    private_key = "your_private_key_here"  # 请替换为真实私钥

    try:
        # 创建客户端（包含私钥用于合约交互）
        client = FunquizApiClient(address=address, private_key=private_key, index=1)

        # 完整流程：先合约交互，再提交分数
        result = client.save_score()

        if result["status"] == "success":
            score_data = result["score_data"]
            contract_result = result.get("contract_result")

            logger.success(f"完整任务成功！")
            logger.info(f"地址: {address}")
            logger.info(f"分数: {score_data['score']}")
            logger.info(f"正确答案: {score_data['questionsCorrect']}/20")

            if contract_result:
                logger.info(f"合约交易哈希: {contract_result.get('tx_hash')}")
                logger.info(f"发送金额: {contract_result.get('amount')} STT")

        elif result["status"] == "skipped":
            logger.info(f"任务已跳过: {result['reason']}")
        else:
            logger.error(f"任务失败: {result['error']}")

    except Exception as e:
        logger.error(f"合约交互示例失败: {e}")


def example_custom_score():
    """自定义分数数据示例"""
    print("\n=== 自定义分数数据示例 ===")

    address = "******************************************"

    # 自定义分数数据
    custom_score_data = {
        "address": address,
        "score": 1350,  # 自定义分数
        "questionsCorrect": 18,  # 自定义正确答案数
        "questionsAttempted": 20,  # 固定值
    }

    try:
        client = FunquizApiClient(address=address)

        # 使用自定义分数数据提交（force=True 强制提交，忽略已完成检查）
        result = client.save_score(score_data=custom_score_data, force=True)

        if result["status"] == "success":
            logger.success(f"自定义分数提交成功: {custom_score_data['score']} 分")
        elif result["status"] == "skipped":
            logger.info(f"任务已跳过: {result['reason']}")
        else:
            logger.error(f"自定义分数提交失败: {result['error']}")

    except Exception as e:
        logger.error(f"自定义分数示例失败: {e}")


def example_multiple_addresses():
    """多个地址处理示例"""
    print("\n=== 多个地址处理示例 ===")

    # 示例地址列表
    addresses = [
        "******************************************",
        "******************************************",
        "0x8ba1f109551bD432803012645Hac136c5C2BD754",
    ]

    results = []

    try:
        logger.info(f"开始处理 {len(addresses)} 个地址...")

        for i, address in enumerate(addresses, 1):
            logger.info(f"处理地址 {i}/{len(addresses)}: {address}")

            try:
                # 为每个地址创建独立的客户端
                client = FunquizApiClient(address=address)

                # 提交分数（会自动检查是否已完成）
                result = client.save_score()
                result["address"] = address
                results.append(result)

                # 显示结果
                if result["status"] == "success":
                    score_data = result.get("score_data", {})
                    logger.success(f"✓ {address}: {score_data.get('score', 'N/A')} 分")
                elif result["status"] == "skipped":
                    logger.info(f"⏭ {address}: {result.get('reason', 'Skipped')}")
                else:
                    logger.error(f"✗ {address}: {result.get('error', 'Unknown error')}")

                # 延迟
                if i < len(addresses):
                    sleep(2)

            except Exception as e:
                error_result = {"status": "failed", "address": address, "error": str(e)}
                results.append(error_result)
                logger.error(f"✗ {address}: {e}")

        # 显示结果摘要
        total = len(results)
        success = len([r for r in results if r.get("status") == "success"])
        skipped = len([r for r in results if r.get("status") == "skipped"])
        failed = len([r for r in results if r.get("status") == "failed"])

        logger.info(f"处理完成:")
        logger.info(f"总数: {total}")
        logger.info(f"成功: {success}")
        logger.info(f"跳过: {skipped}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(success/total*100):.1f}%" if total > 0 else "0%")

    except Exception as e:
        logger.error(f"多地址处理示例失败: {e}")


def example_with_csv_logging():
    """使用CSV日志记录示例"""
    print("\n=== CSV日志记录示例 ===")

    try:
        # 初始化数据工具
        data_util = FunquizDataUtil("funquiz_example_results.csv")

        # 示例数据
        test_addresses = [
            "******************************************",
            "******************************************",
        ]

        for i, address in enumerate(test_addresses, 1):
            logger.info(f"处理地址 {i}: {address}")

            # 创建客户端并提交分数
            client = FunquizApiClient(address=address)
            result = client.save_score()

            # 记录结果到CSV
            data_util.log_api_result(
                index=i, wallet_address=address, api_result=result, proxy=client.proxy, user_agent=client.user_agent
            )

            # 短暂延迟
            if i < len(test_addresses):
                sleep(2)

        # 显示统计信息
        stats = data_util.get_statistics()
        logger.info("CSV记录统计:")
        logger.info(f"总记录: {stats['total']}")
        logger.info(f"成功: {stats['success']}")
        logger.info(f"跳过: {stats['skipped']}")
        logger.info(f"失败: {stats['failed']}")
        logger.info(f"成功率: {stats['success_rate']}")
        logger.info(f"平均分数: {stats['avg_score']}")
        logger.info(f"最高分数: {stats['max_score']}")
        logger.info(f"最低分数: {stats['min_score']}")

    except Exception as e:
        logger.error(f"CSV日志示例失败: {e}")


def example_with_browser_integration():
    """与浏览器集成示例（从浏览器配置获取地址和代理）"""
    print("\n=== 浏览器集成示例 ===")

    try:
        # 使用浏览器配置（示例索引1）
        browser_type = BrowserType.CHROME  # 或其他浏览器类型
        index = "1"

        # 创建浏览器控制器获取配置
        controller = BrowserController(browser_type, index)
        address = controller.browser_config.evm_address
        proxy = controller.browser_config.proxy

        logger.info(f"从浏览器配置获取:")
        logger.info(f"地址: {address}")
        logger.info(f"代理: {proxy}")

        # 创建客户端
        client = FunquizApiClient(address=address, proxy=proxy)

        # 提交分数
        result = client.save_score()

        if result["status"] == "success":
            score_data = result["score_data"]
            logger.success(f"浏览器集成提交成功: {score_data['score']} 分")
        elif result["status"] == "skipped":
            logger.info(f"浏览器集成任务已跳过: {result['reason']}")
        else:
            logger.error(f"浏览器集成提交失败: {result['error']}")

        # 关闭浏览器控制器
        controller.close_page()

    except Exception as e:
        logger.error(f"浏览器集成示例失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    logger.add("logs/funquiz_example.log", rotation="10MB", level="INFO")

    print("Funquiz API 使用示例")
    print("=" * 50)

    # 运行各种示例
    example_verify_player()
    example_single_address()
    # example_with_contract_interaction()  # 需要真实私钥
    # example_custom_score()
    # example_multiple_addresses()
    # example_with_csv_logging()

    # 注意：浏览器集成示例需要有效的浏览器配置
    # example_with_browser_integration()

    print("\n所有示例执行完成！")
    print("查看日志文件: logs/funquiz_example.log")
    print("查看CSV结果: examples/somnia/funquiz_example_results.csv")


if __name__ == "__main__":
    main()
