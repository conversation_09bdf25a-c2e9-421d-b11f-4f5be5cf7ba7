import os
import random
import json
from datetime import datetime
from time import sleep

import requests
from loguru import logger
from retry import retry
from dotenv import load_dotenv

from src.utils.proxies import Proxies

# 加载环境变量
load_dotenv()

# Somnia网络配置
SOMNIA_RPC = os.getenv("SOMNIA_RPC", "https://rpc.somnia.network")
FUNQUIZ_CONTRACT_ADDRESS = "0x8A9Ec2DC6E97208ca3930CcaA01021595Fa07410"
FUNQUIZ_CONTRACT_DATA = "0xe6c84583"
FUNQUIZ_AMOUNT_STT = 0.01  # 0.01 STT


class FunquizApiClient:
    """Funquiz API客户端 - 用于提交quiz分数"""

    BASE_URL = "https://funquiz.vercel.app"

    def __init__(self, address: str, index: int, private_key: str, proxy: str = None, user_agent: str = None):
        """
        初始化Funquiz API客户端

        Args:
            address: 钱包地址
            private_key: 私钥
            proxy: 代理地址 (可选)
            user_agent: 自定义User-Agent (可选)
        """
        self.address = address
        self.private_key = private_key
        self.index = index
        self.proxy = proxy
        self.user_agent = user_agent or (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/********* Safari/537.36"
        )
        self.web3_manager = None
        self._init_http_client()
        if not private_key:
            raise Exception(f"{address} 请传入私钥")

        self._init_web3_manager()

    def _init_web3_manager(self):
        """初始化Web3管理器用于合约交互"""
        try:
            # 导入Web3Manager (需要在这里导入避免循环导入)
            import sys
            import os

            sys.path.append(os.path.dirname(__file__))
            from w3_manager import Web3Manager

            self.web3_manager = Web3Manager(
                index=self.index,
                private_key=self.private_key,
                rpc_url=SOMNIA_RPC,
                proxy=self.proxy,
                user_agent=self.user_agent,
            )
            logger.info(f"Web3管理器初始化成功 - 地址: {self.address}")
        except Exception as e:
            logger.error(f"Web3管理器初始化失败: {e}")
            self.web3_manager = None

    def _init_http_client(self):
        """初始化HTTP客户端"""
        try:
            # 使用Chrome 138浏览器指纹 (根据用户偏好)
            self.headers = {
                "accept": "*/*",
                "accept-language": "en-US,en;q=0.9",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": self.user_agent,
                "referer": "https://funquiz.vercel.app/",
            }

            # 设置代理
            self.proxies = None
            if self.proxy:
                try:
                    self.proxies = Proxies(self.proxy).get_proxies()
                    logger.info(f"使用代理: {self.proxy}")
                except Exception as e:
                    logger.error(f"代理设置失败: {e}")
                    self.proxies = None

            # 创建session
            self.session = requests.Session()
            self.session.headers.update(self.headers)

            if self.proxies:
                self.session.proxies.update(self.proxies)

            logger.info(f"HTTP客户端初始化成功 - 地址: {self.address}")

        except Exception as e:
            logger.error(f"HTTP客户端初始化失败: {e}")
            raise

    def generate_random_score_data(self):
        """
        生成随机的分数数据

        Returns:
            dict: 包含随机生成的分数数据
        """
        # 生成随机数据 (根据用户要求)
        questions_attempted = 20  # 固定值
        questions_correct = random.randint(15, 20)  # 15-20之间
        score = random.randint(1020, 1500)  # 1020-1500之间

        return {
            "address": self.address,
            "score": score,
            "questionsCorrect": questions_correct,
            "questionsAttempted": questions_attempted,
        }

    @retry(tries=3, delay=2)
    def interact_with_contract(self):
        """
        与Funquiz合约交互 - 发送0.01 STT

        Returns:
            dict: 交易结果
        """
        if not self.web3_manager:
            return {"status": "failed", "error": "Web3管理器未初始化，无法进行合约交互"}

        try:
            logger.info(f"开始与Funquiz合约交互 - 地址: {self.address}")

            # 检查余额
            balance = self.web3_manager.get_balance()
            required_amount = FUNQUIZ_AMOUNT_STT + 0.01  # 预留gas费用

            if balance < required_amount:
                error_msg = f"余额不足，当前余额: {balance} STT，需要: {required_amount} STT"
                logger.error(error_msg)
                return {"status": "failed", "error": error_msg}

            # 转换金额为Wei
            amount_wei = self.web3_manager.to_wei(FUNQUIZ_AMOUNT_STT)

            # 发送交易到合约
            result = self.web3_manager.send_transaction(
                to_address=FUNQUIZ_CONTRACT_ADDRESS, value_in_wei=amount_wei, data=FUNQUIZ_CONTRACT_DATA
            )

            if result.get("success"):
                tx_hash = result.get("data", {}).get("tx_hash")
                logger.success(f"合约交互成功 - 地址: {self.address}, 交易哈希: {tx_hash}")
                return {"status": "success", "tx_hash": tx_hash, "amount": FUNQUIZ_AMOUNT_STT, "error": None}
            else:
                error_msg = f"合约交互失败: {result.get('message', 'Unknown error')}"
                logger.error(error_msg)
                return {"status": "failed", "error": error_msg}

        except Exception as e:
            error_msg = f"合约交互异常: {str(e)}"
            logger.error(error_msg)
            return {"status": "failed", "error": error_msg}

    @retry(tries=3, delay=2)
    def verify_player(self):
        """
        查询玩家状态

        Returns:
            dict: 查询结果
        """
        try:
            url = f"{self.BASE_URL}/api/event/verify-player/{self.address}"

            logger.info(f"查询玩家状态: {self.address}")

            response = self.session.get(url=url, timeout=30)

            # 检查响应状态
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    logger.info(
                        f"玩家状态查询成功 - 地址: {self.address}, 分数: {response_data.get('score', 0)}, 完成状态:"
                        f" {response_data.get('completed', False)}"
                    )
                    return {"status": "success", "data": response_data, "error": None}
                except:
                    response_data = response.text
                    return {"status": "success", "data": response_data, "error": None}
            else:
                error_msg = f"查询API返回错误状态码: {response.status_code}, 响应: {response.text}"
                logger.error(f"玩家状态查询失败 - {error_msg}")
                return {"status": "failed", "data": None, "error": error_msg}

        except Exception as e:
            error_msg = f"玩家状态查询异常: {str(e)}"
            logger.error(f"玩家状态查询失败 - {error_msg}")
            return {"status": "failed", "data": None, "error": error_msg}

    @retry(tries=3, delay=2)
    def save_score(self, score_data: dict = None, force: bool = False, skip_contract: bool = False):
        """
        提交分数到funquiz API

        Args:
            score_data: 分数数据，如果为None则自动生成随机数据
            force: 是否强制提交，忽略已完成检查
            skip_contract: 是否跳过合约交互

        Returns:
            dict: API响应结果
        """
        try:
            # 如果不是强制提交，先检查是否已经完成且分数超过1000
            if not force:
                verify_result = self.verify_player()
                if verify_result["status"] == "success" and verify_result["data"]:
                    player_data = verify_result["data"]
                    if isinstance(player_data, dict):
                        completed = player_data.get("completed", False)
                        score = player_data.get("score", 0)

                        if completed and score > 1000:
                            logger.info(f"玩家已完成任务且分数超过1000 - 地址: {self.address}, 分数: {score}, 跳过提交")
                            return {
                                "status": "skipped",
                                "data": player_data,
                                "score_data": None,
                                "contract_result": None,
                                "error": None,
                                "reason": f"已完成任务且分数({score})超过1000",
                            }

            # 第一步：与合约交互（除非跳过）
            contract_result = None
            if not skip_contract and self.web3_manager:
                logger.info(f"步骤1: 与Funquiz合约交互 - 地址: {self.address}")
                contract_result = self.interact_with_contract()

                if contract_result["status"] != "success":
                    logger.error(f"合约交互失败，终止分数提交 - {contract_result['error']}")
                    return {
                        "status": "failed",
                        "data": None,
                        "score_data": None,
                        "contract_result": contract_result,
                        "error": f"合约交互失败: {contract_result['error']}",
                    }

                logger.success(f"合约交互成功，交易哈希: {contract_result.get('tx_hash')}")
                # 等待一段时间确保交易确认
                sleep(random.randint(30, 90))
            elif not skip_contract:
                logger.warning(f"Web3管理器未初始化，跳过合约交互")

            # 第二步：提交分数到API
            logger.info(f"步骤2: 提交分数到API - 地址: {self.address}")

            if score_data is None:
                score_data = self.generate_random_score_data()

            url = f"{self.BASE_URL}/api/save-score"

            logger.info(f"提交分数数据: {score_data}")

            response = self.session.post(url=url, json=score_data, timeout=30)

            # 检查响应状态
            if response.status_code in [200, 201]:
                logger.success(f"分数提交成功 - 地址: {self.address}, 分数: {score_data['score']}")
                try:
                    response_data = response.json()
                except:
                    response_data = response.text

                return {
                    "status": "success",
                    "data": response_data,
                    "score_data": score_data,
                    "contract_result": contract_result,
                    "error": None,
                }
            else:
                error_msg = f"API返回错误状态码: {response.status_code}, 响应: {response.text}"
                logger.error(f"分数提交失败 - {error_msg}")
                return {
                    "status": "failed",
                    "data": None,
                    "score_data": score_data,
                    "contract_result": contract_result,
                    "error": error_msg,
                }

        except Exception as e:
            error_msg = f"分数提交异常: {str(e)}"
            logger.error(f"分数提交失败 - {error_msg}")
            return {
                "status": "failed",
                "data": None,
                "score_data": score_data,
                "contract_result": contract_result,
                "error": error_msg,
            }

    def test_connection(self):
        """
        测试API连接

        Returns:
            bool: 连接是否成功
        """
        try:
            response = self.session.get(f"{self.BASE_URL}/", timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
