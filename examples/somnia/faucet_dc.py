import os
import random
from datetime import datetime
from decimal import Decimal
from time import sleep

import click
from loguru import logger
from retry import retry
from w3_manager import Web3Manager
from w3_tools import WebTools

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import Browser<PERSON><PERSON>roller
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.secure_encryption import SecureEncryption

PROXY_URL = os.getenv("PROXY_URL")
SOMNIA_RPC = os.getenv("SOMNIA_RPC")
default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
csv_path = os.path.join(data_dir, "faucet_dc.csv")
data_util = DataUtil(csv_path, headers=["index", "pk", "address", "to_address", "faucet_date"])


class WalletGenerator:
    def __init__(self, data: DataUtil):
        self.data = data

    def _generate_wallet(self):
        wallet_info = WebTools.generate_wallet()
        private_key = wallet_info.get("private_key")
        address = wallet_info.get("address")

        return address, private_key

    def generate_wallet(self, count: int = 100):
        for _ in range(count):
            address, private_key = self._generate_wallet()
            self.data.add(
                {
                    "index": _ + 1,
                    "pk": private_key,
                    "address": address,
                    "to_address": "",
                }
            )


class Faucet(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        self.channel_id = "1306988055135256738"

    @retry(tries=3, delay=1)
    def task(self, to_address: str) -> bool:
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not to_address or not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(self.channel_id, to_address):
            raise Exception("发送faucet消息失败")
        return True


def get_wallet_by_to_address(to_address: str):
    wallets = data_util.list()
    for wallet in wallets:
        if wallet.get("to_address") == to_address and not wallet.get("faucet_date"):
            return wallet.get("address")
    return None


def get_wallets_by_to_address(to_address: str):
    wallets = data_util.list()
    return [wallet for wallet in wallets if wallet.get("to_address") == to_address and wallet.get("faucet_date")]


def _faucet_task(index: int, type: BrowserType):
    try:
        faucet = Faucet(type, str(index))
        to_address = faucet.browser_config.evm_address
        pk = faucet.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)

        w3_manager = Web3Manager(
            str(index),
            pk,
            SOMNIA_RPC,
            faucet.browser_config.proxy,
            faucet.browser_config.user_agent,
        )

        balance = w3_manager.get_balance()
        # 查询余额是否大于1
        if balance > 1.1:
            logger.info(f"【{str(index)}】{to_address} 余额大于1.1，跳过")
            return {"success": True, "message": "余额大于1.1，跳过"}

        wallet_address = get_wallet_by_to_address(to_address)
        if not wallet_address:
            logger.error(f"钱包序号 {index} 没有找到钱包")
            return {"success": False, "message": "没有找到钱包"}

        result = faucet.task(wallet_address)
        if result:
            data_util.update(
                wallet_address,
                {"faucet_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")},
            )
            return {"success": True, "message": "领水成功"}

        return {"success": False, "message": "领水失败"}

    except Exception as e:
        logger.error(f"钱包序号 {index} 领水失败: {e}")
        return {"success": False, "message": "领水失败"}


def _send_token_task(index, pk, to_address, proxy, user_agent):
    try:
        w3_manager = Web3Manager(
            str(index),
            pk,
            SOMNIA_RPC,
            proxy,
            user_agent,
        )

        balance = w3_manager.get_balance(w3_manager.from_address)
        if balance < 0.01:
            logger.info(f"【{str(index)}】{to_address} 余额小于0.01，跳过")
            return {"success": True, "message": "余额小于0.01，跳过"}

        # 计算gas所需金额
        gas_limit = Decimal("21000")
        gas_params = w3_manager.get_gas_params()
        max_fee_per_gas = Decimal(str(gas_params.get("maxFeePerGas")))
        gas_fee = int(gas_limit * max_fee_per_gas * Decimal("2"))

        balance_wei = Decimal(str(w3_manager.to_wei(balance)))
        amount_wei = balance_wei - gas_fee

        result = w3_manager.send_transaction(to_address, int(amount_wei))
        is_success = result.get("success")
        if is_success:
            tx_hash = result.get("data").get("tx_hash")
            logger.success(f"【{index}】{w3_manager.from_address} 发送token成功: {tx_hash}")
            return {"success": True, "message": "发送token成功"}

        return {"success": False, "message": "发送token失败"}
    except Exception as e:
        logger.error(f"钱包序号 {index} 发送token失败: {e}")
        return {"success": False, "message": "发送token失败"}


def _transfer_task(index: int, type: BrowserType):
    try:
        faucet = Faucet(type, str(index))
        to_address = faucet.browser_config.evm_address
        pk = faucet.browser_config.evm_private_key
        if pk and SecureEncryption.is_encrypted(pk):
            pk = SecureEncryption.decrypt(pk)

        w3_manager = Web3Manager(
            str(index),
            pk,
            SOMNIA_RPC,
            faucet.browser_config.proxy,
            faucet.browser_config.user_agent,
        )
        balance = w3_manager.get_balance()
        if balance > 1.1:
            logger.info(f"【{str(index)}】{to_address} 余额大于1.1，跳过")
            return {"success": True, "message": "余额大于1.1，跳过"}

        wallets = get_wallets_by_to_address(to_address)
        if len(wallets) == 0:
            logger.info(f"【{str(index)}】{to_address} 没有找到钱包")
            return {"success": True, "message": "没有找到钱包"}

        for wallet in wallets:
            pk = wallet.get("pk")
            if pk and SecureEncryption.is_encrypted(pk):
                pk = SecureEncryption.decrypt(pk)
            result = _send_token_task(
                index,
                pk,
                to_address,
                faucet.browser_config.proxy,
                faucet.browser_config.user_agent,
            )

            if not result.get("success"):
                logger.error(f"钱包序号 {index} 转账失败: {result.get('message')}")
                continue
            else:
                logger.success(f"钱包序号 {index} 转账成功: {result.get('message')}")
                sleep(3)

        return {"success": True, "message": "转账成功"}

    except Exception as e:
        logger.error(f"钱包序号 {index} 转账失败: {e}")
        return {"success": False, "message": "转账失败"}


@click.group()
def cli():
    pass


@cli.command("g")
@click.option("-c", "--count", type=int, default=100, help="生成钱包数量")
def generate(count):
    wallet_generator = WalletGenerator(data_util)
    wallet_generator.generate_wallet(count)


@cli.command("f")
@click.option("-i", "--index", type=str, help="钱包序号")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
def faucet(index, type):
    indices = parse_indices(index)
    random.shuffle(indices)
    fail_indices = []
    for _index in indices:
        result = _faucet_task(_index, type)
        if not result.get("success"):
            fail_indices.append(str(_index))
            logger.error(f"钱包序号 {_index} 领水失败: {result.get('message')}")
            continue

    if len(fail_indices) > 0:
        logger.error(f"领水失败: {','.join(fail_indices)}")


@cli.command("t")
@click.option("-i", "--index", type=str, help="钱包序号")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
def transfer(index, type):
    indices = parse_indices(index)
    random.shuffle(indices)
    fail_indices = []
    for _index in indices:
        result = _transfer_task(_index, type)
        if not result.get("success"):
            fail_indices.append(str(_index))
            logger.error(f"钱包序号 {_index} 转账失败: {result.get('message')}")
            continue

    if len(fail_indices) > 0:
        logger.error(f"转账失败: {','.join(fail_indices)}")


if __name__ == "__main__":
    cli()
    # _faucet_task(1, BrowserType.CHROME)
    # _transfer_task(1, BrowserType.CHROME)
