import os
from datetime import datetime

from loguru import logger

from src.utils.common import get_project_root_path
from src.utils.hhcsv import HHCSV

# Funquiz API结果CSV数据结构
FUNQUIZ_DATA_PROPS = [
    "index",
    "wallet_address",
    "score",
    "questions_correct",
    "questions_attempted",
    "status",
    "response_data",
    "error_message",
    "skip_reason",
    "contract_tx_hash",
    "contract_status",
    "timestamp",
    "proxy_used",
    "user_agent",
]


class FunquizDataUtil:
    """Funquiz API结果数据管理工具"""
    
    def __init__(self, csv_filename: str = "funquiz_results.csv"):
        """
        初始化数据工具
        
        Args:
            csv_filename: CSV文件名
        """
        self.csv_filename = csv_filename
        self._init_csv()

    def _init_csv(self):
        """初始化CSV文件"""
        try:
            # 设置CSV文件路径
            data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
            self._csv_path = os.path.join(data_dir, self.csv_filename)
            self._csv = HHCSV(self._csv_path, FUNQUIZ_DATA_PROPS)
            logger.info(f"初始化Funquiz数据CSV文件: {self._csv_path}")
        except Exception as e:
            logger.error(f"初始化Funquiz CSV文件失败: {str(e)}")
            raise

    def list(self):
        """
        获取所有记录
        
        Returns:
            list: 所有记录列表
        """
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部Funquiz数据失败: {str(e)}")
            return []

    def get_by_index(self, index):
        """
        根据索引获取记录
        
        Args:
            index: 索引值
            
        Returns:
            dict: 记录数据，如果不存在返回空字典
        """
        try:
            result = self._csv.query({"index": str(index)})
            return result[0] if result else {}
        except Exception as e:
            logger.error(f"查询 index {index} Funquiz数据失败: {str(e)}")
            return {}

    def get_by_wallet(self, wallet_address):
        """
        根据钱包地址获取记录
        
        Args:
            wallet_address: 钱包地址
            
        Returns:
            list: 记录列表
        """
        try:
            result = self._csv.query({"wallet_address": wallet_address})
            return result if result else []
        except Exception as e:
            logger.error(f"查询 {wallet_address} Funquiz数据失败: {str(e)}")
            return []

    def add(self, data):
        """
        添加新记录
        
        Args:
            data: 记录数据
        """
        try:
            # 添加时间戳
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            self._csv.add_row(data)
            logger.info(f"添加Funquiz记录: index={data.get('index')}, wallet={data.get('wallet_address')}, score={data.get('score')}")
        except Exception as e:
            logger.error(f"新增Funquiz数据失败, data={data}, error={str(e)}")

    def update_by_index(self, index, data):
        """
        根据索引更新记录
        
        Args:
            index: 索引值
            data: 更新数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            criteria = {"index": str(index)}
            # 更新时间戳
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = self._csv.update_row(criteria, data)
            if result > 0:
                logger.info(f"更新Funquiz记录: index={index}, data={data}")
            return result > 0
        except Exception as e:
            logger.error(f"更新Funquiz数据失败, index={index}, data={data}, error={str(e)}")
            return False

    def update_by_wallet(self, wallet_address, data):
        """
        根据钱包地址更新记录
        
        Args:
            wallet_address: 钱包地址
            data: 更新数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            criteria = {"wallet_address": wallet_address}
            # 更新时间戳
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = self._csv.update_row(criteria, data)
            if result > 0:
                logger.info(f"更新Funquiz记录: wallet={wallet_address}, data={data}")
            return result > 0
        except Exception as e:
            logger.error(f"更新Funquiz数据失败, wallet={wallet_address}, data={data}, error={str(e)}")
            return False

    def log_api_result(self, index, wallet_address, api_result, proxy=None, user_agent=None):
        """
        记录API调用结果
        
        Args:
            index: 索引
            wallet_address: 钱包地址
            api_result: API调用结果
            proxy: 使用的代理
            user_agent: 使用的User-Agent
        """
        try:
            # 准备记录数据
            record_data = {
                "index": str(index),
                "wallet_address": wallet_address,
                "status": api_result.get("status", "unknown"),
                "error_message": api_result.get("error", ""),
                "skip_reason": api_result.get("reason", ""),
                "proxy_used": proxy or "",
                "user_agent": user_agent or "",
            }

            # 记录合约交互结果
            contract_result = api_result.get("contract_result")
            if contract_result:
                record_data["contract_tx_hash"] = contract_result.get("tx_hash", "")
                record_data["contract_status"] = contract_result.get("status", "")
            else:
                record_data["contract_tx_hash"] = ""
                record_data["contract_status"] = ""

            # 如果有分数数据，添加分数相关字段
            score_data = api_result.get("score_data", {})
            if score_data:
                record_data.update({
                    "score": score_data.get("score", ""),
                    "questions_correct": score_data.get("questionsCorrect", ""),
                    "questions_attempted": score_data.get("questionsAttempted", ""),
                })

            # 如果有响应数据，记录响应
            response_data = api_result.get("data")
            if response_data:
                # 将响应数据转换为字符串存储
                if isinstance(response_data, dict):
                    record_data["response_data"] = str(response_data)
                else:
                    record_data["response_data"] = str(response_data)

            # 检查是否已存在记录
            existing = self.get_by_index(index)
            if existing:
                # 更新现有记录
                self.update_by_index(index, record_data)
            else:
                # 添加新记录
                self.add(record_data)

        except Exception as e:
            logger.error(f"记录API结果失败: {e}")

    def get_success_count(self):
        """
        获取成功记录数量
        
        Returns:
            int: 成功记录数量
        """
        try:
            records = self.list()
            return len([r for r in records if r.get("status") == "success"])
        except Exception as e:
            logger.error(f"获取成功记录数量失败: {e}")
            return 0

    def get_failed_count(self):
        """
        获取失败记录数量
        
        Returns:
            int: 失败记录数量
        """
        try:
            records = self.list()
            return len([r for r in records if r.get("status") == "failed"])
        except Exception as e:
            logger.error(f"获取失败记录数量失败: {e}")
            return 0

    def get_statistics(self):
        """
        获取统计信息

        Returns:
            dict: 统计信息
        """
        try:
            records = self.list()
            total = len(records)
            success = len([r for r in records if r.get("status") == "success"])
            failed = len([r for r in records if r.get("status") == "failed"])
            skipped = len([r for r in records if r.get("status") == "skipped"])

            # 计算分数统计
            scores = [int(r.get("score", 0)) for r in records if r.get("score") and r.get("score").isdigit()]
            avg_score = sum(scores) / len(scores) if scores else 0
            max_score = max(scores) if scores else 0
            min_score = min(scores) if scores else 0

            return {
                "total": total,
                "success": success,
                "failed": failed,
                "skipped": skipped,
                "success_rate": f"{(success/total*100):.1f}%" if total > 0 else "0%",
                "avg_score": f"{avg_score:.1f}" if avg_score > 0 else "0",
                "max_score": max_score,
                "min_score": min_score,
            }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def flush(self):
        """刷新数据"""
        try:
            self._csv.load()
        except Exception as e:
            logger.error(f"刷新Funquiz数据失败, error={str(e)}")
