from typing import Optional, Dict, Any
from web3 import Web3
from loguru import logger
import time


class BigintQuestMinter:
    """Bigint Quest NFT Minter 工具类

    用于在 https://quest.bigint.co/ 进行 NFT mint 操作
    支持 4 种不同类型的 mint 和查询功能
    """

    # 完整的合约 ABI
    ABI = [
        {
            "inputs": [{"internalType": "uint256", "name": "batchSize_", "type": "uint256"}],
            "name": "mintType1",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function",
        },
        {
            "inputs": [{"internalType": "uint256", "name": "batchSize_", "type": "uint256"}],
            "name": "mintType2",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function",
        },
        {
            "inputs": [{"internalType": "uint256", "name": "batchSize_", "type": "uint256"}],
            "name": "mintType3",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function",
        },
        {
            "inputs": [{"internalType": "uint256", "name": "batchSize_", "type": "uint256"}],
            "name": "mintType4",
            "outputs": [],
            "stateMutability": "payable",
            "type": "function",
        },
        {
            "inputs": [
                {"internalType": "address", "name": "user", "type": "address"},
                {"internalType": "uint8", "name": "metadataType", "type": "uint8"},
            ],
            "name": "hasMintedType",
            "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
            "stateMutability": "view",
            "type": "function",
        },
    ]

    def __init__(
        self,
        contract_address: str,
        rpc_url: str,
        private_key: str,
        proxy: Optional[str] = None,
        user_agent: Optional[str] = None,
    ):
        """初始化 Bigint Quest Minter

        Args:
            contract_address: NFT 合约地址
            rpc_url: RPC 节点地址
            private_key: 私钥
            proxy: 代理设置
            user_agent: User-Agent
        """
        self.contract_address = Web3.to_checksum_address(contract_address)
        self.rpc_url = rpc_url
        self.private_key = private_key
        self.proxy = proxy
        self.user_agent = user_agent

        # 初始化 Web3 连接
        self.web3 = self._init_web3()
        self.account = self.web3.eth.account.from_key(private_key)
        self.from_address = self.account.address

        # 初始化合约实例
        self.contract = self.web3.eth.contract(address=self.contract_address, abi=self.ABI)

        # 计算函数选择器
        self._function_selectors = self._calculate_function_selectors()

        logger.info(f"BigintQuestMinter 初始化完成，合约地址: {self.contract_address}")
        logger.info(f"钱包地址: {self.from_address}")

    def _init_web3(self) -> Web3:
        """初始化 Web3 连接"""
        try:
            # 构建请求参数
            request_kwargs = {}
            if self.proxy:
                request_kwargs["proxies"] = {"http": self.proxy, "https": self.proxy}
            if self.user_agent:
                request_kwargs["headers"] = {"User-Agent": self.user_agent}

            # 创建 Web3 实例
            web3 = Web3(Web3.HTTPProvider(self.rpc_url, request_kwargs=request_kwargs))

            if not web3.is_connected():
                raise Exception(f"无法连接到 RPC: {self.rpc_url}")

            logger.info(f"Web3 连接成功，Chain ID: {web3.eth.chain_id}")
            return web3

        except Exception as e:
            logger.error(f"Web3 连接失败: {str(e)}")
            raise

    def _calculate_function_selectors(self) -> Dict[str, str]:
        """计算函数选择器"""
        selectors = {}

        # 计算每个函数的选择器
        function_signatures = {
            "mintType1": "mintType1(uint256)",
            "mintType2": "mintType2(uint256)",
            "mintType3": "mintType3(uint256)",
            "mintType4": "mintType4(uint256)",
            "hasMintedType": "hasMintedType(address,uint8)",
        }

        for name, signature in function_signatures.items():
            selector = Web3.keccak(text=signature)[:4].hex()
            selectors[name] = selector
            logger.debug(f"{name} 函数选择器: {selector}")

        return selectors

    def get_balance(self) -> float:
        """获取钱包余额 (ETH)"""
        try:
            balance_wei = self.web3.eth.get_balance(self.from_address)
            balance_eth = self.web3.from_wei(balance_wei, "ether")
            return float(balance_eth)
        except Exception as e:
            logger.error(f"获取余额失败: {str(e)}")
            return 0.0

    def _send_transaction(self, function_name: str, *args, value_in_wei: int = 0) -> bool:
        """发送交易的通用方法

        Args:
            function_name: 合约函数名
            *args: 函数参数
            value_in_wei: 支付金额 (wei)

        Returns:
            bool: 是否成功
        """
        try:
            # 获取合约函数
            contract_function = getattr(self.contract.functions, function_name)

            # 构建交易
            transaction = contract_function(*args).build_transaction(
                {
                    "from": self.from_address,
                    "value": value_in_wei,
                    "nonce": self.web3.eth.get_transaction_count(self.from_address),
                    "gasPrice": self.web3.eth.gas_price,
                }
            )

            # 估算 gas
            try:
                estimated_gas = self.web3.eth.estimate_gas(transaction)
                transaction["gas"] = int(estimated_gas * 1.2)  # 增加 20% 缓冲
            except Exception as e:
                logger.warning(f"Gas 估算失败，使用默认值: {str(e)}")
                transaction["gas"] = 200000

            # 签名交易
            signed_txn = self.web3.eth.account.sign_transaction(transaction, self.private_key)

            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_txn.raw_transaction)
            logger.info(f"交易已发送: {tx_hash.hex()}")

            # 等待交易确认
            receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)

            if receipt.status == 1:
                logger.success(f"交易成功: {tx_hash.hex()}")
                logger.info(f"Gas 使用: {receipt.gasUsed}")
                return True
            else:
                logger.error(f"交易失败: {tx_hash.hex()}")
                return False

        except Exception as e:
            logger.error(f"发送交易失败: {str(e)}")
            return False

    def mint_type1(self, batch_size: int, value_in_wei: int = 0) -> bool:
        """Mint Type 1 NFT

        Args:
            batch_size: 批量大小
            value_in_wei: 支付金额 (wei)

        Returns:
            bool: 是否成功
        """
        logger.info(f"开始 mint Type 1, 批量大小: {batch_size}")
        return self._send_transaction("mintType1", batch_size, value_in_wei=value_in_wei)

    def mint_type2(self, batch_size: int, value_in_wei: int = 0) -> bool:
        """Mint Type 2 NFT"""
        logger.info(f"开始 mint Type 2, 批量大小: {batch_size}")
        return self._send_transaction("mintType2", batch_size, value_in_wei=value_in_wei)

    def mint_type3(self, batch_size: int, value_in_wei: int = 0) -> bool:
        """Mint Type 3 NFT"""
        logger.info(f"开始 mint Type 3, 批量大小: {batch_size}")
        return self._send_transaction("mintType3", batch_size, value_in_wei=value_in_wei)

    def mint_type4(self, batch_size: int, value_in_wei: int = 0) -> bool:
        """Mint Type 4 NFT"""
        logger.info(f"开始 mint Type 4, 批量大小: {batch_size}")
        return self._send_transaction("mintType4", batch_size, value_in_wei=value_in_wei)

    def has_minted_type(self, user_address: str, metadata_type: int) -> Optional[bool]:
        """查询用户是否已经 mint 了指定类型的 NFT

        Args:
            user_address: 用户地址
            metadata_type: 元数据类型 (1-4)

        Returns:
            Optional[bool]: True=已mint, False=未mint, None=查询失败
        """
        try:
            logger.info(f"查询地址 {user_address} 的 Type {metadata_type} mint 状态")

            result = self.contract.functions.hasMintedType(Web3.to_checksum_address(user_address), metadata_type).call()

            logger.info(f"地址 {user_address} Type {metadata_type} mint 状态: {result}")
            return result

        except Exception as e:
            logger.error(f"查询 mint 状态异常: {str(e)}")
            return None

    def mint_all_types(
        self, batch_size: int, value_per_mint: int = 0, delay_between_mints: float = 2.0
    ) -> Dict[str, bool]:
        """批量 mint 所有类型的 NFT

        Args:
            batch_size: 每种类型的批量大小
            value_per_mint: 每次 mint 的支付金额 (wei)
            delay_between_mints: mint 之间的延迟时间 (秒)

        Returns:
            Dict[str, bool]: 每种类型的 mint 结果
        """
        results = {}

        mint_methods = [
            ("Type1", self.mint_type1),
            ("Type2", self.mint_type2),
            ("Type3", self.mint_type3),
            ("Type4", self.mint_type4),
        ]

        for i, (type_name, mint_method) in enumerate(mint_methods):
            try:
                logger.info(f"开始 mint {type_name} ({i+1}/{len(mint_methods)})")
                success = mint_method(batch_size, value_per_mint)
                results[type_name] = success

                if success:
                    logger.success(f"{type_name} mint 成功")
                else:
                    logger.error(f"{type_name} mint 失败")

                # 在 mint 之间添加延迟，避免 nonce 冲突
                if i < len(mint_methods) - 1:  # 最后一个不需要延迟
                    logger.info(f"等待 {delay_between_mints} 秒后继续下一个 mint...")
                    time.sleep(delay_between_mints)

            except Exception as e:
                logger.error(f"{type_name} mint 异常: {str(e)}")
                results[type_name] = False

        return results

    def check_all_mint_status(self, user_address: str) -> Dict[str, Optional[bool]]:
        """检查用户所有类型的 mint 状态

        Args:
            user_address: 用户地址

        Returns:
            Dict[str, Optional[bool]]: 每种类型的 mint 状态
        """
        results = {}

        for i in range(1, 5):
            type_name = f"Type{i}"
            try:
                status = self.has_minted_type(user_address, i)
                results[type_name] = status
            except Exception as e:
                logger.error(f"检查 {type_name} 状态异常: {str(e)}")
                results[type_name] = None

        return results

    def mint_unminted_types(
        self, user_address: str, batch_size: int, value_per_mint: int = 0, delay_between_mints: float = 2.0
    ) -> Dict[str, bool]:
        """只 mint 用户尚未 mint 的类型

        Args:
            user_address: 用户地址
            batch_size: 批量大小
            value_per_mint: 每次 mint 的支付金额 (wei)
            delay_between_mints: mint 之间的延迟时间 (秒)

        Returns:
            Dict[str, bool]: mint 结果
        """
        # 先检查当前状态
        current_status = self.check_all_mint_status(user_address)
        logger.info(f"当前 mint 状态: {current_status}")

        results = {}
        mint_methods = {
            "Type1": self.mint_type1,
            "Type2": self.mint_type2,
            "Type3": self.mint_type3,
            "Type4": self.mint_type4,
        }

        # 筛选出需要 mint 的类型
        to_mint = []
        for type_name, mint_method in mint_methods.items():
            if current_status.get(type_name) is True:
                logger.info(f"{type_name} 已经 mint 过，跳过")
                results[type_name] = True
            else:
                to_mint.append((type_name, mint_method))

        # 执行 mint
        for i, (type_name, mint_method) in enumerate(to_mint):
            try:
                logger.info(f"开始 mint {type_name} ({i+1}/{len(to_mint)})")
                success = mint_method(batch_size, value_per_mint)
                results[type_name] = success

                if success:
                    logger.success(f"{type_name} mint 成功")
                else:
                    logger.error(f"{type_name} mint 失败")

                # 在 mint 之间添加延迟
                if i < len(to_mint) - 1:
                    logger.info(f"等待 {delay_between_mints} 秒后继续下一个 mint...")
                    time.sleep(delay_between_mints)

            except Exception as e:
                logger.error(f"{type_name} mint 异常: {str(e)}")
                results[type_name] = False

        return results


# 使用示例
if __name__ == "__main__":
    pass
