import csv
import datetime
import json
import os
import random
import re
import time
from threading import Lock
from time import sleep
from src.utils.data_utils import DataUtil

import click
from faker import Faker
from loguru import logger
from retry import retry
from src.browsers.config import get_browser_extension_id
from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.socials import X
from src.utils.common import generate_username, get_project_root_path, parse_indices
from src.utils.element_util import get_element, get_elements, get_frame
from src.utils.hhcsv import HHCSV
from src.utils.password import generate_pwd
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "github.log")
logger.add(log_path, rotation="10MB", level="INFO")


PROXY_URL = os.getenv("PROXY_URL")
ETH_RPC_URL = os.getenv("ETH_RPC_URL")


class GitHub:
    # 创建类级别的锁
    _csv_lock = Lock()

    def __init__(self, browser_type: BrowserType, id: str, data_util: DataUtil):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self) -> None:
        """
        初始化数据记录

        如果数据不存在则创建新的数据记录，包含索引、浏览器类型和地址信息。
        """
        try:
            # 检查是否已存在数据
            existing_data = self.data_util.get(self.address)
            if existing_data:
                logger.debug(f"【{self.id}】{self.address} 数据已存在，跳过初始化")
                return

            # 创建新的数据记录
            new_data = {"index": self.id, "type": self.browser_type.value, "address": self.address}
            self.data_util.add(new_data)
            logger.success(f"【{self.id}】{self.address} 数据初始化成功")

        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _get_email_verify_code(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            to_mail = proxy_email or email
            client_id = self.browser_controller.browser_config.client_id
            email_client = EmailClient(email, email_pwd)
            email_client.set_client_id(client_id)
            search_criteria = SearchCriteria(subject="Your GitHub launch code", to=to_mail)
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            # 匹配"go to "后面的sendgrid链接，直到遇到换行符
            pattern = r"Continue signing up for GitHub by entering the code below:\s*(\d+)\s*"

            # 查找匹配
            match = re.search(pattern, email["content"])
            if match:
                return match.group(1)  # 返回第一个捕获组（链接内容）
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证码失败: {e}")
            return None

    def _input_field(self, tab, xpath, value, sleep_time=1):
        """统一处理输入框操作"""

        def _handle_input():
            input_element = get_element(tab, xpath, timeout=3)
            if not input_element:
                raise Exception(f"未找到输入框: {xpath}")
            input_element.clear(True)
            input_element.input(value)
            sleep(sleep_time)
            return True

        try:
            return _handle_input()
        except Exception as e:
            logger.error(f"【{self.id}】输入字段失败 {xpath}: {str(e)}")
            return False

    def _click_element(self, tab, xpath, timeout=3, sleep_time=3):
        """统一处理点击操作"""
        try:
            element = get_element(tab, xpath, timeout)
            if not element:
                raise Exception(f"未找到元素: {xpath}")
            element.click()
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】点击元素失败 {xpath}: {str(e)}")
            return False

    def login(self):
        """登录 github 开发者账号."""
        address = self.browser_controller.browser_config.evm_address
        register = self.data_util.get(address)
        tab = self.page.new_tab("https://github.com/login")
        self.page.close_tabs(tab, others=True)
        self._input_field(tab, "x://input[@id='login_field']", register.get("email"))
        self._input_field(tab, "x://input[@id='password']", register.get("password"))
        self._click_element(tab, "x://input[@name='commit']")
        logger.info(f"【{self.id}】登录完成")
        return True

    @retry(tries=2, delay=3)
    def register(self, type: str):
        """注册 github 开发者账号."""
        try:
            register = self.data_util.get(self.address)
            if register:
                if register.get("status") == "1":
                    logger.info(f"【{self.id}】钱包 {self.address} 已注册,跳过注册流程")
                    return True

            email_name = self.browser_controller.browser_config.email
            email_pwd = self.browser_controller.browser_config.email_password
            proxy_email = self.browser_controller.browser_config.proxy_email
            email = proxy_email or email_name
            password = register.get("password") or generate_pwd(10)

            tab = self.page.new_tab("https://github.com/")
            tab.wait.doc_loaded(timeout=10)
            if not try_click(tab, "x://a[normalize-space()='Sign up']", timeout=15, id=self.id):
                logger.error(f"【{self.id}】点击Sign up无响应")
                return False

            # 生成随机用户信息
            username = register.get("username") or generate_username()

            # 填写表单
            form_fields = {
                "x://input[@id='email']": email,
                "x://input[@id='password']": password,
                "x://input[@id='login']": username,
            }
            logger.info(f"【{self.id}】开始填写注册信息")
            for xpath, value in form_fields.items():
                if not self._input_field(tab, xpath, value):
                    raise Exception(f"填写表单字段失败: {xpath}")
            try_click(tab, "x://input[contains(@id,'user_signup')]", timeout=5)

            ele = get_element(tab, "x://auto-check[contains(@src, 'username')]//div[contains(@id,'input-check')]")
            if "is available" in ele.text:
                data = {"username": username, "password": password, "status": 0, "email": email}
                logger.debug(f"【{self.id}】注册信息: {data}")
                self.data_util.update(self.address, data)

            # 提交表单
            result = try_click(tab, "x://button[contains(.,'Create account') and not(@hidden)]", timeout=5)
            if not result:
                logger.error(f"{self.id} 点击提交表单按钮失败")
                return False

            sleep(8)
            # try_click(tab, "x://button[.//span[contains(text(), 'Continue')]]", timeout=3)
            # sub_ele.click.multi(2)

            # 已经到了输入验证码页面
            code_ele = tab.ele("Enter code", timeout=5)
            if code_ele:
                verify_code = self._get_email_verify_code(email_name, email_pwd, proxy_email)
                if verify_code is None:
                    try_click(tab, "x://button[normalize-space()='Resend the code']", timeout=5)
                    sleep(5)
                    verify_code = self._get_email_verify_code(email_name, email_pwd, proxy_email)
                    if verify_code is None:
                        logger.error(f"{self.id} 验证码获取失败, 请手动检查")
                        return False

                # 将验证码的每一位分别输入到对应的输入框
                for i in range(8):
                    input_text(tab, f"x://input[@id='launch-code-{i}']", verify_code[i], timeout=10)

                # result = try_click(tab, "x://span[contains(text(),'Continue')]", timeout=5)
                # if not result:
                #     logger.error(f"{self.id} 点击Continue按钮失败")
                #     return False

                if not tab.wait.url_change("https://github.com/login", timeout=120):
                    logger.error(f"{self.id} 跳转到登录页面失败")
                    return False

                if get_element(tab, "x://div[contains(text(),'Your account was created successfully')]", 10):
                    self.data_util.update(
                        self.address,
                        {"status": "1", "created_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")},
                    )
                    logger.success(f"【{self.id}】 账号注册成功")
                    self._input_field(tab, "x://input[@id='login_field']", register.get("email"))
                    self._input_field(tab, "x://input[@id='password']", register.get("password"))
                    self._click_element(tab, "x://input[@name='commit']")

                    if tab.wait.url_change("https://github.com/dashboard", timeout=30):
                        logger.success(f"{self.id} 登录成功")
                        return True

                    logger.info(f"{self.id} 登录成功")
                    return False

            if get_element(tab, "x://div[contains(text(), 'Unable to verify ')]", timeout=3):
                logger.info("cap认证不通过")
                return False

            if get_element(tab, "x://h2[contains(text(),'Verify your account')]", timeout=5):
                logger.info(f"{self.id} 开始认证")
                iframe = get_frame(
                    tab,
                    "x://iframe[contains(@src, 'octocaptcha.com')]",
                    timeout=10,
                )
                sleep(5)
                btn_ele = iframe.ele("x://button[normalize-space()='Visual puzzle']", timeout=3)
                if btn_ele:
                    btn_ele.click()

                sleep(4)
                if tab.wait.url_change("account_verifications", timeout=180):
                    verify_code = self._get_email_verify_code(email_name, email_pwd, proxy_email)
                    if verify_code is None:
                        try_click(tab, "x://button[normalize-space()='Resend the code']", timeout=5)
                        sleep(5)
                        verify_code = self._get_email_verify_code(email_name, email_pwd, proxy_email)
                    # 将验证码的每一位分别输入到对应的输入框
                    for i in range(8):
                        input_text(tab, f"x://input[@id='launch-code-{i}']", verify_code[i], timeout=10)
                    try_click(tab, "x://span[contains(text(),'Continue')]", timeout=5)
                elif tab.wait.url_change("https://github.com/signup", timeout=30):
                    logger.error(f"【{self.id}】 过验证码失败,请稍后再试")
                    raise Exception(f"【{self.id}】 过CF盾失败")
                if tab.wait.url_change("https://github.com/login", timeout=300):
                    if get_element(tab, "x://div[contains(text(),'Your account was created successfully')]", 10):
                        self.data_util.update(
                            self.address,
                            {"register": "1", "created_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")},
                        )
                        logger.success(f"【{self.id}】 账号注册成功")
                        self._input_field(tab, "x://input[@id='login_field']", register.get("email"))
                        self._input_field(tab, "x://input[@id='password']", register.get("password"))
                        self._click_element(tab, "x://input[@name='commit']")
                        logger.info(f"【{self.id}】登录完成")
                        return True
                else:
                    logger.error(f"【{self.id}】 过验证码失败盾")
                    raise Exception(f"【{self.id}】 过CF盾失败")

        except Exception as e:
            logger.error(f"【{self.id}】注册发生异常: {str(e)}")
            return False

    def fork_repository(self, repo_url: str):
        """Fork GitHub 仓库"""
        try:
            # 确保已登录
            if not self.login():
                logger.error(f"【{self.id}】登录失败，无法执行 fork 操作")
                return False

            # 打开仓库页面
            tab = self.page.new_tab(repo_url)
            self.page.close_tabs(tab, others=True)
            sleep(3)

            # 查找并点击 Fork 按钮
            fork_button_xpaths = [
                "x://button[contains(@class, 'btn') and contains(., 'Fork')]",
                "x://a[contains(@href, '/fork') and contains(., 'Fork')]",
                "x://button[@data-testid='fork-button']",
                "x://span[text()='Fork']//parent::button",
                "x://button[.//span[contains(text(), 'Fork')]]",
            ]

            fork_clicked = False
            for xpath in fork_button_xpaths:
                if self._click_element(tab, xpath, timeout=5, sleep_time=2):
                    fork_clicked = True
                    logger.info(f"【{self.id}】成功点击 Fork 按钮")
                    break

            if not fork_clicked:
                logger.error(f"【{self.id}】未找到 Fork 按钮")
                return False

            # 等待 fork 页面加载，可能需要选择组织或确认
            sleep(3)

            # 检查是否有确认按钮或选择组织的页面
            confirm_button_xpaths = [
                "x://button[contains(text(), 'Create fork')]",
                "x://button[contains(text(), 'Fork repository')]",
                "x://input[@type='submit' and contains(@value, 'Fork')]",
            ]

            for xpath in confirm_button_xpaths:
                if get_element(tab, xpath, timeout=3):
                    self._click_element(tab, xpath, timeout=3, sleep_time=2)
                    logger.info(f"【{self.id}】点击确认 Fork 按钮")
                    break

            # 等待 fork 完成，检查是否跳转到 forked 仓库
            sleep(5)
            current_url = tab.url

            # 检查是否成功 fork（URL 应该包含用户名）
            if "github.com" in current_url and current_url != repo_url:
                logger.success(f"【{self.id}】成功 fork 仓库: {repo_url}")
                return True
            else:
                logger.error(f"【{self.id}】Fork 仓库失败: {repo_url}")
                return False

        except Exception as e:
            logger.error(f"【{self.id}】Fork 仓库发生异常: {str(e)}")
            return False

    def task(self, type: str):
        try:
            self.browser_controller.window_max()
            if not self.register(type):
                logger.error(f"【{self.id}】注册失败，请手动处理")
                return False

            return True
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")


def _run_task(type, index, net):
    github = None
    try:
        if net:
            from src.utils.ip_update import IPManager

            ip_manager = IPManager(config_csv=rf"data/{type.value}.csv")
            ip_manager.check_and_update_proxy_by_id(str(index))

        data_util = _get_data_util()
        github = GitHub(type, str(index), data_util)
        extension_id = get_browser_extension_id("yescaptcha", type)
        github.browser_controller.chrome_extension_status(extension_id, True)
        github.task(type)
        github.browser_controller.chrome_extension_status(extension_id, False)
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
    finally:
        if github:
            github.browser_controller.close_page()


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-n", "--net", is_flag=True, default=False, flag_value=True, help="是否启动网络检测")
def run(type, index, workers, net):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                if net:
                    from src.utils.ip_update import IPManager

                    ip_manager = IPManager(config_csv=rf"data/{type.value}.csv")
                    ip_manager.check_and_update_proxy_by_id(str(index))
                extension_id = get_browser_extension_id("yescaptcha", type)

                data_util = _get_data_util()
                github = GitHub(type, str(index), data_util)
                github.browser_controller.chrome_extension_status(extension_id, True)
                result = github.task(type)
                github.browser_controller.chrome_extension_status(extension_id, False)
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    # faucet.page.quit()
                    # return True
                # return False
                return result
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                # faucet.page.quit()
                return False
            finally:
                if faucet:
                    faucet.page.quit()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("fork")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-n", "--net", is_flag=True, default=False, flag_value=True, help="是否启动网络检测")
def fork(type, index, workers, net):
    """Fork GitHub 仓库"""
    # 预定义的仓库地址列表
    addresses = [
        "https://github.com/TheAlgorithms/Python",
        "https://github.com/facebook/react-native",
        "https://github.com/airbnb/lottie-ios",
        "https://github.com/facebook/react",
        "https://github.com/apple/swift",
        "https://github.com/microsoft/vscode",
        "https://github.com/airbnb/javascript",
    ]

    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []
        failed_indices = []
        fork_results = {}  # 记录每个账号的 fork 结果
        github = None

        def process_fork_task(index):
            try:
                if net:
                    from src.utils.ip_update import IPManager

                    ip_manager = IPManager(config_csv=rf"data/{type.value}.csv")
                    ip_manager.check_and_update_proxy_by_id(str(index))

                extension_id = get_browser_extension_id("yescaptcha", type)
                github = GitHub(type, str(index))
                github.browser_controller.chrome_extension_status(extension_id, True)

                # 对每个地址执行 fork 操作
                account_results = []
                for repo_url in addresses:
                    try:
                        result = github.fork_repository(repo_url)
                        account_results.append({"repo": repo_url, "success": result})
                        if result:
                            logger.success(f"账号 {index} 成功 fork: {repo_url}")
                        else:
                            logger.error(f"账号 {index} fork 失败: {repo_url}")
                        sleep(2)  # 避免请求过快
                    except Exception as e:
                        logger.error(f"账号 {index} fork {repo_url} 异常: {e}")
                        account_results.append({"repo": repo_url, "success": False})

                github.browser_controller.chrome_extension_status(extension_id, False)
                fork_results[index] = account_results

                # 如果至少有一个成功，认为该账号任务成功
                if any(r["success"] for r in account_results):
                    successful_indices.append(index)
                    github.browser_controller.close_page()
                    return True
                else:
                    failed_indices.append(index)
                    github.browser_controller.close_page()
                    return False

            except Exception as e:
                logger.error(f"账号 {index} 执行 fork 任务异常: {e}")
                if "github" in locals():
                    github.browser_controller.close_page()
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"github_fork-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_fork_task, indices)

        # 输出详细结果
        logger.info(f"Fork 任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")
        logger.info(f"失败的账号列表: {failed_indices}")

        # 输出每个账号的详细 fork 结果
        for account_index, account_results in fork_results.items():
            logger.info(f"账号 {account_index} 的 fork 结果:")
            for result in account_results:
                status = "✓" if result["success"] else "✗"
                logger.info(f"  {status} {result['repo']}")

    except Exception as e:
        logger.error(f"执行 fork 任务过程出错: {e}")
        raise


CSV_FILE_NAME = "github.csv"
DATA_DIR_NAME = "github"


def _get_data_util() -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """
    data_dir = os.path.join(get_project_root_path(), "examples", DATA_DIR_NAME)
    csv_path = os.path.join(data_dir, CSV_FILE_NAME)
    return DataUtil(csv_path)


# 点击
# python3 examples/github/github.py run -t bit -i 1-10


if __name__ == "__main__":
    # cli()

    _run_task(BrowserType.CHROME, 75, False)
