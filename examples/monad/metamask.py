from time import sleep
import click
import time
import random
import re
import csv
import os
from faker import Faker
from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from loguru import logger
from web3 import Web3
from src.evm.nft_utils import NFTUtils
from src.utils.common import parse_indices, get_project_root_path
from src.utils.element_util import get_element, get_elements
from src.utils.proxies import Proxies
from src.utils.thread_executor import ThreadExecutor
from src.utils.password import generate_pwd
from src.utils.hhcsv import HHCSV
from threading import Lock

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/metamask_faucet.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")
ETH_RPC_URL = os.getenv("ETH_RPC_URL")

def get_tx_count(address: str, rpc: str=ETH_RPC_URL) -> float:
    """
    查询 Monad 链上某个地址的余额（原生代币或 ERC-20 代币）

    :param address: 需要查询的地址
    :param rpc: ETH_RPC_URL RPC URL
    :return: tx数量
    """
    request_kwargs = {
        "proxies": Proxies(PROXY_URL).get_proxies()
    }
    web3 = Web3(Web3.HTTPProvider(rpc, request_kwargs))

    if not web3.is_connected():
        raise ConnectionError("无法连接到 ETH RPC URL，请检查 RPC URL")
    checksum_address = web3.to_checksum_address(address)
    return web3.eth.get_transaction_count(checksum_address)

class FaucetMetaMask:
    # 创建类级别的锁
    _csv_lock = Lock()
    
    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self._init_csv()

    def _init_csv(self):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            data_dir = os.path.join(get_project_root_path(), "data")
            self.csv_path = os.path.join(data_dir, "metamask_developer.csv")
            with self._csv_lock:
                self.csv = HHCSV(self.csv_path, ["index","type","address", "email", "password"])
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def _check_cf_shield1(self, lasted_tab):
        for _ in range(6):
            try:
                # 1. 判断是否在CF盾页面
                if "Register | MetaMask Developer" in lasted_tab.title:
                    return True

                div_ele = lasted_tab.ele("x:(//div[@id='uATa8']/div/div | //div[@style='display: grid;']/div/div)")
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr('@id=success')
                        if success and success.states.is_displayed:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if "Register | MetaMask Developer" in lasted_tab.title:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                logger.error(f"【{self.id}】过CF盾发生异常，error={str(e)}")
        return False

    def _check_cf_shield2(self, tab, url):
        for _ in range(6):
            try:
                div_ele = tab.ele("x://div[@id='cf-turnstile']/div", timeout=5)
                if not div_ele:
                    logger.success(f"【{self.id}】 过CF盾成功")
                    return True
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr('@id=success')
                        if success and success.states.is_displayed:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if url in tab.url:
                            logger.success(f"【{self.id}】 过CF盾成功")
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                logger.error(f"【{self.id}】过CF盾发生异常，error={str(e)}")
        return False

    def _get_email_verify_link(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Verify your email",
                # from_addr="<EMAIL>",
                to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            pattern = r'href="(https://developer\.metamask\.io/verify/[^"]+)"'

            # 查找匹配
            match = re.search(pattern, email["content"])
            if match:
                return match.group(1)  # 返回第一个捕获组（链接内容）
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _input_field(self, tab, xpath, value, sleep_time=1):
        """统一处理输入框操作"""
        try:
            input_element = get_element(tab, xpath, timeout=3)
            if not input_element:
                raise Exception(f"未找到输入框: {xpath}")
            input_element.clear(True)
            input_element.input(value)
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】输入字段失败 {xpath}: {str(e)}")
            return False

    def _click_element(self, tab, xpath, timeout=3, sleep_time=3):
        """统一处理点击操作"""
        try:
            element = get_element(tab, xpath, timeout)
            if not element:
                raise Exception(f"未找到元素: {xpath}")
            element.click()
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】点击元素失败 {xpath}: {str(e)}")
            return False

    def _handle_survey(self, tab):
        """处理调查问卷"""
        try:
            # 第一页
            logger.debug(f"【{self.id}】开始选择角色")
            roles = get_elements(tab, "x://span[@data-testid='onboarding-role']//div[@role='radio']", 8)
            if roles and len(roles) > 0:
                random_role = random.choice(roles[:-1])
                random_role.click()
                sleep(1)

            logger.debug(f"【{self.id}】开始选择用途")
            usages = get_elements(tab, "x://span[@data-testid='onboarding-usage']//div[@role='radio']", 5)
            if usages and len(usages) > 0:
                random.choice(usages).click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", sleep_time=5):
                return False

            # 第二页
            logger.debug(f"【{self.id}】开始选择类目")
            categories = get_elements(tab, "x://span[@data-testid='onboarding-category']//div[@role='radio']", 5)
            if categories and len(categories) > 0:
                random_category = random.choice(categories[:-1])
                random_category.click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", timeout=5, sleep_time=10):
                return False

            # 第三页
            logger.debug(f"【{self.id}】开始选择套餐")
            tier = get_element(tab, "x:(//span[@data-testid='onboarding-tier']//div[@role='radio'])[1]", 5)
            if tier:
                tier.click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", sleep_time=10):
                return False

            return True
        except Exception as e:
            logger.error(f"【{self.id}】处理调查问卷失败: {str(e)}")
            return False

    def _get_registration(self, address):
        try:
            with self._csv_lock:
                result = self.csv.query({"address": address})
                return result[0] if result else {}
        except Exception as e:
            logger.error(f"获取注册信息时发生错误: {e}")
            return {}

    def _save_registration(self, register):
        """保存注册信息到CSV"""
        try:
            address = register.get('address')
            with self._csv_lock:
                # 先查询是否存在记录
                result = self.csv.query({"address": address})
                if not result:
                    self.csv.add_row(register)
                else:
                    criteria = {"address": address}
                    self.csv.update_row(criteria, register)
        except Exception as e:
            logger.error(f"保存注册信息时发生错误: {e}")

    def register(self, type: str):
        """注册 MetaMask 开发者账号"""
        try:
            # 读取address并检查是否已注册
            address = self.browser_controller.browser_config.evm_address
            password = generate_pwd(10)
            register = self._get_registration(address)
            if register:
                # 兼容历史遗留数据
                if not register.get("index"):
                    register["index"] = self.id
                    register["type"] = type
                    self._save_registration(register)
                if register.get('status') == '1':
                    logger.info(f"【{self.id}】钱包 {address} 已注册,跳过注册流程")
                    return True
            else:
                register['password'] = password
            email_name = self.browser_controller.browser_config.email
            email_pwd = self.browser_controller.browser_config.email_password
            proxy_email = self.browser_controller.browser_config.proxy_email
            email = proxy_email or email_name

            register['index'] = self.id
            register['type'] = type
            register['address'] = address
            register['email'] = email
            logger.info(f"【{self.id}】注册信息: {register}")
            # 打开注册页面
            tab = self.page.new_tab("https://developer.metamask.io/register")
            if not self._check_cf_shield1(tab):
                raise Exception("过CF盾失败")

            # 生成随机用户信息
            faker = Faker()
            first_name = faker.first_name()
            last_name = faker.last_name()


            # 填写表单
            form_fields = {
                "x://input[@id='first_name']": first_name,
                "x://input[@id='last_name']": last_name,
                "x://input[@id='email']": email,
                "x://input[@id='password']": register['password'],
            }
            logger.info(f"【{self.id}】开始填写注册信息")
            for xpath, value in form_fields.items():
                if not self._input_field(tab, xpath, value):
                    raise Exception(f"填写表单字段失败: {xpath}")

            # 勾选条款
            checkbox = get_element(tab, "x://input[@aria-describedby='terms-description']", 3)
            if checkbox and not checkbox.states.is_checked:
                checkbox.check()
                sleep(1)

            # 提交表单
            if not self._click_element(tab, "x://button[@data-testid='sign-up-button']"):
                raise Exception("点击注册按钮失败")
            sleep(5)

            tab.listen.start("developer.metamask.io/api/users")
            if not self._check_cf_shield2(tab, "developer.metamask.io/resendverify"):
                raise Exception("过第二次CF盾失败")
            res = tab.listen.wait(timeout=10)
            if res:
                response_data = res.response.body
                if 'email_in_use' in response_data:
                    logger.warning(f"【{self.id}】邮箱 {email} 已经注册，请直接登录")
                    register['status'] = 1
                    self._save_registration(register)
                    return True

            register['status'] = 0
            self._save_registration(register)

            if "https://developer.metamask.io/resendverify" not in tab.url:
                raise Exception("注册表单提交失败")

            # 处理邮箱验证
            verify_link = None
            for retry in range(3):  # 重试3次
                verify_link = self._get_email_verify_link(email_name, email_pwd, proxy_email)
                if verify_link:
                    break
                sleep(5)  # 等待邮件到达

            if not verify_link:
                raise Exception("未收到验证邮件")

            # 打开验证链接并完成调查
            new_tab = self.page.new_tab(verify_link)
            sleep(3)

            if not self._handle_survey(new_tab):
                raise Exception("完成调查问卷失败")

            if "https://developer.metamask.io/" in new_tab.url:
                logger.success(f"【{self.id}】注册成功, address: {address}, email: {email}, password: {password}")
                register['status'] = 1
                self._save_registration(register)
                return True
            else:
                raise Exception("注册流程未完成")

        except Exception as e:
            logger.error(f"【{self.id}】注册发生异常: {str(e)}")
            return False

    def faucet(self):
        self.browser_controller.metamask_wallet_login()
        tab = self.page.new_tab("https://docs.metamask.io/developer-tools/faucet/")

        # if get_element(tab, "x://a[@data-testid='maintenance-cta-alternative']", 3):
        #     logger.warning(f"【{self.id}】当前网站繁忙，请稍后再试")
        #     return False

        if not self.is_login(tab):
            connect_btn = get_element(tab, "x://button[@data-test-id='navbar-cta-connect-wallet']", 3)
            if connect_btn:
                connect_btn.click()
                if self.page.wait.new_tab(timeout=5):
                    self.browser_controller.metamask_wallet_connect()

                if not tab.wait.ele_deleted("x://div[@aria-label='Connect Wallet']", timeout=15):
                    raise Exception(f"【{self.id}】钱包链接失败，请手动处理")

        if not self.is_login(tab):
            logger.error(f"【{self.id}】登录失败")
            return False

        if get_element(tab, "x://a[@data-testid='maintenance-cta-alternative']", 3):
            logger.warning(f"【{self.id}】当前网站繁忙，请稍后再试")
            return False

        if not self._link(tab):
            logger.error(f"【{self.id}】绑定MetaMask开发者账号失败")
            return False
        self._faucet(tab)
        return True

    def _link(self, tab):
        address = self.browser_controller.browser_config.evm_address
        link_btn = get_element(tab, "x://button[@data-test-id='hero-cta-link-dashboard-account']", 5)

        if link_btn:
            logger.info(f"【{self.id}】开始绑定MetaMask开发者账号")
            link_btn.click()
            sleep(3)

            record = self.csv.query({"address": address})
            if not record:
                raise Exception(f"【{self.id}】 {address}未注册开发者账号，请先注册")

            form_fields = {
                "x://input[@id='email']": record[0]['email'],
                "x://input[@id='password']": record[0]['password'],
            }

            for xpath, value in form_fields.items():
                if not self._input_field(tab, xpath, value):
                    raise Exception(f"填写表单字段失败: {xpath}")

            if not self._click_element(tab, "x://button[@data-testid='auth-button']"):
                raise Exception("点击Link按钮失败")
            sleep(3)

            if not self._check_cf_shield2(tab, "docs.metamask.io/developer-tools/faucet"):
                raise Exception("过CF盾失败")
            sleep(3)
            logger.success(f"【{self.id}】绑定开发者账号成功")

        return True

    def is_login(self, tab):
        try:
            if get_element(tab, "x://button[@data-testid='navbar-account-toggle']", 5):
                return True
            return False
        except Exception as e:
            logger.error(f"【{self.id}】 {str(e)}")
            return False

    def _faucet(self, tab):
        try:
            max_retries = 3
            eles = get_elements(tab, "x://ul[@role='tablist']//li", 3)
            for index in range(len(eles)):
                retry = 1
                while retry <= max_retries:
                    eles[index].click()
                    sleep(3)
                    chain = eles[index].text
                    logger.info(f"【{self.id}】开始领取 {chain}")
                    try:
                        tab.listen.start('developer.metamask.io/api/faucets/')
                        address = self.browser_controller.browser_config.evm_address
                        input_i = get_element(tab, f"x:(//input[@type='text'])[{index+1}]", 3)
                        input_i.clear(True)
                        input_i.input(address)
                        sleep(3)

                        get_element(tab, f"x:(//button[@data-test-id='hero-cta-request-eth'])[{index+1}]", 5).click()
                        res = tab.listen.wait(timeout=10)
                        if res:
                            response_data = res.response.body
                            if isinstance(response_data, dict):
                                if 'error' in response_data:
                                    error_msg = response_data['error'].get('message', '未知错误')
                                    logger.warning(f"【{self.id}】领取 {chain} 失败: {error_msg}")
                                elif 'txnHash' in response_data:
                                    logger.success(f"【{self.id}】领取 {chain} 成功，获得 {response_data.get('value', '0')}ETH")
                            break
                        else:
                            logger.error(f"【{self.id}】未收到响应")
                    except Exception as e:
                        logger.error(f"【{self.id}】 领取 {eles[index].text} 异常: {str(e)}")
                    retry += 1
                sleep(3)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】领水异常: {str(e)}")
            return False

    def task(self, type: str):
        try:
            address = self.browser_controller.browser_config.evm_address

            if not self.register(type):
                logger.error(f"【{self.id}】注册失败，请手动处理")
                return False
            logger.info(f"【{self.id}】已有账号，开始领水")

            tx_count = get_tx_count(address)
            if tx_count < 5:
                logger.error(f"【{self.id}】tx数量过低，当前tx={tx_count}")
                try:
                    self.page.quit()
                except Exception as e:
                    logger.error(f"【{self.id}】关闭页面失败: {e}")
                return False
            logger.info(f"【{self.id}】已有账号，开始领水")

            # if result:
            #
            # else:
            #     logger.error(f"【{self.id}】领水失败")
            return self.faucet()
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】关闭页面失败: {e}")




def _run_task(type, index):
    try:
        faucet = FaucetMetaMask(type, str(index))
        faucet.task(type)
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                faucet = FaucetMetaMask(type, str(index))
                return faucet.task(type)
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
        logger.info(f"失败的账号列表: {failed_indices}")  # 输出失败的账号列表
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/metamask.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.ADS, 37)
