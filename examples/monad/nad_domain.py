import click
import random
import os
import shutil
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from time import sleep
from retry import retry
from datetime import datetime
from src.utils.thread_executor import ThreadExecutor
from src.utils.common import generate_username
from web3 import Web3

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

# 注册域名价格
MINT_DOMAIN_PRICE = 0.05
RPC_URL = "https://testnet-rpc.monad.xyz/"
DOMAIN_ABI = [
    {
        "inputs": [
            {
                "internalType": "address",
                "name": "addr",
                "type": "address"
            }
        ],
        "name": "getNamesOfAddress",
        "outputs": [
            {
                "internalType": "string[]",
                "name": "",
                "type": "string[]"
            }
        ],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {
                "internalType": "address",
                "name": "addr",
                "type": "address"
            }
        ],
        "name": "getPrimaryNameForAddress",
        "outputs": [
            {
                "internalType": "string",
                "name": "",
                "type": "string"
            }
        ],
        "stateMutability": "view",
        "type": "function"
    }
]

logger.add("logs/nad_domain.log", rotation="10MB", level="SUCCESS")

class NadDomain:
    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self._copy_original_csv()

    def _copy_original_csv(self):
        """拷贝原始CSV文件"""
        try:
            original_csv_path = os.path.join(
                os.path.dirname(__file__), "data.csv.example"
            )

            self.csv_path = os.path.join(
                os.path.dirname(__file__),
                f'votes_{datetime.now().strftime("%Y%m%d")}.csv',
            )

            if not os.path.exists(self.csv_path):
                shutil.copy(original_csv_path, self.csv_path)
        except Exception as e:
            logger.error(f"拷贝CSV文件失败: {e}")

    def _click_okx_wallet(self, lasted_tab):
        lasted_tab.ele("x://button[@data-testid='rk-wallet-option-com.okex.wallet']", timeout=10).click()
        sleep(1)
        self.browser_controller.okx_wallet_connect()


    def _check_wallet_connected(self, lasted_tab):
        img = lasted_tab.ele("x://img[contains(@alt, '0x') or contains(@alt, '.nad')]", timeout=3)
        if img:
            return True
        return False

    def _connect_wallet(self, lasted_tab):
        if self._check_wallet_connected(lasted_tab):
            logger.success(f"【{self.id}】 钱包已连接")
            return True

        try_count = 6
        for i in range(try_count):
            try:
                lasted_tab.ele("x://button[.='Connect Wallet']", timeout=5).click()
                self._click_okx_wallet(lasted_tab)
                sleep(2)
                if self._check_wallet_connected(lasted_tab):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

                continue

            except Exception as e:
                logger.error(f"【{self.id}】 连接钱包失败: {e}")
                sleep(1)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False


    def _check_domain_available(self, lasted_tab):
        # Available
        available_ele = lasted_tab.ele("Available", timeout=15)
        if not available_ele:
            registered_ele = lasted_tab.ele("Registered", timeout=3)
            if registered_ele:
                logger.error(f"【{self.id}】 域名已注册")
                return False

        return available_ele

    def w3_get_balance(self):
        web3 = Web3(Web3.HTTPProvider(RPC_URL))
        evm_address = self.browser_controller.browser_config.evm_address
        if not evm_address:
            logger.error(f"【{self.id}】 钱包地址为空")
            return 0

        balance = web3.eth.get_balance(evm_address)
        return balance / 10**18


    def _check_domain_registered(self, lasted_tab, domain_name):
        lasted_tab.get_url(f"https://app.nad.domains/{domain_name}.nad?tab=ownership")
        sleep(3)

        for i in range(15):
            input_ele = lasted_tab.ele("x://input[@name='resolved-address']", timeout=1)
            address = input_ele.value
            if address:
                return True
            sleep(1)
        
        return False

    def w3_check_domain_registered(self):
        try:
            web3 = Web3(Web3.HTTPProvider(RPC_URL))
            contract_address = web3.to_checksum_address("******************************************")

            contract = web3.eth.contract(address=contract_address, abi=DOMAIN_ABI)
            evm_address = self.browser_controller.browser_config.evm_address
            # 4. 尝试调用合约方法
            try:
                print("开始调用 getNamesOfAddress...")
                # 使用 call 的完整参数
                domains = contract.functions.getPrimaryNameForAddress(evm_address).call()

                logger.success(f"【{self.id}】 钱包地址 {evm_address} 已注册域名: {domains}")
                return domains

            except Exception as e:
                logger.error(f"【{self.id}】 合约调用其他错误: {e}")

            logger.info(f"【{self.id}】 钱包地址 {evm_address} 未注册域名")
            return False

        except Exception as e:
            logger.error(f"【{self.id}】 检查钱包地址域名注册状态失败: {e}")
            return False

    def _task_register(self, lasted_tab, domain_name):

        img = lasted_tab.ele("x://img[contains(@alt, '.nad')]", timeout=3)
        if img:
            logger.warning(f"【{self.id}】 域名已注册过, 无需重复注册...")
            return True

        try:
            input_ele = lasted_tab.ele("x://input[@type='text']", timeout=5)
            input_ele.clear(True)
            input_ele.input(domain_name)

            # 检查域名是否可用
            available_ele = self._check_domain_available(lasted_tab)
            if not available_ele:
                logger.warning(f"【{self.id}】 域名已注册")

                # 如果域名已注册，则尝试10次
                for i in range(10):
                    current_domain = f"{domain_name}{i}"
                    input_ele.clear(True)
                    input_ele.input(current_domain)
                    _available_ele = self._check_domain_available(lasted_tab)
                    if _available_ele:
                        available_ele = _available_ele
                        break
                    sleep(1)

            if available_ele:
                available_ele.click()

            # 点击注册
            lasted_tab.ele("x://button[.='Register']", timeout=15).click()
            sleep(1)

            self.browser_controller.okx_wallet_sign()
            sleep(1)
            if domains := self.w3_check_domain_registered():
                if domains == f"{domain_name}.nad":
                    logger.success(f"【{self.id}】 域名注册成功 {domains}")
                    return True

            logger.error(f"【{self.id}】 域名注册失败")
            return False
        except Exception as e:
            logger.error(f"【{self.id}】 输入域名失败: {e}")
            return False



    @retry(tries=3, delay=1)
    def register(self):
        try:
            # 0. 判断金额是否足够 
            balance = self.w3_get_balance()
            if balance < MINT_DOMAIN_PRICE:
                logger.error(f"【{self.id}】 余额少于{MINT_DOMAIN_PRICE}")
                return True

            # 1. 判断是否已注册域名
            if domains := self.w3_check_domain_registered():
                logger.warning(f"【{self.id}】 已注册域名 {domains}, 无需重复注册...")
                return True

            # 2. 初始化浏览器
            self.browser_controller.okx_wallet_login()
            lasted_tab = self.page.new_tab("https://app.nad.domains/")
            sleep(10)

            # 3. 连接钱包
            result = self._connect_wallet(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                return True

            # 4. 点击注册
            domain_name = generate_username(5, 0)
            result = self._task_register(lasted_tab, domain_name)
            if not result:
                logger.error(f"【{self.id}】 域名注册失败")
                return False

            return True

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")

 

def _run_task(type, index):
    try:
        browser = NadDomain(type, str(index))
        browser.register()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("r")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def register(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(id):
            try:
                browser = NadDomain(type, str(id))
                return browser.register()
            except Exception as e:
                logger.error(f"账号 {id} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"NadDomain-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise




# 点击注册
# python3 examples/monad/nad_domain.py r -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.BIT, "1")

 
 
