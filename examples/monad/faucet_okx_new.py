import io
import json
import os
import random
import string
from datetime import datetime
from time import sleep

import click
import requests
from DrissionPage.common import Actions
from loguru import logger
from PIL import Image
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.operations import try_click
from src.controllers import BrowserController
from src.utils.blocker import Blocker
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.mouse_trajectory import humanMouse
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


PROXY_URL = os.getenv("PROXY_URL")


def get_kookeey_url():
    random_session = "".join(random.choices(string.digits, k=8))
    proxy_url = PROXY_URL.replace("*", random_session)
    return proxy_url


def get_proxy():
    for _ in range(10):
        proxy = get_kookeey_url()
        proxies = {"http": f"http://{proxy}", "https": f"http://{proxy}"}
        try:
            ip_check_url = "https://ipinfo.io/json"
            response = requests.get(ip_check_url, proxies=proxies, timeout=10)
            data = response.json()
            logger.info(f"获取代理成功: IP: {data.get('ip')}, 国家: {data.get('country')}, 地区: {data.get('region')}")
            break
        except Exception as e:
            logger.error(f"获取代理失败: {str(e)}")
        else:
            raise Exception("所有代理测试失败，终止请求！")
    return proxies


class FaucetOKX:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.address = self.browser_controller.browser_config.evm_address
        self.blocker = None
        self.receive_result = False
        data_dir = os.path.join(get_project_root_path(), "examples", "monad")
        self._csv_path = os.path.join(
            data_dir,
            "faucet.csv",
        )
        self.data_util = DataUtil(self._csv_path)
        self._init_data()
        logger.info(f"Initialized FaucetOKX with ID: {self.id} and Browser Type: {self.browser_type}")

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _connect_wallet(self, lasted_tab):
        try_count = 6
        for i in range(try_count):
            connect_wallet_button = lasted_tab.ele(
                "x://button[.//span[contains(text(), '连接钱包') or contains(text(), 'Connect wallet')]]",
                timeout=5,
            )
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele("x://button[.//span[text()='连接' or text()='Connect']]").click()
                sleep(2)
                self.browser_controller.okx_wallet_connect()
                return True

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def make_request(self, request_info):
        """
        发起请求，支持代理
        Args:
            request_info: 请求信息
        """
        url = request_info["request"]["url"]
        method = request_info["request"]["method"]
        headers = request_info["request"]["headers"]
        data = request_info.get("request").get("postData")
        for _ in range(10):
            proxies = get_proxy()
            try:
                response = requests.request(
                    method=method, url=url, data=data, headers=headers, proxies=proxies, timeout=30
                )

                logger.info(f"请求状态码: {response.status_code}")
                if "IP" in response.text:
                    logger.error("请求失败: IP限制, 重试")
                    continue

                response_data = response.json()
                if response_data.get("code") == 0:
                    self.receive_result = True
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    logger.success(f"【{self.id}】 {self.address} 领取成功, 领取时间: {current_time}")
                    self.data_util.update(self.address, {"okx_last_time": current_time})
                    self.browser_controller.close_page()
                    return True

                return False
            except Exception as e:
                logger.error(f"请求失败: {e}")
                continue

    def _check_geetest(self, lasted_tab):
        for index, packet in enumerate(lasted_tab.listen.steps()):
            url = packet.request.url
            body = packet.response.body

            if "https://gcaptcha4.geetest.com/verify" in url:
                try:
                    # Handle JSONP response (extract JSON from function call)
                    if body.startswith("geetest_"):
                        # Extract the JSON part from JSONP response using regex
                        response_data = json.loads(body.split("(", 1)[1].rstrip(")"))
                    else:
                        return False
                    # Check for geetest response format
                    if "status" in response_data and "data" in response_data and "result" in response_data["data"]:
                        if response_data["status"] == "success" and response_data["data"]["result"] == "success":
                            logger.success(f"【{self.id}】 验证码验证成功")
                            continue
                        else:
                            logger.error(f"【{self.id}】 验证码验证失败: {response_data['data']['result']}")
                            raise
                except json.JSONDecodeError:
                    logger.warning("无法解析JSON响应")

                except json.JSONDecodeError:
                    logger.warning("无法解析JSON响应")

        return False

    def _check_dialog(self, lasted_tab):
        dialog_ele = lasted_tab.ele("x://*[@data-testid='okd-dialog-confirm-btn']", timeout=5)
        if dialog_ele:
            dialog_ele.click()
            return True
        return False

    def _check_connect_wallet(self, lasted_tab):
        if lasted_tab.ele(
            "x://button[.='连接钱包' or .='Connect wallet']",
            timeout=5,
        ):
            self._connect_wallet(lasted_tab)
            return True
        return False

    def _check_can_receive(self, lasted_tab):
        res = lasted_tab.listen.wait(timeout=30)
        if not res:
            logger.error(f"【{self.id}】 领取失败，无法领取")
            return False

        body = json.loads(res.response.raw_body)
        if body.get("code") != 0:
            logger.error(f"【{self.id}】 领取失败，无法领取")
            return False

        data = body.get("data")
        can_receive = data.get("canReceive")
        wallet_asset_enough = data.get("walletAssetEnough")
        if can_receive and wallet_asset_enough:
            logger.success(f"【{self.id}】 可以领取，剩余领取次数{data.get('remainReceiveNum')}")
            return True

        if not wallet_asset_enough:
            logger.warning(f"【{self.id}】 余额不足10U")
            self.browser_controller.close_page()
            return False

        next_receive_time = data.get("nextReceiveTime")
        if wallet_asset_enough and next_receive_time:
            from datetime import datetime

            seconds = next_receive_time / 1000
            dt = datetime.fromtimestamp(seconds)
            next_receive_time = dt.strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"【{self.id}】 下次领取时间: {next_receive_time}")
            self.data_util.update(self.address, {"okx_last_time": next_receive_time})
            self.browser_controller.close_page()
            return False

        return False

    def _check_task_complete(self, lasted_tab):
        # 检查是否存在领取按钮
        if lasted_tab.ele("x://span[text()='余额不足' or text()='Insufficient balance']", timeout=5):
            logger.warning(f"【{self.id}】 钱包余额不足少于10USD，请充值")
            return False

        # 检查是否存在领取按钮
        eles = lasted_tab.eles("x://button[@class='okui-plain-button']", timeout=5)
        for ele in eles:
            tab = ele.parent().click.for_new_tab()
            sleep(3)
            tab.close()
            ele.click()
            sleep(3)

        return True

    def execute_task(self):
        try:
            self.browser_controller.okx_wallet_login()

            # 开始监听是否可以领取
            lasted_tab = self.page.latest_tab
            lasted_tab.listen.start("https://web3.okx.com/priapi/v2/wallet/faucet/checkReceiveQualification")
            lasted_tab.get("https://www.okx.com/zh-hans/web3/faucet/monad?id=66")

            # 最大化窗口
            self.browser_controller.window_max()

            # 检查是否存在弹窗
            self._check_dialog(lasted_tab)

            # 连接钱包
            self._check_connect_wallet(lasted_tab)

            # 检查是否可以领取
            if not self._check_can_receive(lasted_tab):
                logger.error(f"【{self.id}】 领取失败，无法领取")
                return False

            # 检查是否完成任务
            if not self._check_task_complete(lasted_tab):
                logger.error(f"【{self.id}】 领取失败，无法领取")
                return False

            # 点击领取按钮
            lasted_tab.listen.start("https://static.geetest.com/captcha_v4/d2ce0cc595/slide")
            try_click(lasted_tab, "x://button[.='领取' or .='Claim']")

            result = self._get_captcha_image(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 滑块图片获取失败")
                return False

            self.blocker = Blocker(lasted_tab)
            self.blocker.block_api(
                ["https://web3.okx.com/priapi/v2/wallet/faucet/receive"], on_intercept=self._on_intercept
            )

            self.get_and_move_slider(lasted_tab)

            # 等待领取结果
            for _ in range(30):
                if not self.receive_result:
                    sleep(1)
                else:
                    break

            return True

        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(e)

    def _get_captcha_image(self, lasted_tab):
        res = lasted_tab.listen.wait(timeout=180, count=2)
        if not res:
            return False

        success_count = 0
        for index, packet in enumerate(res):
            body = packet.response.body
            png_dir = "examples/monad/png"
            if not os.path.exists(png_dir):
                os.makedirs(png_dir)
                logger.info(f"Created directory: {png_dir}")

            if packet.response.headers.get("Content-Type") == "image/png":
                with open(
                    os.path.join("examples", "monad", "png", f"{self.id}_{index}.png"),
                    "wb",
                ) as f:
                    f.write(body)
                success_count += 1
            else:
                logger.warning("Received non-PNG image data.")
                raise Exception("Received non-PNG image data.")

        return success_count == 2

    def _on_intercept(self, request_info):
        logger.info(f"拦截API请求: {request_info['request']['url']}")
        # 停止拦截
        if self.blocker:
            self.blocker.stop()

        self.make_request(request_info)

    @retry(tries=5, delay=1)
    def get_and_move_slider(self, lasted_tab):
        logger.info("开始图像识别")
        import ddddocr

        det = ddddocr.DdddOcr(det=False, ocr=False)

        path_1 = os.path.join("examples", "monad", "png", f"{self.id}_1.png")
        with open(path_1, "rb") as f:
            target_bytes = f.read()

        path_0 = os.path.join("examples", "monad", "png", f"{self.id}_0.png")
        with open(path_0, "rb") as f:
            background_bytes = f.read()

        if not target_bytes or not background_bytes:
            logger.error(f"【{self.id}】 滑块图片获取失败")
            return False

        target_img = Image.open(io.BytesIO(target_bytes))
        bg_img = Image.open(io.BytesIO(background_bytes))

        # Compare sizes and assign accordingly
        if target_img.size > bg_img.size:
            background_bytes, target_bytes = target_bytes, background_bytes

        res = det.slide_match(target_bytes, background_bytes, simple_target=True)

        if res["target"][0] == 0:
            raise Exception("image not found, triggering retry.")

        lasted_tab.listen.start(
            [
                # "https://web3.okx.com/priapi/v2/wallet/faucet/receive",
                "https://gcaptcha4.geetest.com/verify",
            ]
        )

        slider = get_element(lasted_tab, "x://div[contains(@class, 'geetest_btn')]")
        ac = Actions(lasted_tab)
        if slider.tag:
            logger.info(f"【{self.id}】 开始滑动滑块{slider.tag}")
            ac.move_to(slider, duration=0.5)
            ac.move_to(slider, duration=0.5).right(3).hold()
            ac.wait(0.4)
            prefix = random.uniform(36, 38)

            mouse = humanMouse().getRandomTrackSpacingArray((res["target"][0] + res["target"][2]) / 2 - prefix, 10, 10)
            for row in mouse:
                x, y = row  # 解包每一行的值
                ac.move(offset_x=x, offset_y=y, duration=random.uniform(0.14, 0.19))
            ac.release()

            sleep(3)

            self._check_geetest(lasted_tab)
            return True
        else:
            logger.error("找不到滑块")

    def faucet(self):
        """直接运行FaucetOKX任务。"""
        logger.info(f"直接运行任务 for {self.id} with browser type {self.browser_type}")
        return self.execute_task()  # 调用现有的执行任务方法


@click.group()
def cli():
    """CLI for FaucetOKX operations."""
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,  # 根据需要设置默认值
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    """Run the faucet tasks."""
    try:
        successful_indices = []
        failed_indices = []
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            faucet = FaucetOKX(type, str(index))
            result = faucet.execute_task()
            if result:
                successful_indices.append(index)
                faucet.browser_controller.close_page()
            return result

        executor = ThreadExecutor(
            workers=workers,
            timeout=2 * 3600,
            retries=3,
            interval=10,
            task_name=f"faucet_okx-{type}",
            raise_exception=False,
        )

        results = executor.run_batch(process_task, indices)

        failed_indices = list(set(indices) - set(successful_indices))
        failed_indices = [str(index).strip() for index in failed_indices]
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")
        logger.info(f"失败的账号列表: {failed_indices}")

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# python3 examples\monad\faucet_okx.py run -t bit -i 2-3
if __name__ == "__main__":
    cli()
