import click
import random
from retry import retry
from loguru import logger
from src.browsers import BrowserType, BROWSER_TYPES
from src.utils.common import parse_indices
from time import sleep
from src.utils.thread_executor import ThreadExecutor
from src.utils.hhcsv import HHCSV
from src.utils.browser_config import BrowserConfigInstance
import requests
import os
import string
import json
from web3 import Web3
from config import DEFAULT_BROWSER_TYPE

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/monad_faucet.log", rotation="10MB", level="SUCCESS")


NOCAPTCHA_KEY = os.getenv("NOCAPTCHA_KEY")
NOCAPTCHA_DEV_CODE = os.getenv("NOCAPTCHA_DEV_CODE")
PROXY_URL = os.getenv("PROXY_URL")
MONAD_RPC = os.getenv("MONAD_RPC")
ETH_RPC_URL = os.getenv("ETH_RPC_URL")
def w3_get_balance(address: str, rpc: str=MONAD_RPC, token_contract: str = None) -> float:
    """
    查询 Monad 链上某个地址的余额（原生代币或 ERC-20 代币）

    :param address: 需要查询的地址
    :param rpc: Monad RPC URL
    :param token_contract: 代币合约地址（可选，不传则查询原生代币）
    :return: 余额（十进制格式）
    """
    web3 = Web3(Web3.HTTPProvider(rpc))

    if not web3.is_connected():
        raise ConnectionError("无法连接到 Monad RPC，请检查 RPC URL")

    if token_contract:
        # 查询 ERC-20 代币余额
        erc20_abi = [
            {
                "constant": True,
                "inputs": [{"name": "owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]
        contract = web3.eth.contract(address=web3.to_checksum_address(token_contract), abi=erc20_abi)

        # 获取代币余额
        balance_wei = contract.functions.balanceOf(web3.to_checksum_address(address)).call()
        # 获取代币小数位数
        decimals = contract.functions.decimals().call()
        # 转换成十进制
        balance = balance_wei / (10 ** decimals)
    else:
        # 查询原生代币余额
        balance_wei = web3.eth.get_balance(web3.to_checksum_address(address))
        balance = web3.from_wei(balance_wei, 'ether')
    return float(balance)


def get_kookeey_url():
    random_session = ''.join(random.choices(string.digits, k=8))
    proxy_url = PROXY_URL.replace("*", random_session)
    return proxy_url

def get_proxy():
    for _ in range(10):
        # FIXME: 这里可以获取其他的代理，自行切换
        proxy = get_kookeey_url()
        proxies = {
            "http": f"http://{proxy}",
            "https": f"http://{proxy}"
        }
        # try:
        #     ip_check_url = "https://ipinfo.io/json"
        #     response = requests.get(ip_check_url, proxies=proxies, timeout=10)
        #     data = response.json()
        #     logger.info(
        #         f"获取代理成功: IP: {data.get('ip')}, 国家: {data.get('country')}, 地区: {data.get('region')}")
        #     break
        # except Exception as e:
        #     logger.error(f"获取代理失败: {str(e)}")
        # else:
        #     raise Exception("所有代理测试失败，终止请求！")
    return proxies



class Faucet(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)
        

    def claim(self, address):
        if not address:
            raise Exception("address 为空")

        # 获取apr的claim信息
        url = f"https://faucet-api.apr.io/api/info?address={address}"

        # 定义请求头
        headers = {
            "accept": "*/*",
            "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "Referer": "https://stake.apr.io/",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }

        try:
            # 发送 GET 请求
            response = requests.get(url, headers=headers, proxies=get_proxy(), timeout=30)  # 添加超时设置
            response.raise_for_status()  # 检查响应状态
            return response.json()
        except requests.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            return None

    def claim_with_retry(self, evm_address,  max_retries=99, wait_time=3600):
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试第 {attempt + 1} 次认领 {evm_address}")
                response = self.claim(evm_address)

                if response is None:
                    logger.error(f"{evm_address} 请求失败，等待 {wait_time // 60} 分钟后重试...")
                    sleep(1)
                    continue

                # 检查响应中的 rejectMessage
                if 'rejectMessage' in response:
                        logger.warning(f"{response['rejectMessage']}")
                        sleep(1)
                        continue

                # 如果没有 rejectMessage，说明可能成功
                logger.info(f"认领成功: {response}")
                return response

            except Exception as e:
                logger.error(f"执行过程出错: {str(e)}")
                sleep(wait_time)

        logger.error(f"{evm_address} 达到最大重试次数 {max_retries}，无法成功认领。")
        return None


    @retry(tries=5, delay=1)
    def task(self) -> bool:
        evm_address = self.browser_config.evm_address

        balance_evm = w3_get_balance(evm_address, rpc=ETH_RPC_URL)
        if balance_evm < 0.01:
            logger.info(f"{evm_address} 余额不足0.01 ETH")
            return True

        # balance_before = w3_get_balance(evm_address)
        
        response = self.claim_with_retry(evm_address)
        if response is None:
            logger.error(f"{evm_address} 领取失败")
            return False
        
        logger.info(f"{evm_address} 领取成功 {response}")
        
        # sleep(120)
        # balance_after = w3_get_balance(evm_address)
        # logger.info(f"🎉 领水之前 {evm_address} 余额: {balance_before} MON")
        # logger.info(f"🎉 领水之后 {evm_address} 余额: {balance_after} MON")

        # if balance_before == balance_after:
        #     logger.error(f"🚫{evm_address} 领取失败")
        #     raise Exception(f"🚫{evm_address} 领取失败")

        return True

def _faucet_task(type, index):
    try:
        Faucet(type, index).task()
    except Exception as e:
        logger.error(f"账号 {index} 执行任务异常: {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            _faucet_task(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Monad_DC-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


if __name__ == "__main__":
    # cli()
    _faucet_task(BrowserType.ADS, "1")
   

    # result = asyncio.run(nocaptcha.get_cloudflare_token())
    # print(f"Cloudflare Token Result: {result}")
    # print(get_kookeey_proxy())
