from time import sleep
import click
import time
import random

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.controllers import Browser<PERSON>ontroller
from src.enums.browsers_enums import BrowserType
from loguru import logger

from src.evm.nft_utils import NFTUtils
from src.utils.common import parse_indices, get_project_root_path
from src.utils.element_util import get_element, get_elements
from src.utils.thread_executor import ThreadExecutor
from config import PROXY_URL

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/emmy.log", rotation="10MB", level="SUCCESS")

EMMY_ON_MONAD_ADDRESS = "0x2a0d96bc211EC8Cdc61c92ba6204BF2DF228CAbA"
MONAD_RPC = "https://testnet-rpc.monad.xyz"
class EmmyOnMonad:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.url = 'https://magiceden.io/launchpad/monad-testnet/emmy_on_monad'

    def _is_login(self, lasted_tab):
        return True if get_element(lasted_tab, "x://div[@data-test-id='wallet-balance']", 5) else False


    def _connect_wallet(self, lasted_tab):
        try_count = 6
        for i in range(try_count):
            logger.info(f"【{self.id}】钱包进行第 {i+1}/{try_count}次连接")

            if self._is_login(lasted_tab):
                logger.success(f"【{self.id}】 钱包已连接")
                return True

            connect_btn = get_element(
                lasted_tab,
                "x:(//div[@id='content']//div[@class='tw-flex tw-items-center tw-justify-between tw-gap-x-3']/button)[2]",
                5
            )

            if connect_btn:
                connect_btn.click()
                sleep(2)

            view_all = lasted_tab("@id=dynamic-modal").child(1).sr("t:div") \
                .ele("x://button[@data-testid='ListTile']//following-sibling::button")
            if view_all:
                view_all.click()
                sleep(2)

            okx_wallet = lasted_tab("@id=dynamic-modal").child(1).sr("t:div") \
                .ele("x://img[@data-testid='wallet-icon-okxwallet']/parent::button")
            if okx_wallet:
                okx_wallet.click()
                sleep(2)

            ethereum = lasted_tab("@id=dynamic-modal").child(1).sr("t:div") \
                .ele("x://img[@data-testid='iconic-ethereum']/parent::button")
            if ethereum:
                ethereum.click()
                sleep(2)

            self.browser_controller.okx_wallet_connect()
            sleep(2)

            if self._is_login(lasted_tab):
                logger.success(f"【{self.id}】 钱包已连接")
                return True

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False


    def mint(self):
        proxy = self.browser_controller.browser_config.proxy or PROXY_URL
        address = self.browser_controller.browser_config.evm_address

        nft_utils = NFTUtils(MONAD_RPC, proxy=proxy)
        if nft_utils.balance_erc1155(EMMY_ON_MONAD_ADDRESS, address, 0) > 0:
            logger.info(f"【{self.id}】 已经mint，跳过...")
            self.page.quit()
            return True

        # 1. 初始化浏览器
        self.browser_controller.okx_wallet_login()
        lasted_tab = self.page.new_tab(self.url)
        sleep(8)

        result = self._connect_wallet(lasted_tab)
        if not result:
            logger.error(f"【{self.id}】 连接钱包失败")
            return False

        input_box = get_element(
            lasted_tab,
            "x://*[@id='content']/div/div[3]/div[1]/div/div/div[2]/div[2]/div/div/div[4]/label/input",
            5
        )

        if input_box and not input_box.states.is_checked:
            input_box.click()
            sleep(2)

        mint_btn = get_element(
                lasted_tab,
                "x://*[@id='content']/div/div[3]/div[1]/div/div/div[2]/div[2]/div/div/div[5]/div/button",
                5
            )

        if mint_btn:
            mint_btn.click()
            sleep(2)

            self.browser_controller.okx_wallet_sign()
            sleep(2)
        else:
            logger.error(f"【{self.id}】 未找到mint按钮，请手动处理")
            return False

        start_time = time.time()  # 记录开始时间
        while time.time() - start_time < 60:  # 60秒内查询
            if nft_utils.balance_erc1155(EMMY_ON_MONAD_ADDRESS, address, 0) > 0:
                logger.success(f"【{self.id}】 mint成功")
                self.page.quit()
                return True
            sleep(2)
            logger.info(f"【{self.id}】 mint等待中...")

        logger.error(f"【{self.id}】 在60秒内未查询到余额，请手动处理")
        return False


@click.group()
def cli():
    pass

@cli.command("r")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def register(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(id):
            try:
                browser = EmmyOnMonad(type, str(id))
                return browser.mint()
            except Exception as e:
                logger.error(f"账号 {id} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"NadDomain-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise

# python3 examples/monad/emmy_on_monad.py r -t ads -i 1-10
if __name__ == "__main__":
    cli()
    # emmy = EmmyOnMonad(BrowserType.MORE, str(1))
    # emmy.mint()
