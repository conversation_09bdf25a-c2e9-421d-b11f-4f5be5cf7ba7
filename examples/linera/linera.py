import os
import random
import re
import string
from collections.abc import Mapping
from threading import Lock
from time import sleep
from typing import Optional, Union

import click
import requests
from faker import Faker
from loguru import logger
from requests.structures import CaseInsensitiveDict
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.socials import X
from src.utils.common import get_project_root_path, parse_indices
from src.utils.element_util import get_element, get_elements, get_frame
from src.utils.hhcsv import HHCSV
from src.utils.password import generate_pwd
from src.utils.thread_executor import ThreadExecutor
from src.utils.yescaptcha import YesCaptcha

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/linera.log", rotation="10MB", level="SUCCESS")


TypeAlias = Mapping[str, str | bytes | None]


class Linera:
    # 创建类级别的锁
    _csv_lock = Lock()

    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self._init_csv()

    def _init_csv(self):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            data_dir = os.path.join(get_project_root_path(), r"examples\linera")
            self.csv_path = os.path.join(data_dir, "linera.csv")
            with self._csv_lock:
                self.csv = HHCSV(
                    self.csv_path,
                    ["index", "type", "address", "email", "password", "drops", "drops_referral_code", "heybeluga"],
                )
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def _generate_random_string(self):
        # 定义字符串长度范围（5-8）
        length = random.randint(5, 8)
        # 字母和数字的字符集
        characters = string.ascii_letters + string.digits
        # 随机选择字符生成字符串
        random_string = "".join(random.choice(characters) for _ in range(length))
        return random_string

    def _get_captcha_token(self, page) -> str | None:
        return YesCaptcha.get_web_plugin_token_hcaptcha(page, self.id)

    def _check_cf_shield1(self, lasted_tab):
        for _ in range(6):
            try:
                # 1. 判断是否在CF盾页面
                if "Register | MetaMask Developer" in lasted_tab.title:
                    return True

                div_ele = lasted_tab.ele("x:(//div[@id='uATa8']/div/div | //div[@style='display: grid;']/div/div)")
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if "Register | MetaMask Developer" in lasted_tab.title:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                logger.error(f"【{self.id}】过CF盾发生异常，error={str(e)}")
        return False

    def _check_cf_shield2(self, tab, url):
        for _ in range(6):
            try:
                div_ele = tab.ele("x://div[@id='cf-turnstile']/div", timeout=5)
                if not div_ele:
                    logger.success(f"【{self.id}】 过CF盾成功")
                    return True
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if url in tab.url:
                            logger.success(f"【{self.id}】 过CF盾成功")
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                logger.error(f"【{self.id}】过CF盾发生异常，error={str(e)}")
        return False

    def _get_email_verify_link(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd, proxy_email)

            search_criteria = SearchCriteria(
                # subject="drops",
                from_addr="email.drops.house",
                # to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]

            pattern = r'https://app\.drops\.house/email-verification\?token=[^\s<>"]+'

            # 查找匹配
            match = re.search(pattern, email["content"])
            if match:
                return match.group(1)  # 返回第一个捕获组（链接内容）
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _get_email_opt_code(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Sign in to your wallet. Your code is",
                from_addr="<EMAIL>",
                to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            # 修改正则表达式，添加捕获组
            pattern = r"(?<=Your code is[ :])(\d{6})"  # 添加括号创建捕获组

            # 查找匹配
            match = re.search(pattern, email["subject"])
            if match:
                # 获取验证码并转换为小写
                verification_code = match.group(1).lower()
                return verification_code  # 返回转换为小写的验证码
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _input_field(self, tab, xpath, value, sleep_time=1):
        """统一处理输入框操作"""
        try:
            input_element = get_element(tab, xpath, timeout=3)
            if not input_element:
                raise Exception(f"未找到输入框: {xpath}")
            input_element.clear(True)
            input_element.input(value)
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】输入字段失败 {xpath}: {str(e)}")
            return False

    def _click_element(self, tab, xpath, timeout=3, sleep_time=3):
        """统一处理点击操作"""
        try:
            element = get_element(tab, xpath, timeout)
            if not element:
                raise Exception(f"未找到元素: {xpath}")
            element.click()
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】点击元素失败 {xpath}: {str(e)}")
            return False

    def _handle_survey(self, tab):
        """处理调查问卷"""
        try:
            # 第一页
            logger.debug(f"【{self.id}】开始选择角色")
            roles = get_elements(tab, "x://span[@data-testid='onboarding-role']//div[@role='radio']", 8)
            if roles and len(roles) > 0:
                random_role = random.choice(roles[:-1])
                random_role.click()
                sleep(1)

            logger.debug(f"【{self.id}】开始选择用途")
            usages = get_elements(tab, "x://span[@data-testid='onboarding-usage']//div[@role='radio']", 5)
            if usages and len(usages) > 0:
                random.choice(usages).click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", sleep_time=5):
                return False

            # 第二页
            logger.debug(f"【{self.id}】开始选择类目")
            categories = get_elements(tab, "x://span[@data-testid='onboarding-category']//div[@role='radio']", 5)
            if categories and len(categories) > 0:
                random_category = random.choice(categories[:-1])
                random_category.click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", timeout=5, sleep_time=10):
                return False

            # 第三页
            logger.debug(f"【{self.id}】开始选择套餐")
            tier = get_element(tab, "x:(//span[@data-testid='onboarding-tier']//div[@role='radio'])[1]", 5)
            if tier:
                tier.click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", sleep_time=10):
                return False

            return True
        except Exception as e:
            logger.error(f"【{self.id}】处理调查问卷失败: {str(e)}")
            return False

    def _get_registration(self, address):
        try:
            with self._csv_lock:
                result = self.csv.query({"address": address})
                return result[0] if result else {}
        except Exception as e:
            logger.error(f"获取注册信息时发生错误: {e}")
            return {}

    def _get_registration_random(self, re):
        try:
            with self._csv_lock:
                result = self.csv.query_pro({"drops_referral_code": (">", 0)})
                return result if result else {}
        except Exception as e:
            logger.error(f"获取注册信息时发生错误: {e}")
            return {}

    def _save_registration(self, register):
        """保存注册信息到CSV"""
        try:
            address = register.get("address")
            with self._csv_lock:
                # 先查询是否存在记录
                result = self.csv.query({"address": address})
                if not result:
                    self.csv.add_row(register)
                else:
                    criteria = {"address": address}
                    self.csv.update_row(criteria, register)
        except Exception as e:
            logger.error(f"保存注册信息时发生错误: {e}")

    def login(self):
        address = self.browser_controller.browser_config.evm_address

        register = self._get_registration(address)
        tab = self.page.latest_tab
        if "https://drops.linera.io/home" not in tab.url:
            tab = self.page.new_tab("https://drops.linera.io/home")
        try:
            try_click(tab, "x://span[normalize-space()='Login']")

            input_text(tab, "x://input[@placeholder='Enter email address']", register["email"])
            input_text(tab, "x://input[@placeholder='Enter password']", register["password"])
            try_click(tab, "x://span[normalize-space()='Log me in']")
            return
        except Exception as e:
            logger.error(f"【{self.id}】 登录失败{str(e)}")
            return False

    def register(self, type: str):
        """注册linera 账号"""
        # self.login()
        try:
            # 读取address并检查是否已注册
            address = self.browser_controller.browser_config.evm_address

            register = self._get_registration(address)
            if register:
                # 兼容历史遗留数据
                if register.get("status") == "1":
                    logger.info(f"【{self.id}】钱包 {address} 已注册,跳过注册流程")
                    return True

            email = self.browser_controller.browser_config.email
            email_password = self.browser_controller.browser_config.email_password
            proxy_email = self.browser_controller.browser_config.proxy_email

            password = generate_pwd(10) if not register.get("password") else register.get("password")

            tab = self.page.latest_tab

            registers = self._get_registration_random("referral_code>0")
            random_index = random.choice(registers)
            drops_referral_code = random_index.get("drops_referral_code")

            # self.page.close_tabs(tab, others=True)
            # if "https://drops.linera.io/home" not in tab.url:
            tab = self.page.new_tab(f"https://drops.linera.io/invite?code={drops_referral_code}&ext_id=5oqo4TUSG")

            register["index"] = self.id
            register["type"] = type
            register["address"] = address
            register["email"] = email
            register["password"] = password
            register["status"] = 1
            logger.info(f"【{self.id}】注册信息: {register}")
            try_click(tab, "x://span[normalize-space()='Login']")
            try_click(tab, "x://span[normalize-space()='Sign up']")

            input_text(tab, "x://input[@placeholder='Enter email address']", email)
            input_text(tab, "x://input[@placeholder='Enter password']", password)
            input_text(tab, "x://input[@placeholder='Confirm password']", password)
            try_click(tab, "x://span[normalize-space()='Sign up with Email']")

            faker = Faker()
            first_name = faker.first_name()
            last_name = faker.last_name()

            input_text(tab, "x://input[@placeholder='Enter username']", first_name + last_name)
            sleep(1)
            try_click(tab, "x://span[normalize-space()='Continue']")

            self._save_registration(register)

            return True
        except Exception as e:
            logger.error(f"【{self.id}】注册发生异常: {str(e)}")
            return False

    def x_login(self):
        x = X(self.id, self.page)
        return x.login_by_user_auth_app(
            self.browser_controller.browser_config.x_user,
            self.browser_controller.browser_config.x_password,
            self.browser_controller.browser_config.x_two_fa,
            self.browser_controller.browser_config.x_email,
            self.browser_controller.browser_config.x_email_password,
            self.browser_controller.browser_config.x_proxy_email,
        )

    def add_dc(self, lasted_tab):
        check_dc = lasted_tab.ele("x://button[normalize-space()='Connect Discord']", timeout=5)
        if check_dc:
            check_dc.next().click()
            lasted_tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=20)
            lasted_tab.set.window.max()
            lasted_tab.wait.ele_displayed("x:(//button)[2]", timeout=20)
            allow_btn = get_element(lasted_tab, "x:(//button)[2]", 5)
            if allow_btn:
                allow_btn.click()
                sleep(10)
            sleep(10)
            return True

    def _link(self, tab):
        address = self.browser_controller.browser_config.evm_address
        link_btn = get_element(tab, "x://button[@data-test-id='hero-cta-link-dashboard-account']", 5)

        if link_btn:
            logger.info(f"【{self.id}】开始绑定MetaMask开发者账号")
            link_btn.click()
            sleep(3)

            record = self.csv.query({"address": address})
            if not record:
                raise Exception(f"【{self.id}】 {address}未注册开发者账号，请先注册")

            form_fields = {
                "x://input[@id='email']": record[0]["email"],
                "x://input[@id='password']": record[0]["password"],
            }

            for xpath, value in form_fields.items():
                if not self._input_field(tab, xpath, value):
                    raise Exception(f"填写表单字段失败: {xpath}")

            if not self._click_element(tab, "x://button[@data-testid='auth-button']"):
                raise Exception("点击Link按钮失败")
            sleep(3)

            if not self._check_cf_shield2(tab, "docs.metamask.io/developer-tools/faucet"):
                raise Exception("过CF盾失败")
            sleep(3)
            logger.success(f"【{self.id}】绑定开发者账号成功")

        return True

    def get_token(self, tab):
        for _ in range(4):
            tab.listen.start("https://api.getdrops.co/v1/auth/me")
            tab.refresh()
            res = tab.listen.wait(timeout=5)
            if res:
                return res.request.headers
            else:
                logger.error(f"【{self.id}】未收到响应")
                tab.refresh()

    # Extract points_earned and campaign_step_id
    def extract_points_and_step_ids(self, data):
        # Extract top-level points_earned
        total_points_earned = data.get("points_earned", 0)

        # Extract all campaign_step_id and step-level points_earned
        campaign_step_ids = []
        step_points_earned = []

        for step in data.get("steps_submission", []):
            campaign_step_ids.append(step.get("campaign_step_id"))
            step_points_earned.append(step.get("points_earned", 0))

        return {
            "total_points_earned": total_points_earned,
            "campaign_step_ids": campaign_step_ids,
            "step_points_earned": step_points_earned,
        }

    def get_task_status(self, headers):
        try:
            proxy = self.browser_controller.browser_config.proxy
            url = "https://api.getdrops.co/v1/user/submission?campaign_id=125&with_steps=true"
            filtered_headers = convert_and_filter_headers(headers)
            proxies = {
                "http": proxy,  # Use SOCKS5 for HTTP
                "https": proxy,  # Use SOCKS5 for HTTPS
            }
            #  filter_header = {key: value for key, value in headers.items() if not key.startswith(":")}
            response = requests.request(method="GET", url=url, headers=filtered_headers, proxies=proxies, timeout=30)
            data = response.json()
            # Extract the referral_code
            result = self.extract_points_and_step_ids(data)
            logger.info(f"Total Points Earned: {result['total_points_earned']}")
            logger.info(f"Campaign Step IDs:  {result['campaign_step_ids']}")
            return result

        except Exception:
            logger.info(f"{self.id} 获取referral_code 失败")

    def get_drops_invite_code(self, headers):
        try:
            address = self.browser_controller.browser_config.evm_address
            register = self._get_registration(address)
            if len(register.get("drops_referral_code")) > 3:
                logger.success(f"【{self.id}】已保存邀请码")
                return True
            address = self.browser_controller.browser_config.evm_address
            proxy = self.browser_controller.browser_config.proxy
            url = "https://api.getdrops.co/v1/user/referral_code?campaign_id=125"
            filtered_headers = convert_and_filter_headers(headers)

            proxies = {
                "http": proxy,  # Use SOCKS5 for HTTP
                "https": proxy,  # Use SOCKS5 for HTTPS
            }
            #  filter_header = {key: value for key, value in headers.items() if not key.startswith(":")}
            response = requests.request(method="GET", url=url, headers=filtered_headers, proxies=proxies, timeout=30)
            data = response.json()
            # Extract the referral_code
            referral_code = data.get("referral_code")
            register = self._get_registration(address)
            register["drops_referral_code"] = referral_code
            self._save_registration(register)
        except Exception:
            logger.info(f"{self.id} 获取referral_code 失败")

    def add_x(self, lasted_tab):
        try_click(lasted_tab, "x://p[normalize-space()='Entry']")
        if get_element(lasted_tab, "x://span[normalize-space()='Connect Twitter']"):
            try_click(lasted_tab, "x://span[normalize-space()='Connect Twitter']")
            tab = lasted_tab.wait.url_change(text="i/flow/login", timeout=5)
            if tab:
                self.browser_controller.login_x_with_auth()

            lasted_tab.wait.url_change(text="i/oauth2/authorize", timeout=10)
            lasted_tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
            allow_btn = get_element(lasted_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not allow_btn:
                logger.error(f"【{self.id}】未找到Twitter授权按钮")
                return False
            allow_btn.click()
            return True

    def task_1357(self, tab):
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1357']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1357']")
        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        dc_ele = get_element(tab, "x://button//span[normalize-space()='Connect Discord']")
        if dc_ele:
            try_click(tab, dc_ele)
            dc_tab = tab.wait.url_change(text="https://discord.com/login", timeout=10)
            if dc_tab:
                self.browser_controller.login_discord()
            tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=20)
            tab.set.window.max()
            tab.wait.ele_displayed("x:(//button)[2]", timeout=20)
            allow_btn = get_element(tab, "x:(//button)[2]", 5)
            if allow_btn:
                allow_btn.click()
                sleep(10)
        linera_tab = tab.wait.url_change(text="https://drops.linera.io", timeout=10)
        if linera_tab:
            if get_element(tab, "x://button//p[normalize-space()='Submit']"):
                try_click(tab, "x://button//p[normalize-space()='Submit']")
            check_dc = get_element(tab, "x://button[normalize-space()='Connect Discord']", timeout=5)
            if check_dc:
                return False
            return True

    def task_1369(self, tab):
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1369']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1369']")

        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
        if tw_ele:
            try:
                # todo有时候没有等到新标签页
                # tw_tab = self.page.new_tab("")
                try_click(tab, tw_ele)
                self.page.wait.new_tab()
                tw_tab = self.page.latest_tab
                sleep(3)
                tw_tab.close()
                try_click(tab, "x://button//p[normalize-space()='Submit']")
                return True
            except Exception as e:
                logger.error(f"{self.id} {e}")
                return False

    def _login_x(self, tab, tw_tab):
        if get_element(tw_tab, "x://a[@href='/login' and @role='link']", timeout=10):
            tw_tab.get(url="https://x.com")
            self.browser_controller.login_x()
            tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
            if tw_ele:
                # tw_tab = tw_ele.click.for_new_tab()
                try_click(tab, tw_ele)
                self.page.wait.new_tab()

                tw_tab = self.page.latest_tab
                tw_tab.set.window.max()

    def task_1358(self, tab):
        # try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1358']")
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1358']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1358']")
        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
        if tw_ele:
            # tw_tab = tw_ele.click.for_new_tab()
            try_click(tab, tw_ele)
            self.page.wait.new_tab()

            tw_tab = self.page.latest_tab
            tw_tab.set.window.max()
            # 判断x是否需要登录
            # self._login_x(tab, tw_tab)

            post_ele = get_element(tw_tab, "x://button[@data-testid='tweetButton']")
            if post_ele:
                try_click(tw_tab, post_ele)
                sleep(2)
            # cancle_ele = get_element(tw_tab, "x://button[@data-testid='unsentButton']")
            # if cancle_ele:
            #     try_click(tw_tab, cancle_ele)
            tw_tab.wait(3)
            tw_tab.set.window.max()
            x = X(self.id, self.page)

            post_url = x.get_last_post_url()
            input_text(tab, "x://input[@placeholder='Enter the tweet link here']", post_url)

            tw_tab.close()
            try_click(tab, "x://button//p[normalize-space()='Submit']")
            return True

    # try_click()

    def task_1375(self, tab):
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1375']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1375']")
        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        tw_ele = get_element(tab, "x://button//span[normalize-space()='Open Twitter']")
        if tw_ele:
            try_click(tab, tw_ele)
            self.page.wait.new_tab()

            tw_tab = self.page.latest_tab
            # tw_tab = tw_ele.click.()
            # tw_tab = self.page.new_tab(
            #     r"https://x.com/intent/post?text=Real-Time%20Blockchain%20%40linera_io%20%23microchains"
            # )

            post_ele = get_element(tw_tab, "x://button[@data-testid='tweetButton']")
            if post_ele:
                try_click(tw_tab, post_ele)
                sleep(2)
            # cancle_ele = get_element(tw_tab, "x://button[@data-testid='unsentButton']")
            # if cancle_ele:
            #     try_click(tw_tab, cancle_ele)
            tw_tab.wait(3)
            tw_tab.set.window.max()
            x = X(self.id, self.page)
            post_url = x.get_last_post_url()
            input_text(tab, "x://input[@placeholder='Enter the tweet link here']", post_url)

            tw_tab.close()
            try_click(tab, "x://button//p[normalize-space()='Submit']")
            return True

    #   try_click()

    def task_1376(self, tab):
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1376']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1376']")
        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        tw_ele = get_element(tab, "x://button//span[normalize-space()='Open Twitter']")
        if tw_ele:
            # tw_tab = tw_ele.click.for_new_tab()
            try_click(tab, tw_ele)
            self.page.wait.new_tab()

            tw_tab = self.page.latest_tab
            post_ele = get_element(tw_tab, "x://button[@data-testid='tweetButton']")
            if post_ele:
                try_click(tw_tab, post_ele)
                sleep(2)
            # cancle_ele = get_element(tw_tab, "x://button[@data-testid='unsentButton']")
            # if cancle_ele:
            #     try_click(tw_tab, cancle_ele)
            tw_tab.wait(3)
            tw_tab.set.window.max()
            x = X(self.id, self.page)
            post_url = x.get_last_post_url()
            input_text(tab, "x://input[@placeholder='Enter the tweet link here']", post_url)

            tw_tab.close()
            try_click(tab, "x://button//p[normalize-space()='Submit']")
            return True

    def task_1408(self, tab):
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1408']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1408']")
        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
        if tw_ele:
            try:
                # todo有时候没有等到新标签页
                # tw_tab = self.page.new_tab("")
                try_click(tab, tw_ele)
                self.page.wait.new_tab()
                tw_tab = self.page.latest_tab
                sleep(3)
                tw_tab.close()
                try_click(tab, "x://button//p[normalize-space()='Submit']")
                return True
            except Exception as e:
                logger.error(f"{self.id} {e}")
                return False

    def task_1407(self, tab):
        if get_element(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1407']"):
            try_click(tab, "x://a[@href='/create-entry?ext_id=5oqo4TUSG&stepId=1407']")
        update_ele = get_element(tab, "x://span[normalize-space()='Update']")
        if update_ele:
            update_ele.click()
            return True
        tw_ele = get_element(tab, "x://span[normalize-space()='Open Twitter']")
        if tw_ele:
            try:
                # todo有时候没有等到新标签页
                # tw_tab = self.page.new_tab("")
                try_click(tab, tw_ele)
                self.page.wait.new_tab()
                tw_tab = self.page.latest_tab
                sleep(3)
                tw_tab.close()
                try_click(tab, "x://button//p[normalize-space()='Submit']")
            except Exception as e:
                logger.error(f"{self.id} {e}")

    def drops(self, tab):
        """处理drops任务"""
        try:
            headers = self.get_token(tab)
            self.get_drops_invite_code(headers)
            task_status = self.get_task_status(headers)
            campaign_step_ids = task_status.get("campaign_step_ids", [])
            if not isinstance(campaign_step_ids, list):
                raise ValueError("task_status['campaign_step_ids'] must be a list")

            self.add_x(tab)

            task_map = {
                "1357": self.task_1357,
                "1369": self.task_1369,
                "1358": self.task_1358,
                "1375": self.task_1375,
                "1376": self.task_1376,
                "1408": self.task_1408,
            }

            for step_id, task_func in task_map.items():
                if step_id not in campaign_step_ids:
                    logger.info(f"Executing task for campaign_step_id {step_id}")
                    task_func(tab)
                    sleep(8)
            else:
                logger.info(f"Skipping task for campaign_step_id {step_id} (already completed)")
            logger.success(f"{self.id} drops任务已经完成!")
            return True
        except Exception as e:
            logger.error(f"{self.id} dropsr 任务异常 {e}")

    def task(self, type: str):
        try:
            tab = self.page.latest_tab

            self.page.latest_tab.set.window.max()

            if not self.register(type):
                logger.error(f"【{self.id}】注册失败，请手动处理")
                return False
            # 判断是否登录
            if "https://drops.linera.io/home" not in tab.url:
                tab = self.page.new_tab("https://drops.linera.io/home")
            # logger.info(get_element(tab, "x://span[normalize-space()='Login']", timeout=5))
            if not get_element(tab, "x://img[@alt='User avatar']", timeout=5):
                self.login()
            x = X(self.id, self.page)
            result = x.is_login()
            if not result["is_login"]:
                logger.info(f"{self.id} 登录x")
                self.browser_controller.login_x()
            result = self.drops(tab)

            if result:
                try:
                    # pass
                    self.page.quit()
                except Exception as e:
                    logger.error(f"【{self.id}】关闭页面失败: {e}")
            else:
                logger.error(f"【{self.id}】任务失败")
                self.page.quit()
            return result
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")
        #  self.page.quit()


def convert_and_filter_headers(header: CaseInsensitiveDict) -> TypeAlias:
    # Convert CaseInsensitiveDict to dict and filter out keys starting with ':'
    return {key: value for key, value in header.items() if not key.startswith(":")}


def _run_task(type, index):
    try:
        faucet = Linera(type, str(index))
        faucet.task(type)
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                faucet = Linera(type, str(index))
                result = faucet.task(type)
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    faucet.page.quit()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                faucet.page.quit()
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("heybeluga")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                agent = Linera(type, str(index))
                result = agent.task(type)
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    # faucet.page.quit()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                # faucet.page.quit()
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/linera/linera.py run -t ads -i 1-10
# python3 examples/linera/linera.py run -t bit -i 1-10
# python3 examples/linera/linera.py run -t chrome -i 1-10
# python3 examples/linera/linera.py run -t brave -i 1-10

if __name__ == "__main__":
    cli()
#
# _run_task(BrowserType.BIT, 22)
