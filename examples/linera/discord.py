import os
import random
from time import sleep

import click
from DrissionPage import Chromium
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.operations import try_click
from src.controllers import <PERSON>rows<PERSON><PERSON><PERSON>roller
from src.socials.discord import Discord
from src.socials.discord_chat_bot import DiscordChatBot
from src.utils.browser_config import BrowserConfigInstance
from src.utils.common import parse_indices
from src.utils.element_util import get_element, get_elements
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/linera_discord.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")


class DCChatBot(BrowserConfigInstance):
    def __init__(self, type: BrowserType, id: str):
        super().__init__(type, id)

    def send_message(self, channel_id: str, message: str) -> bool:
        dc_token = self.browser_config.dc_token
        proxy = self.browser_config.proxy or PROXY_URL
        user_agent = self.browser_config.user_agent
        if not dc_token:
            logger.error(f"{self.browser_id} 没有设置evm地址或dc token")
            return False

        discord = DiscordChatBot(proxy=proxy, token=dc_token, user_agent=user_agent)
        if not discord.send_message(channel_id, message):
            raise Exception("发送消息失败")
        return True


class LineraDiscord(Discord):
    def __init__(self, id: str, page: Chromium, browser_type: BrowserType):
        invite_code = "linera"
        super().__init__(id, page, browser_type, invite_code, "Linera")

    def _pre_verify(self) -> bool:
        tab = self._page.latest_tab

        # 检测是否有弹窗
        ele = get_element(tab, "x://div[contains(@class, 'focusLock__')]", 5)
        if ele:
            # 点击弹窗
            try_click(tab, "x://button[@type='button' and .='Apply']")
            sleep(2)

        self._handle_option_selection(tab, 0)
        sleep(2)
        self._handle_option_selection(tab, 0)
        sleep(2)

        return True


def _join_linera(type, index, wait):
    controller = None
    try:
        controller = BrowserController(type, index)
        discord = LineraDiscord(controller.id, controller.page, controller.browser_type)
        return discord.join_server(wait, server_id="984941796272521226")
    except Exception as e:
        logger.error(f"{controller.id} {controller.browser_type} 加入Linera失败: {e}")
    finally:
        if controller:
            controller.close_page()


@click.group()
def cli():
    pass


@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--wait", is_flag=True, help="是否等待验证码")
def join_server(index, type, wait):
    indices = parse_indices(index)
    fail_indices = []
    for _index in indices:
        try:
            result = _join_linera(type, _index, wait)
            if result:
                logger.success(f"{_index} 加群成功")
            else:
                logger.error(f"{_index} 加群失败")
                fail_indices.append(_index)

        except Exception as e:
            logger.error(f"{_index} 加群失败: {e}")
            fail_indices.append(_index)

    if fail_indices:
        logger.error(f"加群失败：{fail_indices}")


# python3 examples/linera/discord.py join -t chrome -w -i 1

if __name__ == "__main__":
    cli()

    # _join_linera(BrowserType.CHROME, "301", True)
    # _join_mullet_cop(BrowserType.CHROME, "3")
