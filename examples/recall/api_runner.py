import os
import random
from datetime import datetime
from time import sleep

import click
from api_data_utils import Api<PERSON><PERSON><PERSON>til
from loguru import logger
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import <PERSON>rowser<PERSON><PERSON>roller
from src.utils.common import get_project_root_path, parse_indices
from src.utils.thread_executor import ThreadExecutor
import requests
from fake_useragent import UserAgent
import json
from src.utils.proxies import Proxies

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/recall_api.log", rotation="10MB", level="SUCCESS")


class RecallApiClient:
    """Recall交易竞赛API客户端"""

    BASE_URL = "https://tradingcomp.recall.network"
    COMPETITOR_ID = 1  # 默认竞争者ID

    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.address = self.browser_controller.browser_config.evm_address
        self.proxy = self.browser_controller.browser_config.proxy
        self.user_agent = (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********"
            " Safari/537.36"
        )
        self.data = ApiDataUtil()
        self._init_data()
        self._set_wallet_state()
        self._init_http_client()

    def _init_data(self):
        try:
            data = self.data.get_by_index(self.id)
            if data:
                return

            data = {
                "index": self.id,
                "wallet_address": self.address,
                "competitor_id": self.COMPETITOR_ID,
            }
            self.data.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _set_wallet_state(self):
        """设置钱包连接状态到浏览器 localStorage"""
        try:
            # 启动浏览器并设置 localStorage
            page = self.browser_controller.page
            tab = page.new_tab("https://tradingcomp.recall.network/")
            time.sleep(2)

            # 设置钱包连接状态到 localStorage
            wallet_state = {"isConnected": True, "address": self.address.lower(), "isLoading": False}

            # 设置 localStorage
            tab.run_js(f'localStorage.setItem("ai_championship_wallet_state", JSON.stringify({wallet_state}))')
            tab.run_js('localStorage.setItem("isWhitelist", "false")')

            logger.success(f"【{self.id}】已设置钱包连接状态到 localStorage")

            # 获取设置后的 cookies
            cookies = tab.cookies()
            self.cookies = {}
            for cookie in cookies:
                if cookie.get("domain") and "recall.network" in cookie.get("domain", ""):
                    self.cookies[cookie["name"]] = cookie["value"]

            logger.info(f"【{self.id}】获取到 {len(self.cookies)} 个相关 cookies")

            # 关闭标签页
            tab.quit()

        except Exception as e:
            logger.error(f"【{self.id}】设置钱包状态失败: {e}")
            self.cookies = {}

    def _get_browser_cookies(self):
        """从浏览器获取 cookies"""
        try:
            # 启动浏览器并访问目标网站获取 cookies
            page = self.browser_controller.page

            # 访问目标网站
            page.get("https://tradingcomp.recall.network/")
            sleep(3)  # 等待页面加载

            # 获取所有 cookies
            cookies = page.cookies()
            self.cookies = {}

            if cookies:
                for cookie in cookies:
                    if (
                        isinstance(cookie, dict)
                        and cookie.get("domain")
                        and "recall.network" in cookie.get("domain", "")
                    ):
                        self.cookies[cookie["name"]] = cookie["value"]

            logger.info(f"【{self.id}】获取到 {len(self.cookies)} 个相关 cookies")

            # 关闭浏览器页面
            try:
                page.quit()
            except:
                pass

        except Exception as e:
            logger.error(f"【{self.id}】获取浏览器 cookies 失败: {e}")
            self.cookies = {}

    def _init_http_client(self):
        """初始化HTTP客户端"""
        try:
            # 更新到最新的Chrome 138浏览器指纹
            self.headers = {
                "accept": "*/*",
                "accept-language": "en-US,en;q=0.9",
                "content-type": "application/json",
                "priority": "u=1, i",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": self.user_agent,
                "referer": "https://tradingcomp.recall.network/",
            }

            # 设置代理
            self.proxies = None
            if self.proxy:
                try:
                    self.proxies = Proxies(self.proxy).get_proxies()
                    logger.info(f"【{self.id}】使用代理: {self.proxy}")
                except Exception as e:
                    logger.error(f"【{self.id}】代理设置失败: {e}")
                    self.proxies = None

            # 创建session
            self.session = requests.Session()
            self.session.headers.update(self.headers)

            # 添加从浏览器获取的 cookies (对应fetch的credentials: "include")
            if hasattr(self, "cookies") and self.cookies:
                for name, value in self.cookies.items():
                    self.session.cookies.set(name, value, domain=".recall.network")
                logger.info(f"【{self.id}】已设置 {len(self.cookies)} 个 cookies")

            if self.proxies:
                self.session.proxies.update(self.proxies)

            logger.info(f"【{self.id}】HTTP客户端初始化成功")

        except Exception as e:
            logger.error(f"【{self.id}】HTTP客户端初始化失败: {e}")
            raise

    def call_selections_api(self):
        """调用selections API"""
        try:
            url = f"{self.BASE_URL}/api/selections"
            payload = {"walletAddress": self.address, "competitorId": self.COMPETITOR_ID}

            logger.info(f"【{self.id}】调用selections API: {url}")

            response = self.session.post(url=url, json=payload, timeout=30)

            # 检查响应状态
            if response.status_code in [200, 201]:
                logger.success(f"【{self.id}】selections API调用成功")
                try:
                    response_data = response.json()
                except:
                    response_data = response.text

                return {"status": "success", "data": response_data, "error": None}
            elif response.status_code == 400:
                # 检查是否是"已经投过票"的情况
                try:
                    response_data = response.json()
                    if response_data.get("alreadyVoted") == True:
                        logger.success(f"【{self.id}】钱包已经投过票，视为成功")
                        return {"status": "success", "data": response_data, "error": None}
                except:
                    pass

                error_msg = f"selections API返回错误状态码: {response.status_code}"
                logger.error(f"【{self.id}】{error_msg}")
                return {"status": "failed", "data": None, "error": error_msg}
            else:
                error_msg = f"selections API返回错误状态码: {response.status_code}"
                logger.error(f"【{self.id}】{error_msg}")
                return {"status": "failed", "data": None, "error": error_msg}

        except Exception as e:
            error_msg = f"selections API调用失败: {str(e)}"
            logger.error(f"【{self.id}】{error_msg}")
            return {"status": "failed", "data": None, "error": error_msg}

    def call_auth_api(self):
        """调用认证相关API - 模拟钱包连接"""
        try:
            # 尝试调用可能的认证端点
            auth_endpoints = [
                f"{self.BASE_URL}/api/auth/wallet",
                f"{self.BASE_URL}/api/auth/connect",
                f"{self.BASE_URL}/api/wallet/connect",
                f"{self.BASE_URL}/api/user/auth",
                f"{self.BASE_URL}/api/session",
            ]

            for endpoint in auth_endpoints:
                try:
                    logger.info(f"【{self.id}】尝试认证端点: {endpoint}")

                    # 尝试 POST 请求
                    auth_payload = {
                        "walletAddress": self.address,
                        "signature": "mock_signature",  # 可能需要真实签名
                        "message": "Authentication message",
                    }

                    response = self.session.post(url=endpoint, json=auth_payload, timeout=10)
                    logger.info(f"【{self.id}】{endpoint} 响应: {response.status_code}")

                    if response.status_code in [200, 201]:
                        logger.success(f"【{self.id}】找到有效认证端点: {endpoint}")
                        return {"status": "success", "endpoint": endpoint, "data": response.text}

                except Exception as e:
                    logger.debug(f"【{self.id}】{endpoint} 失败: {e}")
                    continue

            return {"status": "no_auth_endpoint", "data": None}

        except Exception as e:
            logger.error(f"【{self.id}】认证API调用失败: {e}")
            return {"status": "failed", "data": None, "error": str(e)}

    def call_voted_api(self):
        """调用voted API"""
        try:
            url = f"{self.BASE_URL}/api/wallet/{self.address}/voted"

            logger.info(f"【{self.id}】调用voted API: {url}")

            # 添加缓存控制headers (匹配fetch请求中的if-none-match)
            headers = self.headers.copy()
            headers["if-none-match"] = 'W/"4d-zUuxjKLOC9yk69rk+2BTA/1iXYk"'

            response = self.session.get(url=url, headers=headers, timeout=30)

            # 检查响应状态
            if response.status_code == 200:
                logger.success(f"【{self.id}】voted API调用成功")
                try:
                    response_data = response.json()
                except:
                    response_data = response.text
                return {"status": "success", "data": response_data, "error": None}
            elif response.status_code == 304:
                logger.info(f"【{self.id}】voted API返回304 (Not Modified)，数据未变化")
                return {"status": "success", "data": "not_modified", "error": None}
            else:
                error_msg = f"voted API返回错误状态码: {response.status_code}"
                logger.error(f"【{self.id}】{error_msg}")
                return {"status": "failed", "data": None, "error": error_msg}

        except Exception as e:
            error_msg = f"voted API调用失败: {str(e)}"
            logger.error(f"【{self.id}】{error_msg}")
            return {"status": "failed", "data": None, "error": error_msg}

    def call_leaderboard_api(self):
        """调用leaderboard API"""
        try:
            url = f"{self.BASE_URL}/api/leaderboard"

            logger.info(f"【{self.id}】调用leaderboard API: {url}")

            # 添加缓存控制headers (匹配fetch请求中的if-none-match)
            headers = self.headers.copy()
            headers["if-none-match"] = 'W/"973-N9niMQWyIp0nkTqFOXM8U0AFRJ4"'

            response = self.session.get(url=url, headers=headers, timeout=30)

            # 检查响应状态
            if response.status_code == 200:
                logger.success(f"【{self.id}】leaderboard API调用成功")
                try:
                    response_data = response.json()
                except:
                    response_data = response.text
                return {"status": "success", "data": response_data, "error": None}
            elif response.status_code == 304:
                logger.info(f"【{self.id}】leaderboard API返回304 (Not Modified)，数据未变化")
                return {"status": "success", "data": "not_modified", "error": None}
            else:
                error_msg = f"leaderboard API返回错误状态码: {response.status_code}"
                logger.error(f"【{self.id}】{error_msg}")
                return {"status": "failed", "data": None, "error": error_msg}

        except Exception as e:
            error_msg = f"leaderboard API调用失败: {str(e)}"
            logger.error(f"【{self.id}】{error_msg}")
            return {"status": "failed", "data": None, "error": error_msg}

    def _log_results(self, selections_result, voted_result, leaderboard_result=None):
        """记录结果到CSV"""
        try:
            csv_data = {
                "selections_status": selections_result["status"],
                "selections_response": json.dumps(selections_result["data"]) if selections_result["data"] else "",
                "voted_status": voted_result["status"],
                "voted_response": json.dumps(voted_result["data"]) if voted_result["data"] else "",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "error_message": selections_result.get("error", "") or voted_result.get("error", ""),
                "proxy_used": self.proxy or "",
                "user_agent_used": self.user_agent,
            }

            self.data.update_by_index(self.id, csv_data)
            logger.info(f"【{self.id}】结果已记录到CSV")

        except Exception as e:
            logger.error(f"【{self.id}】记录结果到CSV失败: {e}")

    @retry(tries=3, delay=1)
    def _task(self):
        """执行API任务"""
        try:
            logger.info(f"【{self.id}】开始执行API任务")

            # 1. 调用selections API
            selections_result = self.call_selections_api()
            sleep(1)

            # 2. 调用voted API
            voted_result = self.call_voted_api()
            sleep(1)

            # 3. 调用leaderboard API
            leaderboard_result = self.call_leaderboard_api()

            # 4. 记录结果到CSV
            self._log_results(selections_result, voted_result, leaderboard_result)

            # 判断任务是否成功
            task_success = (
                selections_result["status"] == "success"
                and voted_result["status"] == "success"
                and leaderboard_result["status"] == "success"
            )

            if not task_success:
                raise Exception("API调用失败")

            return True

        except Exception as e:
            logger.error(f"【{self.id}】执行API任务异常: {e}")
            raise
        finally:
            try:
                self.session.close()
            except:
                pass

    def task(self):
        """主任务方法"""
        try:
            data = self.data.get_by_index(self.id)
            if not data:
                logger.error(f"【{self.id}】 数据不存在")
                return False

            # 检查是否已经完成
            if data.get("selections_status") == "success" and data.get("voted_status") == "success":
                logger.success(f"【{self.id}】 任务已经完成, 无需重复执行...")
                return True

            return self._task()

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False


def _run_task(type, index):
    try:
        client = RecallApiClient(type, str(index))
        client.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                client = RecallApiClient(type, str(index))
                return client.task()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=30 * 60,  # 30分钟超时
            retries=3,
            interval=10,
            task_name=f"RecallAPI-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 使用方法:
# python3 examples/recall/api_runner.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.CHROME, "204")
