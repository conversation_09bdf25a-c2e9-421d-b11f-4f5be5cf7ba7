import click
import time
import random
from datetime import datetime
from loguru import logger
from retry import retry

from api_data_utils import Api<PERSON><PERSON><PERSON><PERSON>
from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType, ChromeBrowser
from src.controllers import Browser<PERSON>ontroller
from src.utils.common import get_project_root_path, parse_indices
from src.utils.thread_executor import ThreadExecutor
from src.utils.element_util import get_element
from src.browsers.operations import try_click

default_browser_type = DEFAULT_BROWSER_TYPE

logger.add("logs/recall_tradingcomp.log", rotation="10MB", level="SUCCESS")


class TradingCompAutomation:
    """Trading Competition 自动化投票类"""

    BASE_URL = "https://tradingcomp.recall.network"
    COMPETITOR_ID = 1

    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.address = self.browser_controller.browser_config.evm_address

        self.data = ApiDataUtil()
        self._init_data()

    def _init_data(self):
        """初始化数据"""
        try:
            data = {
                "index": self.id,
                "wallet_address": self.address,
                "browser_type": self.browser_type.value,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }

            # 检查记录是否已存在
            existing_data = self.data.get_by_index(self.id)
            if existing_data:
                # 如果记录已存在，更新基本信息
                self.data.update_by_index(self.id, data)
                logger.info(f"【{self.id}】更新现有记录: {self.address}")
            else:
                # 如果记录不存在，添加新记录
                self.data.add(data)
                logger.info(f"【{self.id}】添加新记录: {self.address}")
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    @retry(tries=3, delay=2)
    def login_okx_wallet(self):
        """登录 OKX 钱包"""
        try:
            logger.info(f"【{self.id}】开始登录 OKX 钱包...")
            self.browser_controller.okx_wallet_login()
            logger.success(f"【{self.id}】OKX 钱包登录成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】OKX 钱包登录失败: {e}")
            raise

    @retry(tries=3, delay=2)
    def navigate_to_site(self):
        """访问 Trading Competition 网站"""
        try:
            logger.info(f"【{self.id}】访问 Trading Competition 网站...")
            tab = self.page.new_tab(self.BASE_URL)
            time.sleep(3)

            # 检查页面是否加载成功
            title = tab.title
            if "Crypto Trading Competition" not in title:
                raise Exception(f"页面标题不正确: {title}")

            logger.success(f"【{self.id}】页面加载成功: {title}")
            return tab
        except Exception as e:
            logger.error(f"【{self.id}】访问网站失败: {e}")
            raise

    @retry(tries=3, delay=2)
    def connect_wallet(self, tab):
        """连接钱包"""
        try:
            logger.info(f"【{self.id}】开始连接钱包...")

            # 检查是否已经连接
            wallet_address_elements = tab.eles("x://*[contains(text(),'0x')]")
            for elem in wallet_address_elements:
                if len(elem.text) > 10 and "0x" in elem.text.lower():
                    logger.success(f"【{self.id}】钱包已连接: {elem.text}")
                    return True

            # 查找连接钱包按钮 - 使用单一精确的xpath
            connect_btn = get_element(tab, "x://div[@class='ml-4']//button[.='Connect Wallet']", timeout=5)

            if not connect_btn:
                logger.warning(f"【{self.id}】未找到连接钱包按钮，可能已连接")
                return True

            # 点击连接钱包
            logger.info(f"【{self.id}】点击连接钱包按钮...")
            connect_btn.click()
            time.sleep(3)

            # 查找并点击 OKX 钱包选项 - 使用单一xpath
            okx_btn = get_element(tab, "x://button[contains(text(),'OKX')]", timeout=5)

            if okx_btn:
                logger.info(f"【{self.id}】点击 OKX 钱包选项...")
                okx_btn.click()
                time.sleep(2)

            # 调用钱包连接
            logger.info(f"【{self.id}】调用 OKX 钱包连接...")
            self.browser_controller.okx_wallet_connect()
            time.sleep(5)

            # 验证连接是否成功
            wallet_address_elements = tab.eles("x://*[contains(text(),'0x')]")
            for elem in wallet_address_elements:
                if len(elem.text) > 10 and "0x" in elem.text.lower():
                    logger.success(f"【{self.id}】钱包连接成功: {elem.text}")
                    return True

            # 检查连接按钮是否消失
            updated_connect_btn = get_element(tab, "x://div[@class='ml-4']//button[.='Connect Wallet']", timeout=3)
            if not updated_connect_btn:
                logger.success(f"【{self.id}】钱包连接成功（连接按钮已消失）")
                return True

            raise Exception("钱包连接验证失败")

        except Exception as e:
            logger.error(f"【{self.id}】连接钱包失败: {e}")
            raise

    @retry(tries=3, delay=2)
    def vote_for_competitor(self, tab):
        """为竞争者投票"""
        try:
            logger.info(f"【{self.id}】开始投票流程...")

            updated_vote_btn = get_element(tab, "SHARE YOUR VOTES TO EARN MORE", timeout=3)
            if updated_vote_btn:
                logger.success(f"【{self.id}】已经投票过")
                return True

            ele = get_element(tab, "x://label[@for='card-1']")
            ele.click()

            try_click(tab, "x://button[.='Continue to Confirmation']")
            tab.wait(2)

            try_click(tab, "x://button[.='Submit Vote']")

            # 检查投票按钮状态变化
            updated_vote_btn = get_element(tab, "SHARE YOUR VOTES TO EARN MORE", timeout=3)
            if updated_vote_btn:
                logger.success(f"【{self.id}】投票成功")
                return True

            logger.warning(f"【{self.id}】投票状态验证不确定")
            return True  # 假设成功，避免重复尝试

        except Exception as e:
            logger.error(f"【{self.id}】投票失败: {e}")
            raise

    def _log_results(self, success, error_message=None):
        """记录结果到CSV"""
        try:
            csv_data = {
                "status": "success" if success else "failed",
                "result": "投票完成" if success else f"投票失败: {error_message}",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "error_message": error_message or "",
            }

            self.data.update_by_index(self.id, csv_data)
            logger.info(f"【{self.id}】结果已记录到CSV")

        except Exception as e:
            logger.error(f"【{self.id}】记录结果到CSV失败: {e}")

    @retry(tries=2, delay=5)
    def _task(self):
        """执行自动化投票任务"""
        tab = None
        self.page = self.browser_controller.page
        try:
            logger.info(f"【{self.id}】开始执行自动化投票任务")

            # 1. 登录 OKX 钱包
            self.login_okx_wallet()

            # 2. 访问网站
            tab = self.navigate_to_site()

            # 3. 连接钱包
            self.connect_wallet(tab)

            # 4. 投票
            vote_success = self.vote_for_competitor(tab)

            # 5. 记录结果
            self._log_results(vote_success)

            if not vote_success:
                raise Exception("投票失败")

            logger.success(f"【{self.id}】自动化投票任务完成")
            return True

        except Exception as e:
            logger.error(f"【{self.id}】执行自动化投票任务异常: {e}")
            self._log_results(False, str(e))
            raise
        finally:
            # 记录任务完成，浏览器将在外层统一关闭
            if tab:
                logger.info(f"【{self.id}】任务执行完成")

    def task(self):
        """任务入口"""
        try:
            # 检查是否已有记录
            existing_data = self.data.get_by_index(self.id)
            if existing_data and existing_data.get("status") == "success":
                logger.success(f"【{self.id}】自动化投票已完成")
                return True

            return self._task()
        except Exception as e:
            logger.error(f"【{self.id}】任务执行失败: {e}")
            return False


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--indices", type=str, prompt="请输入浏览器序号", help="浏览器序号，支持范围和逗号分隔")
@click.option("-c", "--concurrency", type=int, default=1, help="并发数")
def run(type, indices, concurrency):
    """运行自动化投票任务"""
    try:
        indices_list = parse_indices(indices)
        random.shuffle(indices_list)
        logger.info(f"开始执行自动化投票任务，浏览器序号: {indices_list}")

        failed_indices = []

        def create_task(index):
            automation = TradingCompAutomation(type, str(index))
            try:
                result = automation.task()
                if not result:
                    failed_indices.append(str(index))
                return {"index": index, "success": result, "error": None}
            except Exception as e:
                logger.error(f"【{index}】任务执行异常: {e}")
                return {"index": index, "success": False, "error": str(e)}
            finally:
                # 关闭浏览器窗口
                try:
                    automation.browser_controller.close_page()
                    logger.info(f"【{index}】浏览器窗口已关闭")
                except Exception as e:
                    logger.warning(f"【{index}】关闭浏览器窗口失败: {e}")

        executor = ThreadExecutor(
            workers=concurrency,
            timeout=1800,  # 30分钟超时
            retries=2,
            interval=5,
            task_name=f"TradingComp-{type}-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            raise_exception=False,
        )

        executor.run_batch(create_task, indices_list)

        # 输出失败统计
        logger.info(f"failed_counts: {','.join(failed_indices)}")

    except Exception as e:
        logger.error(f"执行任务时出错: {e}")


if __name__ == "__main__":
    cli()

    # index = 201
    # automation = TradingCompAutomation(BrowserType.CHROME, str(index))
    # automation.task()
