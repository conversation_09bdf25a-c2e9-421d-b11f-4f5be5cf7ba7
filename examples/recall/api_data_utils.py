import os
from threading import Lock
from datetime import datetime

from loguru import logger

from src.utils.common import get_project_root_path
from src.utils.hhcsv import HHCSV

# API结果CSV数据结构
RECALL_API_DATA_PROPS = [
    "index",
    "wallet_address",
    "competitor_id",
    "selections_status",
    "selections_response",
    "voted_status",
    "voted_response",
    "timestamp",
    "error_message",
]


class ApiDataUtil:
    def __init__(self):
        self._init_csv()

    def _init_csv(self):
        try:
            # 设置CSV文件路径
            data_dir = os.path.join(get_project_root_path(), "examples", "recall")
            self._csv_path = os.path.join(data_dir, "recall_api_results.csv")
            self._csv = HHCSV(self._csv_path, RECALL_API_DATA_PROPS)
        except Exception as e:
            logger.error(f"初始化API结果CSV文件失败: {str(e)}")
            raise

    def list(self):
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部API结果数据失败: {str(e)}")
            return []

    def get_by_index(self, index):
        try:
            result = self._csv.query({"index": str(index)})
            return result[0] if result else {}
        except Exception as e:
            logger.error(f"查询 index {index} API结果数据失败: {str(e)}")
            return {}

    def get_by_wallet(self, wallet_address):
        try:
            result = self._csv.query({"wallet_address": wallet_address})
            return result if result else []
        except Exception as e:
            logger.error(f"查询 {wallet_address} API结果数据失败: {str(e)}")
            return []

    def add(self, data):
        try:
            # 添加时间戳
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            self._csv.add_row(data)
            logger.info(f"添加API结果记录: index={data.get('index')}, wallet={data.get('wallet_address')}")
        except Exception as e:
            logger.error(f"新增API结果数据失败, data={data}, error={str(e)}")

    def update_by_index(self, index, data):
        try:
            criteria = {"index": str(index)}
            # 更新时间戳
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = self._csv.update_row(criteria, data)
            if result > 0:
                logger.info(f"更新API结果记录: index={index}, data={data}")
            return result > 0
        except Exception as e:
            logger.error(f"更新API结果数据失败, index={index}, data={data}, error={str(e)}")
            return False

    def update_by_wallet(self, wallet_address, data):
        try:
            criteria = {"wallet_address": wallet_address}
            # 更新时间戳
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = self._csv.update_row(criteria, data)
            if result > 0:
                logger.info(f"更新API结果记录: wallet={wallet_address}, data={data}")
            return result > 0
        except Exception as e:
            logger.error(f"更新API结果数据失败, wallet={wallet_address}, data={data}, error={str(e)}")
            return False

    def flush(self):
        try:
            self._csv.load()
        except Exception as e:
            logger.error(f"刷新API结果数据失败, error={str(e)}")
