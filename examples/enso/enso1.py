import csv
import datetime
import json
import os
import random
import re
import string
import time
from threading import Lock
from time import sleep
from typing import Optional

import click
import pya<PERSON><PERSON><PERSON>
from faker import Faker
from loguru import logger
from retry import retry
from web3 import Web3

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.evm.nft_utils import NFTUtils
from src.socials import X
from src.utils.common import get_project_root_path, parse_indices
from src.utils.element_util import get_element, get_elements, get_frame
from src.utils.hhcsv import HHCSV
from src.utils.password import generate_pwd
from src.utils.thread_executor import ThreadExecutor
from src.utils.yescaptcha import YesCaptcha

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/metamask_faucet.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.getenv("PROXY_URL")
ETH_RPC_URL = os.getenv("ETH_RPC_URL")


class Enso:
    # 创建类级别的锁
    _csv_lock = Lock()

    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self._init_csv()

    def _init_csv(self):
        try:
            # 设置CSV文件路径，使用os.path.join确保跨平台兼容
            data_dir = os.path.join(get_project_root_path(), r"examples\enso")
            self.csv_path = os.path.join(data_dir, "enso.csv")
            with self._csv_lock:
                self.csv = HHCSV(self.csv_path, ["index", "type", "address", "email", "password"])
        except Exception as e:
            logger.error(f"初始化CSV文件失败: {str(e)}")
            raise

    def _generate_random_string(self):
        # 定义字符串长度范围（5-8）
        length = random.randint(5, 8)
        # 字母和数字的字符集
        characters = string.ascii_lowercase + string.digits
        # 随机选择字符生成字符串
        random_string = "".join(random.choice(characters) for _ in range(length))
        return random_string

    def _get_captcha_token(self, page) -> str | None:
        return YesCaptcha.get_web_plugin_token_hcaptcha(page, self.id)

    def _check_cf_shield1(self, lasted_tab):
        for _ in range(6):
            try:
                # 1. 判断是否在CF盾页面
                if "verify-email" in lasted_tab.url:
                    return True

                div_ele = lasted_tab.ele(
                    "x:(//*[@id='radix-:r17:']/div/div/div |//*[@id='radix-:r19:']/div/div/div| /html/body/div[3]/div/div/div)"
                )
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if "Register | MetaMask Developer" in lasted_tab.title:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                logger.error(f"【{self.id}】过CF盾发生异常，error={str(e)}")
        return False

    def _check_cf_shield2(self, tab, url):
        for _ in range(6):
            try:
                div_ele = tab.ele("x://div[@id='cf-turnstile']/div", timeout=5)
                if not div_ele:
                    logger.success(f"【{self.id}】 过CF盾成功")
                    return True
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if url in tab.url:
                            logger.success(f"【{self.id}】 过CF盾成功")
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                logger.error(f"【{self.id}】过CF盾发生异常，error={str(e)}")
        return False

    def _get_email_verify_code(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Your temporary Zealy login code is",
                from_addr="zealy.io",
                # to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]

            pattern = r"is\s+([A-Za-z0-9]{6})"

            # 查找匹配
            match = re.search(pattern, email["subject"])
            if match:
                return match.group(1)  # 返回第一个捕获组（链接内容）
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _get_email_opt_code(self, email, email_pwd, proxy_email):
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            search_criteria = SearchCriteria(
                subject="Sign in to your wallet. Your code is",
                from_addr="<EMAIL>",
                to=proxy_email or email,
                # is_read=False
            )
            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            # 修改正则表达式，添加捕获组
            pattern = r"(?<=Your code is[ :])(\d{6})"  # 添加括号创建捕获组

            # 查找匹配
            match = re.search(pattern, email["subject"])
            if match:
                # 获取验证码并转换为小写
                verification_code = match.group(1).lower()
                return verification_code  # 返回转换为小写的验证码
            return None
        except Exception as e:
            logger.error(f"{self.id} 获取验证链接失败: {e}")
            return None

    def _input_field(self, tab, xpath, value, sleep_time=1):
        """统一处理输入框操作"""
        try:
            input_element = get_element(tab, xpath, timeout=3)
            if not input_element:
                raise Exception(f"未找到输入框: {xpath}")
            input_element.clear(True)
            input_element.input(value)
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】输入字段失败 {xpath}: {str(e)}")
            return False

    def _click_element(self, tab, xpath, timeout=3, sleep_time=3):
        """统一处理点击操作"""
        try:
            element = get_element(tab, xpath, timeout)
            if not element:
                raise Exception(f"未找到元素: {xpath}")
            element.click()
            sleep(sleep_time)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】点击元素失败 {xpath}: {str(e)}")
            return False

    def _handle_survey(self, tab):
        """处理调查问卷"""
        try:
            # 第一页
            logger.debug(f"【{self.id}】开始选择角色")
            roles = get_elements(tab, "x://span[@data-testid='onboarding-role']//div[@role='radio']", 8)
            if roles and len(roles) > 0:
                random_role = random.choice(roles[:-1])
                random_role.click()
                sleep(1)

            logger.debug(f"【{self.id}】开始选择用途")
            usages = get_elements(tab, "x://span[@data-testid='onboarding-usage']//div[@role='radio']", 5)
            if usages and len(usages) > 0:
                random.choice(usages).click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", sleep_time=5):
                return False

            # 第二页
            logger.debug(f"【{self.id}】开始选择类目")
            categories = get_elements(tab, "x://span[@data-testid='onboarding-category']//div[@role='radio']", 5)
            if categories and len(categories) > 0:
                random_category = random.choice(categories[:-1])
                random_category.click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", timeout=5, sleep_time=10):
                return False

            # 第三页
            logger.debug(f"【{self.id}】开始选择套餐")
            tier = get_element(tab, "x:(//span[@data-testid='onboarding-tier']//div[@role='radio'])[1]", 5)
            if tier:
                tier.click()
                sleep(1)
            if not self._click_element(tab, "x://button[@data-testid='survey-page-next']", sleep_time=10):
                return False

            return True
        except Exception as e:
            logger.error(f"【{self.id}】处理调查问卷失败: {str(e)}")
            return False

    def _get_registration(self, address):
        try:
            with self._csv_lock:
                result = self.csv.query({"address": address})
                return result[0] if result else {}
        except Exception as e:
            logger.error(f"获取注册信息时发生错误: {e}")
            return {}

    def _save_registration(self, register):
        """保存注册信息到CSV"""
        try:
            address = register.get("address")
            with self._csv_lock:
                # 先查询是否存在记录
                result = self.csv.query({"address": address})
                if not result:
                    self.csv.add_row(register)
                else:
                    criteria = {"address": address}
                    self.csv.update_row(criteria, register)
        except Exception as e:
            logger.error(f"保存注册信息时发生错误: {e}")

    @retry(tries=3, delay=3)
    def register(self, type: str, laster_tab):
        """注册enso 账号"""
        # self.login()
        try:
            # 读取address并检查是否已注册
            address = self.browser_controller.browser_config.evm_address
            tab = laster_tab
            register = self._get_registration(address)
            if register:
                # 兼容历史遗留数据
                if register.get("status") == "1" or register.get("status") == "0":
                    logger.info(f"【{self.id}】钱包 {address} 已注册,跳过注册流程")
                    return True

            email_name = self.browser_controller.browser_config.email
            email_pwd = self.browser_controller.browser_config.email_password
            proxy_email = self.browser_controller.browser_config.proxy_email
            email = proxy_email or email_name

            register["index"] = self.id
            register["type"] = type
            register["address"] = address
            register["email"] = email
            # 打开注册页面

            # tab.refresh()
            logger.info(f"【{self.id}】钱包 {address} 开始注册")
            sleep(5)
            # logger.info(tab.html)
            check_ele1 = get_element(tab, "x://button[normalize-space()='Check my bonus']", timeout=10)
            check_ele1.wait.has_rect(timeout=20)
            if not check_ele1:
                logger.info(f"【{self.id}】注册发生异常: 点击第一个按钮失败{check_ele1}")

            check_ele1.click()
            if get_element(tab, "x://button[normalize-space()='Done']"):
                try_click(tab, "x://button[normalize-space()='Done']")
                register["status"] = 0
                self._save_registration(register)
                return True
            # try_click(tab, "x://button[normalize-space()='Check my bonus']", timeout=5)
            if not get_element(tab, "x://div[contains(text(), 'signed in')]"):
                # check_ele1 = tab.ele("x://button[normalize-space()='Check my bonus']", timeout=10)
                # check_ele1.wait.has_rect(timeout=20)
                try_click(tab, "x://button[@aria-label='Connect to Connect wallet']")
                try_click(tab, "x://div[contains(text(),'OKX Wallet')]")
                self.browser_controller.okx_wallet_connect()
                try_click(tab, "x://button[contains(text(),'Sign message')]")
                self.browser_controller.okx_wallet_connect()

            # 连接 Zealy
            sleep(3)
            zealy_ele = get_element(tab, "x://span[contains(text(),'Connect Zealy')]", timeout=10)
            if zealy_ele:
                new_tab = zealy_ele.click.for_new_tab()
            else:
                tab.refresh()
                raise Exception("注册流程未完成")
            sleep(3)
            if get_element(new_tab, "x://button[normalize-space()='Connect to Zealy']", timeout=30):
                try_click(new_tab, "x://button[normalize-space()='Connect to Zealy']")
                input_text(new_tab, "x://input[@id='email']", email)
                try_click(new_tab, "x://button[normalize-space()='Continue with email']")
                self._check_cf_shield1(new_tab)
                for retry in range(3):  # 重试3次
                    code = self._get_email_verify_code(email_name, email_pwd, proxy_email)
                    if code:
                        break
                    sleep(5)  # 等待邮件到达

                # if len(code) == 6:
                #     # 将验证码的每一位分别输入到对应的输入框
                #     for i in range(6):
                #         input_text(new_tab, f"x://input[@name='code-{i}']", code[i], timeout=10)

                input_text(new_tab, "x://input[@autocomplete='one-time-code']", code, timeout=10)
                input_text(new_tab, "x://input[@id='name']", f"{email.split('@')[0]}")
                sleep(2)
                try_click(new_tab, "x://button[@aria-label='Next']", timeout=5)
                # try_click(new_tab, "x://button[@aria-label='Next']", timeout=3)
                register["status"] = 0
                self._save_registration(register)
            if get_element(new_tab, "x://button[normalize-space()='Get started']", timeout=10):
                try_click(new_tab, "x://button[normalize-space()='Get started']")
                logger.success(f"【{self.id}】注册成功, address: {address}, email: {email}")
                register["status"] = 1
                self._save_registration(register)

                return True
            else:
                raise Exception("注册流程未完成1")
        except Exception as e:
            logger.error(f"【{self.id}】注册发生异常: {str(e)}")
            raise Exception("注册流程未完成")

    def x_login(self):
        x = X(self.id, self.page)
        return x.login_by_user_auth_app(
            self.browser_controller.browser_config.x_user,
            self.browser_controller.browser_config.x_password,
            self.browser_controller.browser_config.x_two_fa,
            self.browser_controller.browser_config.x_email,
            self.browser_controller.browser_config.x_email_password,
            self.browser_controller.browser_config.x_proxy_email,
        )

    @retry(tries=3, delay=3)
    def defi_task_daily(self):
        """检查所有任务的完成状态"""
        try:
            # 监听任务状态的请求
            x_user = self.browser_controller.browser_config.x_user
            tab = self.page.latest_tab
            if not tab.url == "https://speedrun.enso.build/":
                logger.info("切换tab,开始defi_task_daily")
                self.page.new_tab(url="https://speedrun.enso.build")
                tab = self.page.latest_tab
            ai_ele = get_element(tab, "x://a[contains(@href, '/categories/de-fi') ]")
            if ai_ele:
                ai_ele.click()
                if not get_element(tab, "x://span[contains(text(),'Resets ')]"):
                    for i in range(5):
                        try_click(tab, "x://span[normalize-space()='DeFi DEX']")
                        faker = Faker()
                        first_name = faker.first_name()
                        last_name = faker.last_name()
                        input_text(tab, "x://input[@id='name']", first_name)
                        input_text(tab, "x://input[@id='subdomain']", self._generate_random_string())
                        input_text(tab, "x://input[@id='twitter']", "@" + x_user)

                        try_click(tab, "x://button[normalize-space()='Submit']")

                        try_click(tab, "x://button[normalize-space()='Close']")

                    return True
                ai_ele = get_element(tab, "x://span[contains(text(),'Resets ')]")
                logger.info(f"{self.id} defi_task_daily已经完成，还需等待{ai_ele.raw_text}")
                return True
            else:
                logger.error(f"{self.id} defi_task_daily 未找到")
                raise Exception("defi_task_daily, triggering retry.")
        except Exception as e:
            logger.error(f"【{self.id}】 {str(e)}")
            tab = self.page.new_tab("https://speedrun.enso.build")
            raise Exception("defi_task_daily, triggering retry.")

    @retry(tries=3, delay=3)
    def ai_task_daily(self):
        try:
            tab = self.page.latest_tab

            # self.page.close_tabs(tab, others=True)
            logger.info(f"{self.id} ai_task_daily开始")
            if not tab.url == "https://speedrun.enso.build/":
                tab.get(url="https://speedrun.enso.build")

            try_click(tab, "x://a[contains(@href, '/categories/ai') ]")
            if not get_element(tab, "x://span[contains(text(),'Resets ')]"):
                ai_ele = get_element(tab, "x://span[normalize-space()='Ask Enso AI']")
                if ai_ele:
                    # 设置加载模式为none
                    ai_tab = ai_ele.click.for_new_tab()
                    ai_tab.set.load_mode.none()
                    ai_tab.wait(10)
                    ai_tab.stop_loading()
                    # tab.refresh()

                    if get_element(ai_tab, "x://button[normalize-space()='Connect Wallet']", timeout=10):
                        # ai_tab.stop_loading()
                        try_click(ai_tab, "x://button[normalize-space()='Connect Wallet']")
                        if get_element(
                            ai_tab,
                            "x://button[normalize-space()='发送消息' or normalize-space()='Sign message'  ]",
                            timeout=5,
                        ):
                            try_click(
                                ai_tab,
                                "x://button[normalize-space()='发送消息' or normalize-space()='Sign message'  ]",
                            )
                            if not self.browser_controller.okx_wallet_connect():
                                self.browser_controller.okx_wallet_connect()
                        else:
                            try_click(ai_tab, "x://div[contains(text(),'OKX Wallet')]")
                            self.browser_controller.okx_wallet_connect()
                            try_click(
                                ai_tab,
                                "x://button[normalize-space()='发送消息' or normalize-space()='Sign message'  ]",
                            )
                            if not self.browser_controller.okx_wallet_connect():
                                self.browser_controller.okx_wallet_connect()
                        for i in range(7):
                            # try_click(ai_tab, "x://div[normalize-space()='What is Uniswap v4?']")
                            logger.info(f"{self.id} 开始ai 答题")
                            input_text(ai_tab, "x://input[@placeholder='Start searching here!']", "What is Uniswap v4")
                            try_click(ai_tab, "x://button[normalize-space()='Search']")

                            sleep(5)
                    ai_tab.close()
                    return True
            else:
                ai_ele = get_element(tab, "x://span[contains(text(),'Resets ')]")
                logger.info(f"{self.id} ai_task_daily 已经完成，还需等待{ai_ele.raw_text}")
                return True

        except Exception:
            raise Exception("ai_task_daily, triggering retry.")

    @retry(tries=3, delay=3)
    def fake_task(self):
        # self.login()
        try:
            logger.info(f"{self.id} 开始完成点击任务")
            tab = self.page.latest_tab
            address = self.browser_controller.browser_config.evm_address

            register = self._get_registration(address)
            if register:
                # 兼容历史遗留数据
                if register.get("fake_task") == "1":
                    logger.info(f"【{self.id}】钱包 {address} fake_task,fake_task已经完成")
                    return True
            # self.page.close_tabs(tab, others=True)
            if not tab.url == "https://speedrun.enso.build/apps":
                logger.info("切换tab,apps点击任务")
                #    sleep(10)
                tab = self.page.new_tab("https://speedrun.enso.build/apps")

            for i in range(7):
                # task_link = get_elements(tab, "x://a[contains(@href, 'https') and @class='w-full' ]", 10)
                if i > 0:
                    try_click(tab, f"x://li[@title={i + 1}]", 10)
                    logger.info(f"{self.id} 开始点击 第{i + 1} 页")
                    sleep(3)
                task_link = tab.eles("x://a[contains(@href, 'https') and @class='w-full' ]")
                for j, ele in enumerate(task_link):
                    # time.sleep(1.1)  # 等待 0.3 秒

                    ele.wait(1.6, 1.8)
                    if ele.wait.has_rect(timeout=30):
                        tab_temp = ele.click.for_new_tab()
                        sleep(1)
                        tab_temp.close()
                    else:
                        logger.error(f"【{self.id}】 元素没有位置和大小")
                sleep(2)
                # self.page.close_tabs(tab, others=True)
            # sleep(2)
            register["fake_task"] = 1
            self._save_registration(register)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】 {str(e)}")
            raise Exception("fake_task, triggering retry.")

    def add_wal(self, tab):
        # self.page.close_tabs(tab, others=True)

        if "questboard" not in tab.url:
            logger.info(f"{self.id}切换tab,add_wal")
            #    sleep(10)
            self.page.latest_tab.get(
                url="https://zealy.io/cw/enso/questboard/ed7a48b8-c972-4098-841e-21b6d6f44fca/27c7d639-ef02-491d-9dba-d070cfd6b794"
            )
        if get_element(tab, "x://button[normalize-space()='Connect wallet']", timeout=5):
            try_click(tab, "x://button[normalize-space()='Connect wallet']")
            try_click(tab, "x://div[contains(text(),'OKX Wallet')]")
            self.browser_controller.okx_wallet_connect()
            try_click(tab, "x://button[normalize-space()='发送消息' or normalize-space()='Sign message'  ]")
            if not self.browser_controller.okx_wallet_connect():
                self.browser_controller.okx_wallet_connect()

        if get_element(tab, "x://button[normalize-space()='Connect wallet']", timeout=5):
            return False
        return True

    def add_dc(self, lasted_tab):
        check_dc = get_element(lasted_tab, "x://button[normalize-space()='Connect Discord']", timeout=5)
        if check_dc:
            try_click(lasted_tab, "x://button[normalize-space()='Connect Discord']", timeout=5)

            tab = lasted_tab.wait.url_change(text="https://discord.com/login", timeout=20)
            if tab:
                self.browser_controller.login_discord()
            lasted_tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=20)
            lasted_tab.set.window.max()
            lasted_tab.wait.ele_displayed("x:(//button)[2]", timeout=20)
            allow_btn = get_element(lasted_tab, "x:(//button)[2]", 5)
            if allow_btn:
                allow_btn.click()
                sleep(10)
            sleep(10)

        check_dc = get_element(lasted_tab, "x://button[normalize-space()='Connect Discord']", timeout=5)
        if check_dc:
            return False
        return True

    def add_x(self, lasted_tab):
        check_x = lasted_tab.ele("x://button[normalize-space()='Connect Twitter']", timeout=5)
        if check_x:
            # try_click(latest_tab, check_x.next(), timeout=5)
            logger.info(f"{self.id} 开始绑定x")
            check_x.click()

            # tab = lasted_tab.wait.url_change(text="i/flow/login", timeout=10)
            # if tab:
            #     self.browser_controller.login_x_with_auth()

            lasted_tab.wait.url_change(text="oauth/authorize", timeout=10)

            allow_btn = get_element(lasted_tab, "x://input[@id='allow']", 5)
            if not allow_btn:
                logger.error(f"【{self.id}】未找到Twitter授权按钮")
                return False

            allow_btn.click()
            sleep(3)
            tab = lasted_tab.wait.url_change(text="i/flow/login", timeout=10)
            if tab:
                self.browser_controller.login_x_with_auth()
                # lasted_tab.wait.ele_displayed("x://input[@id='allow']", timeout=10)
                lasted_tab.wait.url_change(text="oauth/authorize", timeout=10)

                allow_btn = get_element(lasted_tab, "x://input[@id='allow']", 5)
                if not allow_btn:
                    logger.error(f"【{self.id}】未找到Twitter授权按钮")
                    return False

                allow_btn.click()
                sleep(3)
            lasted_tab = lasted_tab.wait.url_change(text="zealy.io/cw/enso/questboard", timeout=20)
            if not lasted_tab:
                logger.error(f"【{self.id}】 Twitter绑定失败")
                return False
        check_x = lasted_tab.ele("x://button[normalize-space()='Connect Twitter']", timeout=3)
        if check_x:
            # try_click(latest_tab, check_x.next(), timeout=5)
            logger.error(f"{self.id} 绑定x失败")
            return False
        return True

    def add_zealy(self, lasted_tab):
        check_x = lasted_tab.ele("x://button[normalize-space()='Connect Zealy']", timeout=5)
        if check_x:
            # try_click(latest_tab, check_x.next(), timeout=5)
            logger.info(f"{self.id} 开始绑定Zealy")
            check_x.click()
            try_click(lasted_tab, "x://button[normalize-space()='Claim']", timeout=60)

            if get_element(lasted_tab, "//span[normalize-space()='Verifying with Zealy Connect']", timeout=20):
                return False
            return True
        return True

    def _follow_x(self, lasted_tab):
        check_x = lasted_tab.ele("x://button[normalize-space()='Follow']", timeout=5)
        if check_x:
            # try_click(latest_tab, check_x.next(), timeout=5)
            logger.info(f"{self.id} 开始绑定x")
            x_tab = check_x.click.for_new_tab()
            try_click(x_tab, "x://button[normalize-space()='Follow @EnsoBuild']")

            sleep(3)
            x_tab.close()
            try_click(x_tab, "x://button[normalize-space()='Claim']")

            if not lasted_tab:
                logger.error(f"【{self.id}】 Twitter绑定失败3")
                return False
        check_x = lasted_tab.ele("x://span[normalize-space()='No X account linked']", timeout=3)
        if check_x:
            # try_click(latest_tab, check_x.next(), timeout=5)
            logger.error(f"{self.id} 绑定x失败")
        return True

    def soci_bind(self):
        """处理社交任务"""
        # self.login()
        tab = self.page.latest_tab
        address = self.browser_controller.browser_config.evm_address

        register = self._get_registration(address)
        if register:
            # 兼容历史遗留数据
            if register.get("soci_bind") == "1":
                logger.info(f"【{self.id}】钱包 {address} soci_bind,soci_bind")
                return True

        success_wal = self.add_wal(tab)
        success_dc = self.add_dc(tab)
        success_x = self.add_x(tab)
        success_zealy = self.add_zealy(tab)

        # 判断是否全部成功
        if success_wal and success_dc and success_x and success_zealy:
            # if success_zealy:
            register["soci_bind"] = 0
            self._save_registration(register)
            return True
        else:
            logger.info(f"{self.id} 绑定失败一个")

            register["soci_bind"] = 0
            self._save_registration(register)
            return False

    def soci_task(self, tab):
        address = self.browser_controller.browser_config.evm_address

        register = self._get_registration(address)
        if register:
            # 兼容历史遗留数据
            if register.get("soci_task") == "1":
                logger.info(f"【{self.id}】钱包 {address} soci_task,soci_task")
                return True
        self._follow_x(tab)
        register["soci_task"] = 1
        self._save_registration(register)
        # 检查任务状态

    # tab.refresh()
    #     mission_statuses = {
    #     2: False,  # 任务 2
    #     3: False,  # 任务 3
    #     4: False,  # 任务 4
    #     11: False  # 任务 11
    # }

    def _link(self, tab):
        address = self.browser_controller.browser_config.evm_address
        link_btn = get_element(tab, "x://button[@data-test-id='hero-cta-link-dashboard-account']", 5)

        if link_btn:
            logger.info(f"【{self.id}】开始绑定MetaMask开发者账号")
            link_btn.click()
            sleep(3)

            record = self.csv.query({"address": address})
            if not record:
                raise Exception(f"【{self.id}】 {address}未注册开发者账号，请先注册")

            form_fields = {
                "x://input[@id='email']": record[0]["email"],
                "x://input[@id='password']": record[0]["password"],
            }

            for xpath, value in form_fields.items():
                if not self._input_field(tab, xpath, value):
                    raise Exception(f"填写表单字段失败: {xpath}")

            if not self._click_element(tab, "x://button[@data-testid='auth-button']"):
                raise Exception("点击Link按钮失败")
            sleep(3)

            if not self._check_cf_shield2(tab, "docs.metamask.io/developer-tools/faucet"):
                raise Exception("过CF盾失败")
            sleep(3)
            logger.success(f"【{self.id}】绑定开发者账号成功")

        return True

    def is_login(self, tab):
        try:
            if get_element(tab, "x://button[@data-testid='navbar-account-toggle']", 5):
                return True
            return False
        except Exception as e:
            logger.error(f"【{self.id}】 {str(e)}")
            return False

    def _faucet(self, tab):
        try:
            max_retries = 3
            eles = get_elements(tab, "x://ul[@role='tablist']//li", 3)
            for index in range(len(eles)):
                retry = 1
                while retry <= max_retries:
                    eles[index].click()
                    sleep(3)
                    chain = eles[index].text
                    logger.info(f"【{self.id}】开始领取 {chain}")
                    try:
                        tab.listen.start("developer.metamask.io/api/faucets/")
                        address = self.browser_controller.browser_config.evm_address
                        input_i = get_element(tab, f"x:(//input[@type='text'])[{index + 1}]", 3)
                        input_i.clear(True)
                        input_i.input(address)
                        sleep(3)

                        get_element(tab, f"x:(//button[@data-test-id='hero-cta-request-eth'])[{index + 1}]", 5).click()
                        res = tab.listen.wait(timeout=10)
                        if res:
                            response_data = res.response.body
                            if isinstance(response_data, dict):
                                if "error" in response_data:
                                    error_msg = response_data["error"].get("message", "未知错误")
                                    logger.warning(f"【{self.id}】领取 {chain} 失败: {error_msg}")
                                elif "txnHash" in response_data:
                                    logger.success(
                                        f"【{self.id}】领取 {chain} 成功，获得 {response_data.get('value', '0')}ETH"
                                    )
                            break
                        else:
                            logger.error(f"【{self.id}】未收到响应")
                    except Exception as e:
                        logger.error(f"【{self.id}】 领取 {eles[index].text} 异常: {str(e)}")
                    retry += 1
                sleep(3)
            return True
        except Exception as e:
            logger.error(f"【{self.id}】领水异常: {str(e)}")
            return False

    def task(self, type: str):
        start_time = time.time()  # Record start time
        try:
            address = self.browser_controller.browser_config.evm_address
            self.browser_controller.okx_wallet_login()
            tab = self.page.latest_tab
            tab.set.window.max()
            if "https://speedrun.enso.build" not in tab.url:
                logger.info(f"{self.id} 打开项目页面")
                tab = self.page.new_tab("https://speedrun.enso.build")
                for i in range(3):
                    if get_element(tab, "x://a[contains(@href, '/categories/ai') ]", timeout=10):
                        break
                    tab.refresh()
            if not self.register(type, tab):
                logger.error(f"【{self.id}】注册失败，请手动处理")
                return False
            logger.info(f"【{self.id}】已有账号，开始做任务")

            tab.set.window.max()
            result = self.soci_bind()
            if result:
                # result = self.fake_task()
                result = self.defi_task_daily()
                # result = self.soci_task(tab)
                result = self.ai_task_daily()
            if result:
                try:
                    #  pass
                    self.page.quit()
                except Exception as e:
                    logger.error(f"【{self.id}】关闭页面失败: {e}")
            else:
                logger.error(f"【{self.id}】任务失败")
                self.page.quit()
            end_time = time.time()  # Record end time
            elapsed_time = end_time - start_time  # Calculate elapsed time in seconds
            logger.info(f"【{self.id}】任务执行耗时: {elapsed_time:.2f}秒")
            return result
        except Exception as e:
            logger.error(f"【{self.id}】执行任务发生异常，error={str(e)}")
            self.page.quit()


def _run_task(type, index):
    try:
        faucet = Enso(type, str(index))
        faucet.task(type)
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表
        failed_indices = []  # 新增失败列表

        def process_task(index):
            try:
                faucet = Enso(type, str(index))
                result = faucet.task(type)
                if result:
                    successful_indices.append(index)  # 将成功的索引添加到成功列表
                    # faucet.page.quit()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)  # 将失败的索引添加到失败列表
                # faucet.page.quit()
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"faucet_monai-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]  # 不需要strip()，因为str()转换的数字不会有空格
        logger.info(f"任务执行结果: {results}")
        logger.info(f"成功的账号列表: {successful_indices}")  # 输出成功的账号列表
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/enso/enso.py run -t bit -i 1-10
# python3 examples/enso/enso.py run   -t brave -i 1-10

if __name__ == "__main__":
    cli()
#
#  _run_task(BrowserType.BRAVE, 89)
