import re
from datetime import datetime, timedelta

from loguru import logger


def _get_email_verify_code(email, email_pwd):
    try:
        from src.emails.imap4.email_client import EmailClient, SearchCriteria

        email_client = EmailClient(email, email_pwd)
        # 计算10分钟前的时间
        ten_minutes_ago = datetime.now() - timedelta(minutes=1)
        # 格式化为 DD-MMM-YYYY 格式
        since_date = ten_minutes_ago.strftime("%d-%b-%Y")

        search_criteria = SearchCriteria(
            subject="Your temporary Zealy login code is",
            from_addr="zealy.io",
            sort_order="DESC",  # 按时间倒序排序
        )

        emails = email_client.search_emails_with_retry(search_criteria)
        if not emails:
            logger.error(" 未找到验证码邮件")
            return None

        email = emails[0]

        pattern = r"is\s+([A-Za-z0-9]{6})"
        logger.info(email["subject"])
        # 查找匹配
        match = re.search(pattern, email["subject"])
        if match:
            logger.info(match.group(1))
            return match.group(1)  # 返回第一个捕获组（链接内容）
        return None
    except Exception as e:
        logger.error(f" 获取验证链接失败: {e}")
        return None


if __name__ == "__main__":
    _get_email_verify_code(
        "<EMAIL>",
        "M.C513_BL2.0.U.-Cm6NjZbeSjOds11LOnW*PLCGHibonJf0XeM1jhCaGBD*R7mTQh!wd0NW0J19oLKKoTAqC6GLztotUucIcX5pNNxc!AuTqO80g0!64OMLx4!N6RHqfAo**CQ9Dw3flYFXvB5OL8yeTwTCUu2GOjnc9KenzQuggB4wZ6KWsYVIH2TMwBVGXW6dzdqbnkhb3yOSGFef17C5qNob36wAOrQ6PBATdaaQWFDQVO3XWOlRZ1INW*ZQI!2XFYfbHn8IM1Pkp6x5Xc4ZU69mkH3yrlsZjwrZ3K4pcu40j*aypV0ExE1f5qEmF2WWuErF2Ad19qMB*RP96uZ2i7MLs7H0WV3mZ497vGL9c0c7Q3Hd60p9VTtFbB6pxikq!P!CFbI!R*pdU5DAg2zYij7IKx4WHmxlOEc$---9e5f94bc-e8a4-4e73-b8be-63364c29d753",
    )
