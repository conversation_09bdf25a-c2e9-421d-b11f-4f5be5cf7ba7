import os
from enum import Enum

# 默认配置
DEFAULT_BROWSER_TYPE = "BRAVE"
DEFAULT_TIMEOUT = 30
DEFAULT_RETRY_COUNT = 3
DEFAULT_RETRY_DELAY = 3

# 文件路径配置
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_DIR = os.path.join(PROJECT_ROOT, "examples", "enso")
CSV_PATH = os.path.join(DATA_DIR, "enso.csv")

# 日志配置
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
LOG_FILE = os.path.join(LOG_DIR, "enso.log")
LOG_ROTATION = "10MB"
LOG_LEVEL = "SUCCESS"

# 任务配置
TASK_TIMEOUT = 6 * 3600  # 6小时
TASK_RETRIES = 3
TASK_INTERVAL = 10
