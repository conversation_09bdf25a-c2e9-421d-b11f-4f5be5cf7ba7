import os
import random
import re
import string
import sys
from datetime import datetime
from time import sleep
from typing import Optional

from config_enso import DEFAULT_TIMEOUT
from data_manager import DataManager
from faker import Faker
from loguru import logger
from retry import retry

from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils.element_util import get_element, get_elements, get_frame
from src.utils.password import generate_random_string
from utils import measure_time


class EnsoTask:
    """任务管理类，用于处理具体任务"""

    def __init__(self, browser_type: BrowserType, id: str):
        """初始化任务管理器

        Args:
            browser_type: 浏览器类型
            id: 任务ID
        """
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.data_manager = DataManager("enso")  # 使用enso项目的数据管理器
        self.discord_bind_failed = False  # 记录Discord绑定失败状态
        self.twitter_bind_failed = False  # 记录Twitter绑定失败状态
        self.pid = os.getpid()  # 获取进程ID

        # 初始化带有任务ID的logger
        # self.logger = logger.bind(task_id=f"【PID:{self.pid}】【{self.id}】")
        self.logger = logger.bind(task_id=f"【PID:{self.pid}】【{self.id}】")

    @measure_time
    @retry(tries=3, delay=3)
    def register(self, type: str) -> bool:
        """注册任务

        Args:
            type: 注册类型

        Returns
        -------
            bool: 是否注册成功
        """
        try:
            address = self.browser_controller.browser_config.evm_address
            registration = self.data_manager.get_registration(address)
            if registration and registration.get("status") in ["0", "1"]:
                self.logger.info(f"钱包 {address} 已注册,跳过注册流程")
                return True
            tab = self.page.latest_tab

            # 打开任务页面
            self.logger.info(f"当前页面: {tab.url}")

            # 检查当前页面是否是插件页面
            tab = self.page.new_tab("https://speedrun.enso.build")
            # self.page.close_tabs(tab, others=True)
            tab.set.window.max()
            # 等待页面加载完成

            if not get_element(tab, "x://a[contains(@href, '/categories/ai') ]", timeout=30):
                raise Exception(f"{self.id} 页面未正常加载")

            # 检查是否已注册

            # 获取邮箱信息
            email_name = self.browser_controller.browser_config.email
            proxy_email = self.browser_controller.browser_config.proxy_email
            email = proxy_email or email_name

            # 准备注册信息
            registration = {"index": self.id, "type": type, "address": address, "email": email, "status": "0"}
            # 注册前保存用户信息
            self.data_manager.save_registration(registration)
            # 执行注册流程
            if not self._execute_registration(tab, registration):
                return False
            registration["status"] = "1"  # 是否需要重发邮件状态
            self.soci_bind()
            # 保存注册信息
            self.data_manager.save_registration(registration)
            return True

        except Exception as e:
            self.logger.error(f"注册失败: {str(e)}")
            return False

    @retry(tries=3, delay=3)
    def login(self, tab) -> bool:
        sleep(1)
        self.logger.info(f"login {tab.url}")
        check_ele = get_element(tab, "x://button[normalize-space()='Check my bonus']", timeout=20)
        if not check_ele:
            ai_btn = get_element(tab, "x://a[contains(@href, '/categories/ai')]", timeout=10)
            if not ai_btn:
                tab.refresh()
                raise Exception("login 未找到AI分类按钮")
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect']")
            if not connect_btn:
                tab.refresh()
                raise Exception("未找到登录按钮")
            connect_btn.click()

        check_ele.click()

        # 检查是否已完成注册
        done_ele = get_element(tab, "x://button[normalize-space()='Done']", timeout=3)
        if done_ele:
            done_ele.click()
            return True

        if get_element(
            tab, "x://button[normalize-space()='Connect wallet' or normalize-space()='Sign message'  ]", timeout=5
        ):
            try_click(tab, "x://button[normalize-space()='Connect wallet']")
            try_click(tab, "x://div[contains(text(),'OKX Wallet')]")
            self.browser_controller.okx_wallet_connect()
            try_click(tab, "x://button[normalize-space()='发送消息' or normalize-space()='Sign message'  ]")
            if not self.browser_controller.okx_wallet_connect():
                self.browser_controller.okx_wallet_connect()

        zealy_ele = get_element(tab, "x://span[contains(text(),'Connect Zealy')]", timeout=10)
        # zealy，当时注册后，绑定社交账号失败的
        if zealy_ele:
            new_tab = zealy_ele.click.for_new_tab()
            if not self.soci_bind():
                self.logger.info("绑定社交账号失败")
                return False
            return True
        else:
            tab.refresh()
            return False

    def score_task(self, res):
        try:
            if not res or not hasattr(res, "response") or not res.response.body:
                self.logger.error("无效的响应数据")
                return None

            response_data = res.response.body
            if not isinstance(response_data, dict):
                self.logger.error(f"响应数据格式错误: {response_data}")
                return None

            # 提取用户信息
            connected_wallet = response_data.get("connectedWallet", "")
            discord_id = response_data.get("discordId", "")
            twitter_id = response_data.get("twitterId", "")
            xp = response_data.get("xp", 0)
            rank = response_data.get("rank", 0)
            level = response_data.get("level", 0)

            # 获取当前数据
            address = self.browser_controller.browser_config.evm_address
            current_data = self.data_manager.get(address) or {}

            # 保存上一次的数据
            last_xp = current_data.get("xp", 0)
            last_rank = current_data.get("rank", 0)
            last_level = current_data.get("level", 0)

            # 更新数据
            self.data_manager.update(
                address,
                {
                    "xp": xp,
                    "rank": rank,
                    "level": level,
                    "last_xp": last_xp,
                    "last_rank": last_rank,
                    "last_level": last_level,
                    "xp_date_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                },
            )

            # 记录用户信息
            self.logger.info(
                f"用户信息: 钱包:{connected_wallet} Discord:{discord_id} Twitter:{twitter_id} XP:{xp} 等级:{level} 排名:{rank}",
            )
            return {
                "connected_wallet": connected_wallet,
                "discord_id": discord_id,
                "twitter_id": twitter_id,
                "xp": xp,
                "rank": rank,
                "level": level,
            }
        except Exception as e:
            self.logger.error(f"处理用户信息失败: {str(e)}")
            return None

    def execute_tasks(self) -> bool:
        """执行所有任务

        Returns
        -------
            bool: 是否所有任务都执行成功
        """
        tab = None
        try:
            self.logger.info("开始执行任务")

            tab = self.page.latest_tab

            tab = self.page.new_tab()
            self.browser_controller.window_max()
            # self.browser_controller.close_other_tabs(tab)
            # 开始监听
            tab.listen.start("speedrun.enso.build/api/zealy/user/")
            tab.get(url="https://speedrun.enso.build")

            # 打开主页
            self.logger.info(f"打开主页{tab.url}")
            address = self.browser_controller.browser_config.evm_address
            registration = self.data_manager.get_registration(address)
            res = tab.listen.wait(count=1, timeout=10)
            result = None

            # 尝试获取用户信息
            if res and hasattr(res, "response"):
                result = self.score_task(res)
            elif self.login(tab):
                tab.refresh()
                res = tab.listen.wait(count=1, timeout=10)
                if res and hasattr(res, "response"):
                    result = self.score_task(res)
            else:  # 空的，登录系统
                self.logger.error("登录失败")
                return False

            # 检查是否需要注册和绑定
            need_registration = False
            if result is None:
                need_registration = True
                self.logger.info("未获取到用户信息，需要注册")
            elif result.get("twitter_id") is None or result.get("discord_id") is None:
                need_registration = True
                self.logger.info("社交账号未绑定，需要注册")

            if need_registration:
                self.soci_bind()
                # 如果未绑定，执行Zealy注册和社交绑定
                self.logger.info("开始执行注册流程")
                self.logger.info(f"注册信息: {registration}")
                if not self._execute_zealy_connection(tab, registration):
                    raise Exception("Zealy registration failed")
            # self.fake_task()
            # 执行AI任务
            # if not self.ai_task_daily(tab):
            #     logger.error(f"【{self.id}】AI任务失败")
            #     return False

            # 执行DeFi任务
            if not self.defi_task_daily(tab):
                self.logger.error("DeFi任务失败")
                return False
            res = tab.listen.wait(count=1, timeout=10)
            # 尝试获取用户信息
            if res and hasattr(res, "response"):
                result = self.score_task(res)
            else:
                tab.refresh()
                res = tab.listen.wait(count=1, timeout=10)
                if res and hasattr(res, "response"):
                    result = self.score_task(res)

            self.logger.success("所有任务执行完成")
            return True

        except Exception as e:
            self.logger.error(f"执行任务失败: {str(e)}")
            return False
        finally:
            try:
                if tab:
                    tab.close()
                self.browser_controller.close_page()
            except Exception as e:
                self.logger.debug(f"关闭浏览器失败: {str(e)}")

    def _execute_registration(self, tab, registration: dict) -> bool:
        """执行注册流程

        Args:
            tab: 浏览器标签页
            registration: 注册信息

        Returns
        -------
            bool: 是否注册成功
        """
        try:
            # 点击注册按钮
            check_ele = get_element(tab, "x://button[normalize-space()='Check my bonus']", timeout=10)
            if not check_ele:
                self.logger.error("未找到注册按钮")
                return False
            check_ele.click()

            # 检查是否已完成注册
            done_ele = get_element(tab, "x://button[normalize-space()='Done']", timeout=5)
            if done_ele:
                done_ele.click()
                return True

            # 连接钱包
            if not self._connect_wallet(tab):
                return False
            done_ele = get_element(tab, "x://button[normalize-space()='Done']", timeout=5)
            if done_ele:
                done_ele.click()
                return True
            # 连接Zealy
            if not self._connect_zealy(tab, registration):
                return False

            return True

        except Exception as e:
            self.logger.error(f"执行注册流程失败: {str(e)}")
            return False

    def _connect_wallet(self, tab) -> bool:
        """连接钱包

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否连接成功
        """
        try:
            if not get_element(tab, "x://div[contains(text(), 'signed in')]", timeout=5):
                # 点击连接钱包按钮
                connect_btn = get_element(tab, "x://button[@aria-label='Connect to Connect wallet']", timeout=5)
                if not connect_btn:
                    return False
                connect_btn.click()
                sleep(2)

                # 选择OKX钱包
                okx_btn = get_element(tab, "x://div[contains(text(),'OKX Wallet')]", timeout=5)
                if not okx_btn:
                    return False
                okx_btn.click()
                sleep(2)

                # 连接钱包
                self.browser_controller.okx_wallet_connect()

                # 点击签名按钮
                sign_btn = get_element(tab, "x://button[contains(text(),'Sign message')]", timeout=10)
                if not sign_btn:
                    return False
                sign_btn.click()
                sleep(2)

                # 再次连接钱包
                result = self.browser_controller.okx_wallet_connect()
                self.logger.info(f"连接钱包成功{result}")

            return True
        except Exception as e:
            self.logger.error(f"连接钱包失败: {str(e)}")
            return False

    def _connect_zealy(self, tab, registration: dict) -> bool:
        """连接Zealy

        Args:
            tab: 浏览器标签页
            registration: 注册信息

        Returns
        -------
            bool: 是否连接成功
        """
        try:
            # 点击连接Zealy按钮
            zealy_ele = get_element(tab, "x://span[contains(text(),'Connect Zealy')]", timeout=DEFAULT_TIMEOUT)
            if not zealy_ele:
                return False

            new_tab = zealy_ele.click.for_new_tab()
            if not new_tab:
                return False

            # 执行Zealy连接流程
            if not self._execute_zealy_connection(new_tab, registration):
                return False

            return True

        except Exception as e:
            self.logger.error(f"连接Zealy失败: {str(e)}")
            return False

    def _execute_zealy_connection(self, tab, registration: dict) -> bool:
        """执行Zealy连接流程

        Args:
            tab: 浏览器标签页
            registration: 注册信息

        Returns
        -------
            bool: 是否连接成功
        """
        try:
            # 点击连接按钮
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect to Zealy']", timeout=10)
            if not connect_btn:
                return False
            connect_btn.click()
            sleep(2)

            # 输入邮箱
            email_input = get_element(tab, "x://input[@id='email']", timeout=10)
            if not email_input:
                return False
            email_input.clear(True)
            email_input.input(registration["email"])
            sleep(2)

            # 点击继续按钮
            continue_btn = get_element(tab, "x://button[normalize-space()='Continue with email']", timeout=10)
            if not continue_btn:
                return False
            continue_btn.click()
            sleep(2)
            self._check_cf_shield1(tab)
            # 处理验证码
            if not self._handle_verification_code(tab, registration):
                return False
            faker = Faker()
            first_name = faker.first_name()
            last_name = faker.last_name()
            # 生成2-4位随机数字
            random_digits = "".join(random.choices(string.digits, k=random.randint(2, 4)))
            # 输入用户名
            name_input = get_element(tab, "x://input[@id='name']", timeout=10)
            if not name_input:
                return False
            name_input.clear(True)
            name_input.input(first_name + last_name + random_digits)
            sleep(2)

            # 点击下一步
            next_btn = get_element(tab, "x://button[@aria-label='Next']", timeout=10)
            if next_btn:
                next_btn.click()

            # 增加判断用户名 注册成功判断
            sleep(2)
            # 这个元素可能跳，可能不跳
            if get_element(tab, "x://button[normalize-space()='Get started']", timeout=10):
                try_click(tab, "x://button[normalize-space()='Get started']")
            if tab.wait.url_change(text="https://zealy.io/cw/enso/questboard", timeout=10):
                return True
            return False
        except Exception as e:
            self.logger.error(f"执行Zealy连接流程失败: {str(e)}")
            return False

    def _handle_verification_code(self, tab, registration: dict) -> bool:
        """处理验证码

        Args:
            tab: 浏览器标签页
            registration: 注册信息

        Returns
        -------
            bool: 是否处理成功
        """
        try:
            # 获取验证码
            code = self._get_email_verify_code(
                self.browser_controller.browser_config.email,
                self.browser_controller.browser_config.email_password,
                self.browser_controller.browser_config.proxy_email,
            )

            if not code:
                return False

            # 输入验证码
            code_input = get_element(tab, "x://input[@autocomplete='one-time-code']", timeout=10)
            if not code_input:
                return False
            code_input.clear(True)
            code_input.input(code)
            sleep(2)
            if not tab.wait.url_change(text="https://zealy.io/create-user", timeout=10):
                self.logger.info(f"{self.id} 验证码失效或错误")
                return False
            return True

        except Exception as e:
            self.logger.error(f"处理验证码失败: {str(e)}")
            return False

    def _get_email_verify_code(self, email: str, email_pwd: str, proxy_email: str | None = None) -> str | None:
        """获取邮箱验证码

        Args:
            email: 邮箱
            email_pwd: 邮箱密码
            proxy_email: 代理邮箱

        Returns
        -------
            Optional[str]: 验证码，如果获取失败则返回None
        """
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            # 获取今天的日期

            search_criteria = SearchCriteria(
                subject="Your temporary Zealy login code is",
                to=proxy_email or email,
                from_addr="zealy.io",
                sort_order="DESC",  # 按时间倒序排序
            )

            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                self.logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            pattern = r"is\s+([A-Za-z0-9]{6})"
            match = re.search(pattern, email["subject"])

            return match.group(1) if match else None

        except Exception as e:
            self.logger.error(f"{self.id} 获取验证码失败: {str(e)}")
            return None

    @retry(tries=3, delay=3)
    def soci_bind(self) -> bool:
        """处理社交绑定任务

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            tab = self.page.latest_tab
            if (
                "https://zealy.io/cw/enso/questboard/ed7a48b8-c972-4098-841e-21b6d6f44fca/27c7d639-ef02-491d-9dba-d070cfd6b794"
                not in tab.url
            ):
                tab.get(
                    url="https://zealy.io/cw/enso/questboard/ed7a48b8-c972-4098-841e-21b6d6f44fca/27c7d639-ef02-491d-9dba-d070cfd6b794"
                )
            address = self.browser_controller.browser_config.evm_address

            # 检查是否已完成绑定
            registration = self.data_manager.get_registration(address)
            self.logger.info(f"注册信息 {registration}")
            # if registration and registration.get("soci_bind") == "1":
            #     logger.info(f"【{self.id}】钱包 {address} 已完成社交绑定")
            #     return True
            # tab.listen.start("https://api-v2.zealy.io/api/users/edd61f0c-6203-4eb4-9c46-a38e08d70fef")
            # tab.refresh()
            # res = tab.listen.wait(timeout=10)
            # if not res:
            #     logger.error(f"【{self.id}】 未收到响应")
            #     return False

            # response_data = res.response.body
            sleep(4)
            self._execute_zealy_connection(tab, registration)
            # 绑定钱包
            if not self._bind_wallet(tab):
                return False

            # 绑定Discord
            if not self._bind_discord(tab):
                return False

            # 绑定Twitter
            if not self._bind_twitter(tab):
                return False

            # 绑定Zealy
            if not self._bind_zealy(tab):
                return False
            if "https://zealy.io" in tab.url:
                tab.close()
            # 更新绑定状态
            registration["soci_bind"] = "1"
            self.logger.info("社交绑定成功")
            self.data_manager.save_registration(registration)
            return True

        except Exception as e:
            self.logger.error(f"社交绑定失败: {str(e)}")
            return False

    def _bind_wallet(self, tab) -> bool:
        """绑定钱包

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            address = self.browser_controller.browser_config.evm_address
            registration = self.data_manager.get_registration(address)

            connect_btn = get_element(tab, "x://button[normalize-space()='Connect wallet']", timeout=5)
            if not connect_btn:
                return True

            connect_btn.click()
            sleep(2)

            okx_btn = get_element(tab, "x://div[contains(text(),'OKX Wallet')]", timeout=5)
            if okx_btn:
                okx_btn.click()
            sleep(2)
            # 可能出现 可能不出现
            self.browser_controller.okx_wallet_connect()

            sign_btn = get_element(
                tab, "x://button[normalize-space()='发送消息' or normalize-space()='Sign message']", timeout=5
            )
            if sign_btn:
                sign_btn.click()
            sleep(2)

            return self.browser_controller.okx_wallet_connect()

        except Exception as e:
            self.logger.error(f"绑定钱包失败: {str(e)}")
            return False

    def _bind_discord(self, tab) -> bool:
        """绑定Discord

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect Discord']", timeout=5)
            if not connect_btn:
                return True

            connect_btn.click()
            sleep(2)

            new_tab = tab.wait.url_change(text="https://discord.com/login", timeout=10)
            if new_tab:
                result = self.browser_controller.login_discord()
                self.logger.info(f"登录Discord结果: {result}")
                if not result:
                    self.discord_bind_failed = True
                    return False
                else:
                    raise Exception(f"【{self.id}】重新绑定x")
            tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=10)

            scroll_ele = get_element(tab, "x://div[@dir='ltr' and @style='overflow: hidden scroll;']", timeout=10)
            if scroll_ele:
                scroll_ele.scroll.down(500)
                sleep(0.5)
            allow_btn = get_element(tab, "x:(//button)[2]", timeout=10)
            # allow_btn.scroll.down(00)
            if not allow_btn:
                self.discord_bind_failed = True
                return False
            allow_btn.click()
            sleep(10)

            return True

        except Exception as e:
            self.logger.error(f"绑定Discord失败: {str(e)}")
            self.discord_bind_failed = True
            return False

    def _bind_twitter(self, tab) -> bool:
        """绑定Twitter

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            self.logger.info(f"{self.id} 开始执绑定")
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect Twitter']", timeout=5)
            if not connect_btn:
                return True

            connect_btn.click()
            sleep(2)

            tab.wait.url_change(text="oauth/authorize", timeout=10)
            i = 0
            while True:
                i = i + 1
                current_url = tab.url
                if "oauth/authorize" in current_url:
                    allow_btn = get_element(tab, "x://input[@id='allow']", timeout=5)
                    if allow_btn:
                        allow_btn.click()
                    sleep(3)
                elif "i/flow/login" in current_url or "/account/access" in current_url:
                    self.twitter_bind_failed = True
                    self.logger.error(f"【{self.id}】绑定Twitter失败,需要登录")
                    return False

                sleep(1)  # 每秒检查一次
                if i == 10:
                    self.twitter_bind_failed = True
                    break
            if not tab.wait.url_change(text="zealy.io/cw/enso/questboard", timeout=20):
                self.twitter_bind_failed = True
                return False

            return True

        except Exception as e:
            self.logger.error(f"绑定Twitter失败: {str(e)}")
            self.twitter_bind_failed = True
            return False

    def _bind_zealy(self, tab) -> bool:
        """绑定Zealy

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect Zealy']", timeout=5)
            if not connect_btn:
                return True

            connect_btn.click()
            sleep(2)

            claim_btn = get_element(tab, "x://button[normalize-space()='Claim']", timeout=60)
            if not claim_btn:
                return False
            claim_btn.click()
            sleep(2)

            if get_element(tab, "x://span[normalize-space()='Verifying with Zealy Connect']", timeout=20):
                return False

            return True

        except Exception as e:
            self.logger.error(f"绑定Zealy失败: {str(e)}")
            return False

    def fake_task(self) -> bool:
        """执行假任务

        Returns
        -------
            bool: 是否执行成功
        """
        try:
            self.logger.info(f"{self.id} 开始完成点击任务")
            tab = self.page.latest_tab
            address = self.browser_controller.browser_config.evm_address

            # 检查是否已完成任务
            registration = self.data_manager.get_registration(address)
            if registration and registration.get("fake_task") == "1":
                self.logger.info(f"【{self.id}】钱包 {address} 已完成假任务")
                return True

            # 打开任务页面
            if not tab.url == "https://speedrun.enso.build/apps":
                tab = self.page.new_tab("https://speedrun.enso.build/apps")

            # 执行点击任务
            for i in range(7):
                if i > 0:
                    page_btn = get_element(tab, f"x://li[@title={i + 1}]", timeout=10)
                    if not page_btn:
                        continue
                    page_btn.click()
                    sleep(3)

                task_links = tab.eles("x://a[contains(@href, 'https') and @class='w-full']")
                for ele in task_links:
                    ele.wait(1.6, 1.8)
                    try:
                        new_tab = ele.click.for_new_tab()
                        sleep(1)
                        new_tab.close()
                    except Exception as e:
                        self.logger.error(f"{e}")
                sleep(2)

            # 更新任务状态
            registration["fake_task"] = "1"
            self.data_manager.save_registration(registration)
            return True

        except Exception as e:
            self.logger.error(f"执行假任务失败: {str(e)}")
            return False

    @retry(tries=3, delay=3)
    def defi_task_daily(self, tab) -> bool:
        """执行DeFi每日任务

        Returns
        -------
            bool: 是否执行成功
        """
        try:
            x_user = self.browser_controller.browser_config.x_user

            if not tab.url == "https://speedrun.enso.build" and not tab.url == "https://speedrun.enso.build/":
                self.logger.info("打开主页 defi_task")
                tab.get("https://speedrun.enso.build")

            # 点击DeFi分类
            ai_ele = get_element(tab, "x://a[contains(@href, '/categories/de-fi')]", timeout=30)
            if not ai_ele:
                raise Exception(f"【{self.id}】未找到元素de-fi")
            ai_ele.click()
            sleep(2)
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect']")
            if connect_btn:
                connect_btn.click()
                # self.page.wait.new_tab()
                # new_tab = self.page.latest_tab
                # login_tab = new_tab.wait.url_change(text="https://zealy.io/signup?subdomain=enso", timeout=3)
                # if login_tab:
                #     address = self.browser_controller.browser_config.evm_address
                #     registration = self.data_manager.get_registration(address)
                #     self._execute_zealy_connection(login_tab, registration)

                connect_btn = get_element(tab, "x://button[@aria-label='Connect to Connect wallet']", timeout=5)
                if not connect_btn:
                    raise Exception(f"【{self.id}】未找到元素connect_btn")
                connect_btn.click()
                sleep(2)

                # 选择OKX钱包
                okx_btn = get_element(tab, "x://div[contains(text(),'OKX Wallet')]", timeout=5)
                if not okx_btn:
                    raise Exception(f"【{self.id}】未找到元素okx_btn")
                okx_btn.click()
                sleep(2)

                # 连接钱包
                self.browser_controller.okx_wallet_connect()

                # 点击签名按钮
                sign_btn = get_element(tab, "x://button[contains(text(),'Sign message')]", timeout=10)
                if not sign_btn:
                    raise Exception(f"【{self.id}】未找到元素sign_btn")
                sign_btn.click()
                sleep(2)

                # 再次连接钱包
                result = self.browser_controller.okx_wallet_connect()
                self.logger.info(f"【{self.id}】连接钱包成功{result}")
                sleep(2)
            # 检查是否已完成任务
            reset_ele = get_element(tab, "x://span[contains(text(),'Resets ')]", timeout=5)
            if reset_ele:
                self.logger.info(f"{self.id} defi已经完成，还需等待{reset_ele.raw_text}")
                return True

            # 执行DeFi任务
            for i in range(8):
                defi_btn = get_element(tab, "x://span[normalize-space()='DeFi DEX']")
                if not defi_btn:
                    continue
                defi_btn.click()
                sleep(2)

                # 生成随机信息
                faker = Faker()
                first_name = faker.first_name()
                last_name = faker.last_name()

                # 填写表单
                name_input = get_element(tab, "x://input[@id='name']")
                if not name_input:
                    continue
                name_input.clear(True)
                name_input.input(first_name)
                sleep(1)

                subdomain_input = get_element(tab, "x://input[@id='subdomain']")
                if not subdomain_input:
                    continue
                subdomain_input.clear(True)
                subdomain_input.input(generate_random_string())
                sleep(1)

                twitter_input = get_element(tab, "x://input[@id='twitter']")
                if not twitter_input:
                    continue
                twitter_input.clear(True)
                twitter_input.input("@" + x_user)
                sleep(1)

                # 提交表单
                submit_btn = get_element(tab, "x://button[normalize-space()='Submit']")
                if not submit_btn:
                    continue
                submit_btn.click()
                sleep(2)

                close_btn = get_element(tab, "x://button[normalize-space()='Close']", timeout=10)
                if not close_btn:
                    continue
                close_btn.click()
                sleep(2)
                if i >= 4:
                    reset_ele = get_element(tab, "x://span[contains(text(),'Resets ')]", timeout=5)
                    if reset_ele:
                        self.logger.success(f"{self.id} 已经完成，还需等待{reset_ele.raw_text}")
                    return True
            raise Exception(f"【{self.id}】执行DeFi任务失败")

        except Exception as e:
            self.logger.error(f"执行DeFi任务失败: {str(e)}")
            raise Exception(f"【{self.id}】执行DeFi任务失败: {str(e)}")

    @retry(tries=3, delay=3)
    def ai_task_daily(self, tab) -> bool:
        """执行AI每日任务

        Returns
        -------
            bool: 是否执行成功
        """
        try:
            if not tab.url == "https://speedrun.enso.build/" and not tab.url == "https://speedrun.enso.build":
                tab = self.page.new_tab("https://speedrun.enso.build")
            sleep(5)
            # 点击AI分类
            ai_btn = get_element(tab, "x://a[contains(@href, '/categories/ai')]", timeout=10)
            if not ai_btn:
                self.logger.info("ai未找到AI分类按钮")
                raise Exception(f"{self.id}:未找到AI分类按钮")
            duration = random.uniform(0.1, 0.3)
            offset_x = ai_btn.rect.corners[1][0] + random.randint(20, 50)
            offset_y = ai_btn.rect.corners[1][1] + random.randint(-3, 6)
            ai_btn.click()
            connect_btn = get_element(tab, "x://div[contains(text(), 'Under Maintenance')]", timeout=5)
            if connect_btn:
                # 右上角点击，取消弹出框
                self.logger.info(f"{self.id} 维护中")
                tab.actions.move_to(connect_btn).move(offset_x=offset_x, offset_y=offset_y, duration=duration).click()

                return True

            sleep(2)
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect']")
            if connect_btn:
                connect_btn.click()
                login_tab = tab.wait.url_change(text="https://zealy.io/signup?subdomain=enso", timeout=3)
                if login_tab:
                    address = self.browser_controller.browser_config.evm_address
                    registration = self.data_manager.get_registration(address)
                    self._execute_zealy_connection(login_tab, registration)
                connect_btn = get_element(tab, "x://button[@aria-label='Connect to Connect wallet']", timeout=5)
                if not connect_btn:
                    raise Exception("未找到连接钱包按钮")
                connect_btn.click()
                sleep(2)

                # 选择OKX钱包
                okx_btn = get_element(tab, "x://div[contains(text(),'OKX Wallet')]", timeout=5)
                if not okx_btn:
                    raise Exception("未找到OKX钱包按钮")
                okx_btn.click()
                sleep(2)

                # 连接钱包
                self.browser_controller.okx_wallet_connect()

                # 点击签名按钮
                sign_btn = get_element(
                    tab, "x://button[contains(text(),'Sign message') or contains(text(),'发送消息')]", timeout=10
                )
                if not sign_btn:
                    raise Exception("未找到签名按钮")
                sign_btn.click()
                sleep(2)

                # 再次连接钱包
                result = self.browser_controller.okx_wallet_connect()
                self.logger.info(f"【{self.id}】连接钱包成功{result}")
                sleep(2)

            # 检查是否已完成任务
            reset_ele = get_element(tab, "x://span[contains(text(),'Resets ')]")
            if reset_ele:
                self.logger.info(f"{self.id} ai_task 已经完成，还需等待{reset_ele.raw_text}")
                return True

            # 执行AI任务
            ai_ele = get_element(tab, "x://span[normalize-space()='Ask Enso AI']")
            if not ai_ele:
                raise Exception("未找到Ask Enso AI按钮")

            ai_tab = ai_ele.click.for_new_tab()
            ai_tab.set.load_mode.none()
            ai_tab.wait(10)
            ai_tab.stop_loading()

            # 连接钱包
            connect_btn = get_element(ai_tab, "x://button[normalize-space()='Connect Wallet']", timeout=5)
            if connect_btn:
                connect_btn.click()
                sleep(2)

                sign_btn = get_element(
                    ai_tab, "x://button[normalize-space()='发送消息' or normalize-space()='Sign message']", timeout=5
                )
                if sign_btn:
                    sign_btn.click()
                    sleep(2)
                    if not self.browser_controller.okx_wallet_connect():
                        self.browser_controller.okx_wallet_connect()
                else:
                    okx_btn = get_element(ai_tab, "x://div[contains(text(),'OKX Wallet')]")
                    if not okx_btn:
                        raise Exception("未找到OKX钱包按钮")
                    okx_btn.click()
                    sleep(2)
                    self.browser_controller.okx_wallet_connect()

                    sign_btn = get_element(
                        ai_tab, "x://button[normalize-space()='发送消息' or normalize-space()='Sign message']"
                    )
                    if not sign_btn:
                        raise Exception("未找到签名按钮")
                    sign_btn.click()
                    sleep(2)
                    if not self.browser_controller.okx_wallet_connect():
                        self.browser_controller.okx_wallet_connect()

            # 执行AI问答
            for i in range(7):
                self.logger.info(f"{self.id} 开始ai 答题")
                search_input = get_element(ai_tab, "x://input[@placeholder='Start searching here!']")
                if not search_input:
                    continue
                search_input.clear(True)
                search_input.input("What is Uniswap v4")
                sleep(1)

                search_btn = get_element(ai_tab, "x://button[normalize-space()='Search']")
                if not search_btn:
                    continue
                search_btn.click()
                sleep(5)

            ai_tab.close()
            return True

        except Exception as e:
            raise Exception(f"【{self.id}】执行AI任务失败: {str(e)}") from e

    def _check_cf_shield1(self, lasted_tab):
        """检查并处理CF盾

        Args:
            lasted_tab: 浏览器标签页

        Returns
        -------
            bool: 是否成功处理
        """
        for _ in range(6):
            try:
                # 1. 判断是否在CF盾页面
                if "verify-email" in lasted_tab.url:
                    return True

                div_ele = lasted_tab.ele("x://div[@class='flex justify-center items-center']/div/div")
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        self.logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if "Register | MetaMask Developer" in lasted_tab.title:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                            return True
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        self.logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                self.logger.error(f"过CF盾发生异常，error={str(e)}")
        return False

    def _check_cf_shield2(self, tab, url):
        """检查并处理CF盾2

        Args:
            tab: 浏览器标签页
            url: 目标URL

        Returns
        -------
            bool: 是否成功处理
        """
        for _ in range(6):
            try:
                div_ele = tab.ele("x://div[@id='cf-turnstile']/div", timeout=5)
                if not div_ele:
                    self.logger.success(f"【{self.id}】 过CF盾成功")
                    return True
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        self.logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if url in tab.url:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        self.logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                self.logger.error(f"过CF盾发生异常，error={str(e)}")
        return False
