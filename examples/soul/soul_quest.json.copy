{"quest00": {"key": "quest00", "name": "Quest 00: Engage With <PERSON>'s Announcement on X", "missions": [{"key": "special_x_1", "name": "<PERSON>, <PERSON><PERSON>, comment and Bookmark Soul's Announcement on X.", "type": "SPECIAL_X", "chain": "social", "operation": "checkSpecialX", "protocol": "x", "order": 1}, {"key": "special_x_2", "name": "<PERSON>, <PERSON><PERSON>, comment and Bookmark Soul's Announcement on X.", "type": "SPECIAL_X", "chain": "social", "operation": "checkSpecialX", "protocol": "x", "order": 2}]}, "quest1": {"key": "quest1", "name": "Quest 1: Become Part Of The Soul Protocol Community", "missions": [{"key": "connect_x", "name": "Connect Your X Account.", "type": "CONNECT_X", "chain": "social", "operation": "connectX", "protocol": "x", "order": 1}, {"key": "x_follow", "name": "Follow Soul Protocol's X Account.", "type": "SOCIAL_X_FOLLOW", "chain": "social", "operation": "checkXFollow", "protocol": "x", "order": 2}, {"key": "x_retweet", "name": "Like And Repost Soul Protocol's <PERSON><PERSON>d Post On X.", "type": "SOCIAL_X_RETWEET", "chain": "social", "operation": "checkXRetweet", "protocol": "x", "order": 3}, {"key": "x_comment", "name": "Comment On Soul Protocol's Pinned Post On X.", "type": "SOCIAL_X_COMMENT", "chain": "social", "operation": "checkXComment", "protocol": "x", "order": 4}, {"key": "connect_telegram", "name": "Connect Your Telegram Account And Join The Community.", "type": "CONNECT_TELEGRAM", "chain": "social", "operation": "connectTelegram", "protocol": "telegram", "order": 5}, {"key": "connect_discord", "name": "Connect Your Discord Account.", "type": "CONNECT_DISCORD", "chain": "social", "operation": "connectDiscord", "protocol": "discord", "order": 6}, {"key": "join_discord", "name": "Unlock The Explorer Role By Joining Soul's Discord Community.", "type": "SOCIAL_DISCORD", "chain": "social", "operation": "joinDiscordAndCheckRole", "protocol": "discord", "order": 7}]}, "quest2": {"key": "quest2", "name": "Quest 2: Lend Cross Chain On Ethereum Sepolia And Fuji Avalanche", "missions": [{"key": "claim_tokens", "name": "Claim Testnet Tokens To Perform The Next Tasks.", "type": "CLAIM_TOKENS", "chain": "", "operation": "", "protocol": "", "order": 1}, {"key": "supply_avalanche", "name": "Supply Any Of The Available Assets On The Avalanche Chain.", "type": "ON_CHAIN", "chain": "Ava<PERSON>", "operation": "supply", "protocol": "aave", "order": 2}, {"key": "supply_ethereum", "name": "Supply Any Of The Available Assets On The Ethereum Sepolia Chain.", "type": "ON_CHAIN", "chain": "Ethereum <PERSON>", "operation": "supply", "protocol": "compoundV3", "order": 3}, {"key": "add_collateral_avalanche", "name": "Add The Same Asset As Collateral On The Avalanche Chain.", "type": "ON_CHAIN", "chain": "Ava<PERSON>", "operation": "addCollateral", "protocol": "aave", "order": 4}]}, "quest3": {"key": "quest3", "name": "Quest 3: Lend And Borrow Cross Chain On Sepolia Arbitrum And Optimism", "missions": [{"key": "supply_arbitrum", "name": "Supply Any Of The Available Assets On The Arbitrum Chain.", "type": "ON_CHAIN", "chain": "Arbitrum Sepolia", "operation": "supply", "protocol": "aave", "order": 1}, {"key": "supply_optimism", "name": "Supply Any Of The Available Assets On The Optimism Chain.", "type": "ON_CHAIN", "chain": "Optimism Sepolia", "operation": "supply", "protocol": "venus", "order": 2}, {"key": "add_collateral_optimism", "name": "Add The Same Asset As Collateral On The Optimism Chain.", "type": "ON_CHAIN", "chain": "Optimism Sepolia", "operation": "addCollateral", "protocol": "venus", "order": 3}, {"key": "borrow_optimism", "name": "Take A Borrow On The Optimism Chain.", "type": "ON_CHAIN", "chain": "Optimism Sepolia", "operation": "borrow", "protocol": "venus", "order": 4}]}, "quest4": {"key": "quest4", "name": "Quest 4: Borrow Cross Chain On Ethereum Sepolia And Avalanche Fuji", "missions": [{"key": "borrow_avalanche", "name": "Take A Borrow Of Any Asset On The Avalanche Chain.", "type": "ON_CHAIN", "chain": "Ava<PERSON>", "operation": "borrow", "protocol": "aave", "order": 1}, {"key": "repay_avalanche", "name": "<PERSON>ay The Borrowed Asset On The Avalanche Chain.", "type": "ON_CHAIN", "chain": "Ava<PERSON>", "operation": "repayBorrow", "protocol": "aave", "order": 2}, {"key": "remove_collateral_avalanche", "name": "Remove Collateral From The Avalanche Chain.", "type": "ON_CHAIN", "chain": "Ava<PERSON>", "operation": "removeCollateral", "protocol": "aave", "order": 3}, {"key": "redeem_ethereum", "name": "Withdraw From The Ethereum Sepolia Chain.", "type": "ON_CHAIN", "chain": "Ethereum <PERSON>", "operation": "redeem", "protocol": "compoundV3", "order": 4}]}, "quest5": {"key": "quest5", "name": "Quest 5: Lend And Borrow Cross Chain On Base And Arbitrum", "missions": [{"key": "supply_base", "name": "Supply Any Of The Available Assets On The Base Chain.", "type": "ON_CHAIN", "chain": "Base Sepolia", "operation": "supply", "protocol": "compoundV3", "order": 1}, {"key": "add_collateral_base", "name": "Add The Same Asset As Collateral On The Base Chain.", "type": "ON_CHAIN", "chain": "Base Sepolia", "operation": "addCollateral", "protocol": "compoundV3", "order": 2}, {"key": "supply_arbitrum", "name": "Supply Any Of The Available Assets On The Arbitrum Chain.", "type": "ON_CHAIN", "chain": "Arbitrum Sepolia", "operation": "supply", "protocol": "aave", "order": 3}, {"key": "borrow_base", "name": "Take A Borrow Of Any Asset On The Base Chain.", "type": "ON_CHAIN", "chain": "Base Sepolia", "operation": "borrow", "protocol": "compoundV3", "order": 4}]}, "quest6": {"key": "quest6", "name": "Quest 6: Withdraw Assets From Sepolia Base And Arbitrum", "missions": [{"key": "repay_base", "name": "Repay The Borrowed Asset On The Base Chain.", "type": "ON_CHAIN", "chain": "Base Sepolia", "operation": "repayBorrow", "protocol": "compoundV3", "order": 1}, {"key": "redeem_arbitrum", "name": "Withdraw From The Arbitrum Chain.", "type": "ON_CHAIN", "chain": "Arbitrum Sepolia", "operation": "redeem", "protocol": "aave", "order": 2}, {"key": "remove_collateral_base", "name": "Remove Collateral From The Base Chain.", "type": "ON_CHAIN", "chain": "Base Sepolia", "operation": "removeCollateral", "protocol": "compoundV3", "order": 3}, {"key": "redeem_base", "name": "Withdraw From The Base Chain.", "type": "ON_CHAIN", "chain": "Base Sepolia", "operation": "redeem", "protocol": "compoundV3", "order": 4}]}}