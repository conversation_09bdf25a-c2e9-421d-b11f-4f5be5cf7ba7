import os
import random
import re
import string
import sys
from datetime import datetime
from time import sleep
from typing import Any, Dict, List, Optional, Union

from config_pharo import DEFAULT_TIMEOUT
from data_manager import DataManager
from faker import Faker
from loguru import logger
from retry import retry

from src.api.twitter.twitter import Twitter
from src.browsers.operations import input_text, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils.element_util import get_element, get_elements, get_frame
from src.utils.password import generate_random_string
from src.utils.yescaptcha import YesCaptcha
from utils import measure_time

PROXY_URL = os.environ.get("PROXY_URL")


class PharoTask:
    """任务管理类，用于处理具体任务"""

    def __init__(self, browser_type: BrowserType, id: str):
        """初始化任务管理器

        Args:
            browser_type: 浏览器类型
            id: 任务ID
        """
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.data_manager = DataManager("pharo")  # 使用enso项目的数据管理器
        self.discord_bind_failed = False  # 记录Discord绑定失败状态
        self.twitter_bind_failed = False  # 记录Twitter绑定失败状态
        self.pid = os.getpid()  # 获取进程ID

        # 初始化带有任务ID的logger
        self.logger = logger.bind(task_id=f"【PID:{self.pid}】【{self.id}】")

    def _get_registration_random(self) -> str:
        """获取随机注册信息

        Returns
        -------
            str: 推荐码
        """
        try:
            # 获取所有注册信息
            all_registrations = self.data_manager.list()
            # 过滤出有效的推荐码
            valid_registrations = [
                reg for reg in all_registrations if reg.get("referral_code") and reg.get("referral_code") != ""
            ]
            return valid_registrations[0].get("referral_code") if valid_registrations else "P9FvTOctdhJsPa3K"
        except Exception as e:
            self.logger.error(f"获取随机注册信息失败: {e}")
            return "P9FvTOctdhJsPa3K"

    def _get_captcha_token(self, page) -> str | None:
        return YesCaptcha.get_web_plugin_token_recaptcha(page, self.id)

    @retry(tries=2, delay=3)
    def execute_tasks(self) -> bool:
        """执行所有任务

        Returns
        -------
            bool: 是否所有任务都执行成功
        """
        tab = None
        try:
            tab = self.page.latest_tab
            if "https://testnet.pharosnetwork.xyz/experience" not in tab.url:
                self.logger.info("打开新标签页")
                tab = self.page.new_tab()
                tab.listen.start(
                    targets=["https://api.pharosnetwork.xyz/user/profile", "https://api.pharosnetwork.xyz/user/tasks"]
                )
                tab.get("https://testnet.pharosnetwork.xyz/experience", timeout=30)  # 添加超时
            else:
                self.logger.info("刷新当前页面")
                tab.listen.start(
                    targets=["https://api.pharosnetwork.xyz/user/profile", "https://api.pharosnetwork.xyz/user/tasks"]
                )
                tab.refresh()  # 添加超时
            # 获取用户信息
            if get_element(tab, "x://button[normalize-space()='Switch >' or normalize-space()='Continue']", timeout=5):
                try_click(tab, "x://button[normalize-space()='Switch >']", max_attempts=2, timeout=3)
                try_click(tab, "x://button[normalize-space()='Continue']", max_attempts=2, timeout=3)
                if not self.browser_controller.okx_wallet_connect():
                    self.logger.error("钱包连接失败")
                    return False
                if get_element(tab, "x://button[normalize-space()='Continue']"):
                    if not self.browser_controller.clear_site_data("testnet.pharosnetwork.xyz"):
                        self.logger.info("清除缓存失败")
                        return False
                    tab.refresh()
                    raise Exception("重新登录")
            # 登录检查
            self.logger.info("检查登录状态")
            if not self.is_login(tab) and not self.login(tab):
                self.logger.error("登录失败")
                return False

            self.logger.info("获取用户信息")
            x_id, dc_id, task_ids = self.get_profile_task(tab)
            if x_id is None:
                self.logger.error("获取用户信息失败")
                return False
            # 等待水到账
            sleep(30)
            # 签到
            self.logger.info("执行签到")
            if not self.sign_in(tab):
                self.logger.error("签到失败")
                return False

            if not self.faucet(tab):
                self.logger.error("领水失败")
                # 重试
                return False
            # if not self.send(tab):
            #     return False
            # if not self.swap():
            #     return False
            # x 任务
            if not self.x_tasks(tab, task_ids):
                self.twitter_bind_failed = True
                self.logger.error("x任务执行失败")
                return False

            self.logger.success("所有任务执行完成")
            return True

        except Exception as e:
            self.logger.error(f"执行任务失败: {str(e)}")
            raise Exception("重试任务")
        finally:
            try:
                if tab:
                    self.logger.info("关闭标签页")
                    tab.close()
                self.logger.info("关闭浏览器")
                self.browser_controller.close_page()
            except Exception as e:
                self.logger.debug(f"关闭浏览器失败: {str(e)}")

    @measure_time
    @retry(tries=3, delay=3)
    def register(self, type: str) -> bool:
        """注册任务

        Args:
            type: 注册类型

        Returns
        -------
            bool: 是否注册成功
        """
        try:
            address = self.browser_controller.browser_config.evm_address
            registration = self.data_manager.get_registration(address)
            if registration:
                self.logger.info(f"钱包 {address} 已注册,跳过注册流程")
                return True
            tab = self.page.latest_tab

            # 打开任务页面
            self.logger.info(f"当前页面: {tab.url}")
            referral_code = self._get_registration_random()

            if registration is not None and registration.get("registration"):
                referral_code = registration.get("registration")
            # 检查当前页面是否是插件页面
            tab = self.page.new_tab(f"https://testnet.pharosnetwork.xyz/experience?inviteCode={referral_code}")
            # tab = self.page.latest_tab
            # self.page.close_tabs(tab, others=True)
            # tab.set.window.max()
            # 等待页面加载完成
            # 检查是否已注册

            if not self._connect_wallet(tab):
                self.logger.info("注册失败")
                return False

            registration = {"index": self.id, "type": type, "address": address, "status": "1"}
            # 注册前保存用户信息
            self.data_manager.save_registration(registration)
            return True
            # 准备注册信息

        except Exception as e:
            self.logger.error(f"注册失败: {str(e)}")
            return False

    def get_profile(self, tab):
        try:
            self.logger.info("等待用户信息响应")
            res = tab.listen.wait(timeout=30)  # 增加超时时间

            if "https://api.pharosnetwork.xyz/user/profile" in res.url:
                if not res or not hasattr(res, "response") or not res.response.body:
                    self.logger.error("profile无效的响应数据")
                    return None

                response_data = res.response.body
                if not isinstance(response_data, dict):
                    self.logger.error(f"响应数据格式错误: {response_data}")
                    return None

                # 提取用户信息
                user_info = response_data.get("data", {}).get("user_info", {})
                if not user_info:
                    self.logger.error("未找到用户信息")
                    return None

                x_id = user_info.get("XId", "")
                discord_id = user_info.get("DiscordId", "")
                referral_code = user_info.get("InviteCode", "")
                total_points = user_info.get("TotalPoints", 0)
                task_points = user_info.get("TaskPoints", 0)
                invite_points = user_info.get("InvitePoints", 0)

                # 获取当前数据
                address = self.browser_controller.browser_config.evm_address
                current_data = self.data_manager.get(address) or {}

                # 保存上一次的数据
                last_total_points = current_data.get("total_points", 0)
                last_task_points = current_data.get("task_points", 0)
                last_invite_points = current_data.get("invite_points", 0)

                # 更新数据
                self.logger.info("更新用户数据")
                self.data_manager.update(
                    address,
                    {
                        "x_id": x_id,
                        "discord_id": discord_id,
                        "referral_code": referral_code,
                        "total_points": total_points,
                        "task_points": task_points,
                        "invite_points": invite_points,
                        "last_total_points": last_total_points,
                        "last_task_points": last_task_points,
                        "last_invite_points": last_invite_points,
                        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    },
                )

                # 记录用户信息
                self.logger.info(
                    f"用户信息: XId:{x_id} DiscordId:{discord_id} TotalPoints:{total_points} TaskPoints:{task_points} InvitePoints:{invite_points}",
                )

            return x_id, discord_id
        except Exception as e:
            self.logger.error(f"处理用户信息失败: {str(e)}")
            return None

    def get_profile_task(self, tab):
        try:
            self.logger.info("等待用户信息响应")
            ress = tab.listen.wait(count=2, timeout=30)  # 增加超时时间
            for res in ress:
                if "https://api.pharosnetwork.xyz/user/profile" in res.url:
                    if not res or not hasattr(res, "response") or not res.response.body:
                        self.logger.error("profile无效的响应数据")
                        return None

                    response_data = res.response.body
                    if not isinstance(response_data, dict):
                        self.logger.error(f"响应数据格式错误: {response_data}")
                        return None

                    # 提取用户信息
                    user_info = response_data.get("data", {}).get("user_info", {})
                    if not user_info:
                        self.logger.error("未找到用户信息")

                        if not self.browser_controller.clear_site_data("testnet.pharosnetwork.xyz"):
                            self.logger.info("清除缓存失败")
                            return False
                        tab.refresh()
                        raise Exception("重新登录")

                    x_id = user_info.get("XId", "")
                    discord_id = user_info.get("DiscordId", "")
                    referral_code = user_info.get("InviteCode", "")
                    total_points = user_info.get("TotalPoints", 0)
                    task_points = user_info.get("TaskPoints", 0)
                    invite_points = user_info.get("InvitePoints", 0)

                    # 获取当前数据
                    address = self.browser_controller.browser_config.evm_address
                    current_data = self.data_manager.get(address) or {}

                    # 保存上一次的数据
                    last_total_points = current_data.get("total_points", 0)
                    last_task_points = current_data.get("task_points", 0)
                    last_invite_points = current_data.get("invite_points", 0)

                    # 更新数据
                    self.logger.info("更新用户数据")
                    self.data_manager.update(
                        address,
                        {
                            "x_id": x_id,
                            "discord_id": discord_id,
                            "referral_code": referral_code,
                            "total_points": total_points,
                            "task_points": task_points,
                            "invite_points": invite_points,
                            "last_total_points": last_total_points,
                            "last_task_points": last_task_points,
                            "last_invite_points": last_invite_points,
                            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        },
                    )

                    # 记录用户信息
                    self.logger.info(
                        f"用户信息: XId:{x_id} DiscordId:{discord_id} TotalPoints:{total_points} TaskPoints:{task_points} InvitePoints:{invite_points}",
                    )
                if "https://api.pharosnetwork.xyz/user/tasks" in res.url:
                    if not res or not hasattr(res, "response") or not res.response.body:
                        self.logger.error("profile无效的响应数据")
                        return None

                    response_data = res.response.body
                    if not isinstance(response_data, dict):
                        self.logger.error(f"响应数据格式错误: {response_data}")
                        return None

                    # 提取任务ID列表
                    user_tasks = response_data.get("data", {}).get("user_tasks", [])
                    if not user_tasks:
                        self.logger.info("未找到任务信息")
                        task_ids = None
                    # 提取所有TaskId
                    task_ids = [task.get("TaskId") for task in user_tasks]
                    self.logger.info(f"已完成的任务ID列表: {task_ids}")

            return x_id, discord_id, task_ids
        except Exception as e:
            self.logger.error(f"处理用户信息失败: {str(e)}")
            return None

    def is_login(self, tab):
        return get_element(tab, "x://span[contains(text(),'0x')]", timeout=10)

    def login(self, tab):
        if not self._connect_wallet(tab):
            self.logger.info("登录失败")
            return False
        return True

    def sign_in(self, tab):
        try:
            if get_element(tab, "x://button[contains(text(), 'Continue')]"):
                try_click(tab, get_element(tab, "x://button[contains(text(), 'Continue')]"))
                return True
            if get_element(tab, "x://button[contains(text(), 'Checked')]"):
                self.logger.info("今日已经签到")
                return True

            self.logger.info("开始签到流程")
            tab.listen.start("https://api.pharosnetwork.xyz/sign/in")
            if not try_click(tab, "x://button[normalize-space()='Check in']"):
                self.logger.error("点击签到按钮失败")
                return False

            self.logger.info("等待签到响应")
            res = tab.listen.wait(timeout=30)  # 增加超时时间

            if not res or not hasattr(res, "response") or not res.response.body:
                self.logger.error("sign无效的响应数据")
                return False

            response_data = res.response.body
            if not isinstance(response_data, dict):
                self.logger.error(f"响应数据格式错误: {response_data}")
                return False

            # 检查响应状态码
            code = response_data.get("code")
            if code != 0:
                self.logger.error(f"请求失败: {response_data.get('msg', '未知错误')}")
                return False

            self.logger.success("签到成功")
            return True
        except Exception as e:
            self.logger.error(f"签到失败: {str(e)}")
            return False

    @retry(tries=3, delay=3)
    def faucet(self, tab):
        try_click(tab, "x://li[contains(text(),'Home')]")
        sleep(2)
        if get_element(tab, "x://button[@disabled and not(contains(text(), 'CLAIM NOW'))]", timeout=10) is not None:
            self.logger.info("等待下次领取")
            return True
        scroll_ele = get_element(tab, "x://div/a[contains(text(), 'Need More Tokens')]")
        scroll_ele.scroll.to_see()
        bind_x_ele = get_element(tab, "x://button[normalize-space()='Connect X Account for More!']")
        if bind_x_ele:
            if not self._bind_twitter(tab, bind_x_ele):
                return False

        tab.listen.start("https://api.pharosnetwork.xyz/faucet/status")
        captcha_token = self._get_captcha_token(tab)
        if not captcha_token:
            raise Exception(f"【{self.id}】 获取验证码失败")

        try_click(tab, "x://button[contains(text(),'CLAIM NOW')]", timeout=10)
        res = tab.listen.wait(timeout=10)

        if res and hasattr(res, "response"):
            response_data = res.response.body
            if not isinstance(response_data, dict):
                self.logger.error(f"响应数据格式错误: {response_data}")
                return None

            # Extract values from response
            code = response_data.get("code")
            avaliable_timestamp = response_data.get("data", {}).get("avaliable_timestamp")
            is_able_to_faucet = response_data.get("data", {}).get("is_able_to_faucet")

            self.logger.info(
                f"提取数据: code={code}, avaliable_timestamp={avaliable_timestamp}, is_able_to_faucet={is_able_to_faucet}"
            )
            if not is_able_to_faucet:
                self.logger.success("领水成功")
                return True
            raise Exception(f"【{self.id}】重新领水")
        else:
            self.logger.error("未收到响应")
            raise Exception(f"【{self.id}】重新领水")

    def _verify_task(self, tab, task_name: str, task_id: int) -> bool:
        """验证任务是否完成

        Args:
            tab: 浏览器标签页
            task_name: 任务名称
            task_id: 任务ID

        Returns
        -------
            bool: 是否验证成功
        """
        if not self.x_task_check(tab, task_id):
            self.logger.warning(f"x {task_name} fail")
            return False
        return True

    def x_tasks_check(self, tab):
        """检查并验证所有X任务

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否所有任务都验证成功
        """
        tasks = [("Follow", "follow", 201), ("Retweet", "retweet", 202), ("Reply", "reply", 203)]
        tab.listen.start("https://api.pharosnetwork.xyz/task/verify")
        for button_text, task_name, task_id in tasks:
            ele = get_element(tab, f"x://button[normalize-space()='{button_text}']")
            if ele:
                try_click(tab, ele.next())
                if not self._verify_task(tab, task_name, task_id):
                    sleep(3)
                    try_click(tab, ele.next())
                    if not self._verify_task(tab, task_name, task_id):
                        return False

        return True

    def swap(self):
        try:
            laster_tab = self.page.new_tab()
            laster_tab.get("https://testnet.zenithfinance.xyz/swap", timeout=30)  # 添加超时

            if get_element(laster_tab, "x://button[@data-testid='navbar-connect-wallet']", timeout=10):
                try_click(laster_tab, "x://button[@data-testid='navbar-connect-wallet']")
                try_click(laster_tab, "x://button[@data-testid='wallet-option-OKX_WALLET']")
                self.browser_controller.okx_wallet_connect()
            # 生成0.001到0.002之间的随机数，保留4位小数
            amount = round(random.uniform(0.001, 0.002), 4)
            input_text(laster_tab, "x://input[@id='swap-currency-input']", str(amount))

            try_click(laster_tab, "x://span[contains(text(),'Select token')]")
            # 生成2-4之间的随机数
            num = random.randint(2, 4)

            ele_coin = laster_tab.ele("x://div[contains(text(),'Token list')]").next().child(num)
            try_click(laster_tab, ele_coin)
            sleep(10)
            if num == 2:
                wrap_ele = get_element(laster_tab, "x://button[text()='Wrap']")
                if wrap_ele.wait.enabled(timeout=40):
                    try_click(laster_tab, wrap_ele)
                    self.browser_controller.okx_wallet_sign()
                    sleep(15)
                    laster_tab.close()
                    self.logger.info("swap成功")
                    return True
                return False
            else:
                swap_ele = get_element(laster_tab, "x://button[@data-testid='swap-button']")
                if swap_ele.wait.enabled(timeout=40):
                    try_click(laster_tab, swap_ele)
                    sleep(2)
                    try_click(laster_tab, "x://button[@data-testid='confirm-swap-button']")
                    self.browser_controller.okx_wallet_sign()
                    sleep(15)
                    laster_tab.close()
                    self.logger.info("swap成功")
                    return True
                return False
        except Exception as e:
            self.logger.debug(f"swap失败: {str(e)}")

    @retry(tries=2, delay=3)
    def send(self, tab):
        try_click(tab, "x://li[contains(text(),'Experience')]")
        if not try_click(tab, "x://button[contains(text(),'Send')]"):
            return False
        if not try_click(tab, "x://div[contains(text(),'0.001PHRS')]"):
            return False

        # 从文件中随机读取一个地址
        try:
            with open(r"examples/pharo/evm_address.txt") as f:
                random_address = f.readlines()
                if not random_address:
                    self.logger.error("地址文件为空")
                    return False
                send_address = random.choice(random_address).strip()
                self.logger.info(f"随机选择发送地址: {send_address}")
        except Exception as e:
            self.logger.error(f"读取地址文件失败: {str(e)}")
            return False
        tab.listen.start("https://api.pharosnetwork.xyz/task/verify")
        if not input_text(tab, "x://input[@placeholder='Enter Address']", send_address):
            return False
        sleep(2)
        if not try_click(tab, "x://button[contains(text(),'Send PHRS')]"):
            return False
        sleep(4)
        if not self.browser_controller.okx_wallet_sign():
            # todo
            self.logger.info("发送成功")
        #  return False
        # if not self.x_task_check(tab, 103):
        #     tab.refresh()
        #     raise Exception("重新swap")
        self.logger.success("发送成功")
        return True

    def x_task_check(self, tab, task_id_check):
        res = tab.listen.wait(timeout=60)
        if not res:
            self.logger.error(f"【{self.id}】 未收到响应")
            return False

        response_data = res.response.body
        if not isinstance(response_data, dict):
            self.logger.error(f"响应数据格式错误: {response_data}")
            return False

        # 检查响应状态码
        code = response_data.get("code")
        if code != 0:
            self.logger.error(f"请求失败: {response_data.get('msg', '未知错误')}")
            return False

        # 检查任务验证状态
        verified = response_data.get("data", {}).get("verified", False)
        task_id = response_data.get("data", {}).get("task_id", False)
        # if verified and task_id == task_id_check:
        if verified and task_id == task_id_check:
            self.logger.success(f"【{self.id}】 任务验证成功")
            return True
        else:
            self.logger.warning(f"【{self.id}】任务验证失败")
            return False

    def x_follow(self, name):
        try:
            x_token = self.browser_controller.browser_config.x_token
            proxy = self.browser_controller.browser_config.proxy or PROXY_URL

            twitter_api = Twitter(int(self.id), x_token, proxy)
            twitter_api.start()

            twitter_api.follow(name)

            return True
        except Exception as e:
            self.logger.error(f"{self.id} 关注elonmusk失败: {e}")
            return False

    def x_retweet(self, retweet_id):
        try:
            x_token = self.browser_controller.browser_config.x_token
            proxy = self.browser_controller.browser_config.proxy or PROXY_URL

            twitter_api = Twitter(int(self.id), x_token, proxy)
            twitter_api.start()
            twitter_api.retweet(retweet_id)

            return True
        except Exception as e:
            if "already retweeted" in str(e):
                return True
            self.logger.error(f"{self.id} retweet: {e}")
            return False

    def x_reply(self, replay, replay_id):
        try:
            x_token = self.browser_controller.browser_config.x_token
            proxy = self.browser_controller.browser_config.proxy or PROXY_URL

            twitter_api = Twitter(int(self.id), x_token, proxy)
            twitter_api.start()

            twitter_api.post_tweet(replay, "1923181902581948830")
            return True
        except Exception as e:
            self.logger.error(f"{self.id} 回复失败: {e}")
            return False

    # 关注
    def x_tasks(self, tab, task_ids):
        try_click(tab, "x://li[contains(text(),'Experience')]")

        # 如果所有任务都已完成，直接返回
        if task_ids and all(task_id in task_ids for task_id in [201, 202, 203]):
            self.logger.info("所有任务已完成，跳过执行")
            return True

        follow_ele = get_element(tab, "x://button[normalize-space()='Follow']")
        if follow_ele and 201 not in task_ids:
            new_tab = follow_ele.click.for_new_tab()
            sleep(random.uniform(3, 6))
            new_tab.close()
            if not self.x_follow("pharos_network"):
                self.logger.info("follow 失败")
                return False

        retweet_ele = get_element(tab, "x://button[normalize-space()='Retweet']")
        if retweet_ele and 202 not in task_ids:
            new_tab = retweet_ele.click.for_new_tab()
            sleep(random.uniform(3, 6))
            new_tab.close()
            if not self.x_retweet("1923181902581948830"):
                self.logger.info(" retweet失败")
                return False

        reply_ele = get_element(tab, "x://button[normalize-space()='Reply']")
        if reply_ele and 203 not in task_ids:
            new_tab = reply_ele.click.for_new_tab()
            sleep(random.uniform(3, 6))
            new_tab.close()
            if not self.x_reply("I like it", "1923181902581948830"):
                self.logger.info("reply失败")
                return False

        self.x_tasks_check(tab)
        return True

    def _connect_wallet(self, tab) -> bool:
        """连接钱包

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否连接成功
        """
        try:
            if get_element(tab, "x://button[normalize-space()='Switch >' or normalize-space()='Continue']", timeout=3):
                try_click(tab, "x://button[normalize-space()='Switch >']", max_attempts=3, timeout=3)
                try_click(tab, "x://button[normalize-space()='Continue']", max_attempts=3, timeout=3)
                if not self.browser_controller.okx_wallet_connect():
                    self.logger.error("钱包连接失败")
                    return False

            if get_element(tab, "x://button[normalize-space()='Connect Wallet']", timeout=5):
                # 点击连接钱包按钮
                connect_btn = get_element(tab, "x://button[normalize-space()='Connect Wallet']", timeout=5)
                if not connect_btn:
                    return False
                connect_btn.click()
                sleep(2)
                wal_ele = (
                    tab("x://w3m-modal")
                    .sr("x://wui-flex/wui-card/w3m-router")
                    .sr("x://div/w3m-connect-view")
                    .sr("x://w3m-wallet-login-list")
                    .sr("x://w3m-connector-list")
                    .sr("x://w3m-connect-announced-widget")
                    .sr("x://wui-flex/wui-list-wallet[@name='OKX Wallet']")
                )
                # .sr("x://button/wui-wallet-image[@name='OKX Wallet']")
                if not wal_ele:
                    self.logger.error("okx钱包未找到")
                    return False
                if not try_click(tab, wal_ele):
                    self.logger.error("点击钱包按钮失败")
                    return False

                if get_element(
                    tab, "x://button[normalize-space()='Switch >' or normalize-space()='Continue']", timeout=3
                ):
                    if not try_click(tab, "x://button[normalize-space()='Continue']", timeout=2):
                        self.logger.info("点击Continue失败")
                    if not self.browser_controller.okx_wallet_connect():
                        self.logger.error("钱包连接失败")
                        return False
                    return True
                # 连接钱包
                if not self.browser_controller.okx_wallet_connect():
                    self.logger.error("钱包连接失败")

                    if self.is_login(tab):
                        return True
                    return False

                if get_element(
                    tab, "x://button[normalize-space()='Switch >' or normalize-space()='Continue']", timeout=10
                ):
                    if not try_click(tab, "x://button[normalize-space()='Switch >']", timeout=10):
                        self.logger.info("点击Switch >失败")
                        return False
                    if not try_click(tab, "x://button[normalize-space()='Continue']", timeout=10):
                        self.logger.info("点击Continue失败")
                        return False
                    if not self.browser_controller.okx_wallet_connect():
                        self.logger.error("钱包连接失败")
                        return False

                self.logger.info("连接钱包成功")

            return True
        except Exception as e:
            self.logger.error(f"连接钱包失败: {str(e)}")
            return False

    def _handle_verification_code(self, tab, registration: dict) -> bool:
        """处理验证码

        Args:
            tab: 浏览器标签页
            registration: 注册信息

        Returns
        -------
            bool: 是否处理成功
        """
        try:
            # 获取验证码
            code = self._get_email_verify_code(
                self.browser_controller.browser_config.email,
                self.browser_controller.browser_config.email_password,
                self.browser_controller.browser_config.proxy_email,
            )

            if not code:
                self.logger.info(f"{self.id} 验证码为空")
                return False

            # 输入验证码
            code_input = get_element(tab, "x://input[@autocomplete='one-time-code']", timeout=10)
            if not code_input:
                return False
            code_input.clear(True)
            code_input.input(code)
            sleep(2)
            if not tab.wait.url_change(text="https://zealy.io/create-user", timeout=10):
                self.logger.info(f"{self.id} 验证码失效或错误")
                return False
            return True

        except Exception as e:
            self.logger.error(f"处理验证码失败: {str(e)}")
            return False

    def _get_email_verify_code(self, email: str, email_pwd: str, proxy_email: str | None = None) -> str | None:
        """获取邮箱验证码

        Args:
            email: 邮箱
            email_pwd: 邮箱密码
            proxy_email: 代理邮箱

        Returns
        -------
            Optional[str]: 验证码，如果获取失败则返回None
        """
        try:
            from src.emails.imap4.email_client import EmailClient, SearchCriteria

            email_client = EmailClient(email, email_pwd)

            # 获取今天的日期

            search_criteria = SearchCriteria(
                subject="Your temporary Zealy login code is",
                to=proxy_email or email,
                from_addr="zealy.io",
                sort_order="DESC",  # 按时间倒序排序
            )

            emails = email_client.search_emails_with_retry(search_criteria)
            if not emails:
                self.logger.error(f"{self.id} 未找到验证码邮件")
                return None

            email = emails[0]
            pattern = r"is\s+([A-Za-z0-9]{6})"
            match = re.search(pattern, email["subject"])

            return match.group(1) if match else None

        except Exception as e:
            self.logger.error(f"{self.id} 获取验证码失败: {str(e)}")
            return None

    def _bind_wallet(self, tab) -> bool:
        """绑定钱包

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            address = self.browser_controller.browser_config.evm_address
            registration = self.data_manager.get_registration(address)

            connect_btn = get_element(tab, "x://button[normalize-space()='Connect wallet']", timeout=5)
            if not connect_btn:
                return True

            connect_btn.click()
            sleep(2)

            okx_btn = get_element(tab, "x://div[contains(text(),'OKX Wallet')]", timeout=5)
            if okx_btn:
                okx_btn.click()
            sleep(2)
            # 可能出现 可能不出现
            self.browser_controller.okx_wallet_connect()

            sign_btn = get_element(
                tab, "x://button[normalize-space()='发送消息' or normalize-space()='Sign message']", timeout=5
            )
            if sign_btn:
                sign_btn.click()
            sleep(2)

            return self.browser_controller.okx_wallet_connect()

        except Exception as e:
            self.logger.error(f"绑定钱包失败: {str(e)}")
            return False

    def _bind_discord(self, tab) -> bool:
        """绑定Discord

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            connect_btn = get_element(tab, "x://button[normalize-space()='Connect Discord']", timeout=5)
            if not connect_btn:
                return True

            new_tab = connect_btn.click.for_new_tab()
            sleep(2)

            new_tab = tab.wait.url_change(text="https://discord.com/login", timeout=10)
            if new_tab:
                result = self.browser_controller.login_discord()
                self.logger.info(f"登录Discord结果: {result}")
                if not result:
                    self.discord_bind_failed = True
                    return False
                else:
                    raise Exception(f"【{self.id}】重新绑定x")
            tab.wait.url_change(text="https://discord.com/oauth2/authorize", timeout=10)

            scroll_ele = get_element(tab, "x://div[@dir='ltr' and @style='overflow: hidden scroll;']", timeout=10)
            if scroll_ele:
                scroll_ele.scroll.down(500)
                sleep(0.5)
            allow_btn = get_element(tab, "x:(//button)[2]", timeout=10)
            # allow_btn.scroll.down(00)
            if not allow_btn:
                self.discord_bind_failed = True
                return False
            allow_btn.click()
            sleep(10)

            return True

        except Exception as e:
            self.logger.error(f"绑定Discord失败: {str(e)}")
            self.discord_bind_failed = True
            return False

    def _bind_twitter(self, tab, ele) -> bool:
        """绑定Twitter

        Args:
            tab: 浏览器标签页

        Returns
        -------
            bool: 是否绑定成功
        """
        try:
            self.logger.info(f"{self.id} 开始执绑定x")
            connect_btn = get_element(tab, ele, timeout=10)
            if not connect_btn:
                return True
            tab.listen.start("https://api.pharosnetwork.xyz/user/profile")
            lasted_tab = connect_btn.click.for_new_tab()
            sleep(2)

            tab_ele = lasted_tab.wait.url_change(text="twitter.com/i/flow/login", timeout=20)

            if tab_ele:
                self.browser_controller.login_x_with_auth()
            lasted_tab.wait.url_change(text="twitter.com/i/oauth2/authorize", timeout=10)
            lasted_tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
            allow_btn = get_element(lasted_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not allow_btn:
                self.twitter_bind_failed = True
                logger.error(f"【{self.id}】{self.id}未找到Twitter授权按钮")
                return False

            allow_btn.click()
            # sleep(3)

            x_id, _ = self.get_profile(tab)
            if x_id is not None:
                logger.success(f"【{self.id}】{self.id}绑定成功")
                return True
            self.twitter_bind_failed = True
            logger.error(f"【{self.id}】{self.id}绑定Twitter失败")
            return False

        except Exception as e:
            self.logger.error(f"绑定Twitter失败: {str(e)}")
            self.twitter_bind_failed = True
            return False

    def _check_cf_shield1(self, lasted_tab):
        """检查并处理CF盾

        Args:
            lasted_tab: 浏览器标签页

        Returns
        -------
            bool: 是否成功处理
        """
        for _ in range(6):
            try:
                # 1. 判断是否在CF盾页面
                if "verify-email" in lasted_tab.url:
                    return True

                div_ele = lasted_tab.ele("x://div[@class='flex justify-center items-center']/div/div")
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        self.logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if "Register | MetaMask Developer" in lasted_tab.title:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                            return True
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        self.logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                self.logger.error(f"过CF盾发生异常，error={str(e)}")
        return False

    def _check_cf_shield2(self, tab, url):
        """检查并处理CF盾2

        Args:
            tab: 浏览器标签页
            url: 目标URL

        Returns
        -------
            bool: 是否成功处理
        """
        for _ in range(6):
            try:
                div_ele = tab.ele("x://div[@id='cf-turnstile']/div", timeout=5)
                if not div_ele:
                    self.logger.success(f"【{self.id}】 过CF盾成功")
                    return True
                iframe = div_ele.sr(
                    "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                    timeout=10,
                )
                if iframe:
                    try:
                        success = iframe.ele("tag:body").sr("@id=success")
                        if success and success.states.is_displayed:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                            return True

                        self.logger.info(f"【{self.id}】 在CF盾页面")
                        checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                        if not checkbox:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                        checkbox.wait.has_rect(timeout=20)
                        checkbox.click()
                        sleep(3)

                        if url in tab.url:
                            self.logger.success(f"【{self.id}】 过CF盾成功")
                        else:
                            raise Exception(f"{self.id} 未找到验证码输入框")
                    except Exception as e:
                        self.logger.error(f"【{self.id}】 过CF盾失败: {e}")
                        sleep(2)
                        continue
            except Exception as e:
                self.logger.error(f"过CF盾发生异常，error={str(e)}")
        return False
