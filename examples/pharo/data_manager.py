import os
from threading import Lock
from typing import Any, Dict, List, Optional

from loguru import logger

from src.utils.common import get_project_root_path
from src.utils.hhcsv import HHCSV

# 定义通用的数据属性
COMMON_DATA_PROPS = [
    "index",
    "type",
    "address",
    "email",
    "status",
]


class DataManager:
    """通用数据管理类，用于处理CSV数据的读写操作"""

    _instances = {}
    _lock = Lock()
    _write_lock = Lock()  # 添加写入锁

    def __new__(cls, project_name: str = "pharo", data_props: list[str] = None, base_path: str = "examples"):
        """单例模式实现，支持不同项目的数据管理

        Args:
            project_name: 项目名称，用于区分不同项目的数据
            data_props: 数据属性列表，如果为None则使用默认属性
            base_path: 基础路径，默认为 "examples"
        """
        if project_name not in cls._instances:
            with cls._lock:
                if project_name not in cls._instances:
                    instance = super(DataManager, cls).__new__(cls)
                    instance._initialized = False
                    instance._project_name = project_name
                    instance._data_props = data_props or COMMON_DATA_PROPS
                    instance._base_path = base_path
                    cls._instances[project_name] = instance
        return cls._instances[project_name]

    def __init__(self, project_name: str, data_props: list[str] = None, base_path: str = "examples"):
        """初始化数据管理器

        Args:
            project_name: 项目名称
            data_props: 数据属性列表，如果为None则使用默认属性
            base_path: 基础路径，默认为 "examples"
        """
        if self._initialized:
            return

        with self._lock:
            if self._initialized:
                return

            try:
                # 设置CSV文件路径
                # 移除project_name中的.csv后缀
                dir_name = project_name.replace(".csv", "")

                # 如果base_path是data，直接使用project_name作为文件名
                if base_path == "data":
                    self._csv_path = os.path.join(get_project_root_path(), base_path, f"{dir_name}.csv")
                else:
                    # 其他情况保持原有的目录结构
                    data_dir = os.path.join(get_project_root_path(), base_path, dir_name)
                    self._csv_path = os.path.join(data_dir, f"{dir_name}.csv")

                self._csv = HHCSV(self._csv_path, self._data_props)
            except Exception as e:
                logger.error(f"初始化CSV文件失败: {str(e)}")
                raise

            self._initialized = True

    def list(self) -> list[dict[str, Any]]:
        """获取所有数据

        Returns
        -------
            List[Dict[str, Any]]: 所有数据记录
        """
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部数据失败: {str(e)}")
            return []

    def get(self, address: str) -> dict[str, Any] | None:
        """根据地址获取数据

        Args:
            address: 钱包地址

        Returns
        -------
            Optional[Dict[str, Any]]: 数据记录，如果不存在则返回None
        """
        try:
            result = self._csv.query({"address": address})
            return result[0] if result else None
        except Exception as e:
            logger.error(f"查询 {address} 数据失败: {str(e)}")
            return None

    def get_by_id(self, id: str) -> dict[str, Any] | None:
        """根据id获取数据

        Args:
            id: 索引

        Returns
        -------
            Optional[Dict[str, Any]]: 数据记录，如果不存在则返回None
        """
        try:
            result = self._csv.query({"id": id})
            return result[0] if result else None
        except Exception as e:
            logger.error(f"查询 {id} 数据失败: {str(e)}")
            return None

    def add(self, data: dict[str, Any]) -> bool:
        """添加新数据

        Args:
            data: 要添加的数据

        Returns
        -------
            bool: 是否添加成功
        """
        try:
            with self._write_lock:  # 使用写入锁保护添加操作
                self._csv.add_row(data)
                return True
        except Exception as e:
            logger.error(f"新增数据失败, data={data}, error={str(e)}")
            return False

    def update(self, address: str, data: dict[str, Any]) -> bool:
        """更新数据

        Args:
            address: 钱包地址
            data: 要更新的数据

        Returns
        -------
            bool: 是否更新成功
        """
        try:
            with self._write_lock:  # 使用写入锁保护更新操作
                criteria = {"address": address}
                self._csv.update_row(criteria, data)
                return True
        except Exception as e:
            logger.error(f"更新数据失败, address={address}, data={data}, error={str(e)}")
            return False

    def save_registration(self, registration: dict[str, Any]) -> bool:
        """保存注册信息

        Args:
            registration: 注册信息

        Returns
        -------
            bool: 是否保存成功
        """
        try:
            address = registration.get("address")
            if not address:
                logger.error("注册信息缺少地址")
                return False

            existing = self.get(address)
            if existing:
                return self.update(address, registration)
            else:
                return self.add(registration)
        except Exception as e:
            logger.error(f"保存注册信息失败: {str(e)}")
            return False

    def get_registration(self, address: str) -> dict[str, Any] | None:
        """获取注册信息

        Args:
            address: 钱包地址

        Returns
        -------
            Optional[Dict[str, Any]]: 注册信息，如果不存在则返回None
        """
        return self.get(address)

    def flush(self) -> bool:
        """刷新数据

        Returns
        -------
            bool: 是否刷新成功
        """
        try:
            self._csv.load()
            return True
        except Exception as e:
            logger.error(f"刷新数据失败: {str(e)}")
            return False
