import os
import time
from collections.abc import Callable
from functools import wraps
from typing import Any, Optional

from loguru import logger
from retry import retry


def measure_time(func: Callable) -> Callable:
    """测量函数执行时间的装饰器

    Args:
        func: 要装饰的函数

    Returns
    -------
        Callable: 装饰后的函数
    """

    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        # 获取进程ID
        pid = os.getpid()
        # 获取任务ID（如果存在）
        task_id = ""
        if args and hasattr(args[0], "id"):
            task_id = f"【{args[0].id}】"

        # 使用实例的logger
        if args and hasattr(args[0], "logger"):
            # 使用已经绑定了task_id的logger
            args[0].logger.info(f"【方法:{func.__name__}】执行耗时: {end_time - start_time:.2f}秒")
        else:
            # 如果没有实例logger，则使用全局logger
            logger.info(f"【PID:{pid}】{task_id} 【方法:{func.__name__}】执行耗时: {end_time - start_time:.2f}秒")

        return result

    return wrapper
