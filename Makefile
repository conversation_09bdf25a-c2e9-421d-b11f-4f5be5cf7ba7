.PHONY: format check install install-dev clean help

# 安装项目依赖
install:
	pip install -e .

# 安装开发依赖
install-dev:
	pip install -e ".[dev]"
	pre-commit install

# 格式化代码
format:
	black .
	isort .

# 检查代码格式（不修复）
check:
	black --check --diff .
	isort --check-only --diff .

# 清理缓存文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +

# 显示帮助
help:
	@echo "可用的命令："
	@echo "  install    - 安装项目依赖"
	@echo "  install-dev - 安装开发工具（Black, isort, pre-commit等）"
	@echo "  format     - 格式化所有Python代码（Black + isort）"
	@echo "  check      - 检查代码格式（不修复）"
	@echo "  clean      - 清理缓存文件"
