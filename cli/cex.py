import random
from time import sleep

import click
from loguru import logger

from src.cex.okx import OKX, get_amount
from src.utils.common import get_project_root_path

WITHDRAW_CONFIG = {
    "g_galxe": {
        "start": 80,  # 最小提现数量
        "end": 110,  # 最大提现数量
        "decimal_places_range": (1, 2),  # token小数位数范围
        "sleep_range": (120, 1800),  # 操作间隔时间范围
        "token": "G",
        "network": "Gravity Alpha Mainnet",
    },
    "eth_eth": {
        "start": 0.01138,  # 最小提现数量
        "end": 0.0125,  # 最大提现数量
        "decimal_places_range": (5, 7),  # 小数位数范围
        "sleep_range": (120, 1800),  # 操作间隔时间范围
        "token": "ETH",
        "network": "ERC20",
    },
    "eth_arbitrum": {
        "start": 0.0023,  # 最小提现数量
        "end": 0.0025,  # 最大提现数量
        "decimal_places_range": (4, 5),  # 小数位数范围
        "sleep_range": (180, 900),
        "token": "ETH",
        "network": "Arbitrum One",
    },
    "eth_optimism": {
        "start": 0.003,  # 最小提现数量
        "end": 0.005,  # 最大提现数量
        "decimal_places_range": (5, 6),  # 小数位数范围
        "sleep_range": (120, 600),
        "token": "ETH",
        "network": "Optimism",
    },
    "eth_base": {
        "start": 0.0023,  # 最小提现数量
        "end": 0.0025,  # 最大提现数量
        "decimal_places_range": (4, 5),  # 小数位数范围
        "sleep_range": (180, 900),
        "token": "ETH",
        "network": "Base",
    },
    "pol_polygon": {
        "start": 24,  # 最小提现数量
        "end": 30,  # 最大提现数量
        "decimal_places_range": (1, 3),  # 小数位数范围
        "sleep_range": (180, 900),
        "token": "POL",
        "network": "Polygon",
    },
}


def _validate_address(address: str) -> bool:
    """验证地址格式

    Args:
        address: 待验证的地址

    Returns:
        bool: 地址是否有效
    """
    # 基本的地址格式验证
    return bool(address and address.startswith("0x") and len(address) == 42)


def _get_addresses():
    address_file = get_project_root_path() + "/cli/address.txt"
    with open(address_file, "r") as f:
        address = f.read().splitlines()

    # 验证地址格式
    for addr in address:
        if not _validate_address(addr):
            logger.error(f"地址 {addr} 格式不正确")
            return []

    return address


def _withdraw(name: str):
    config = WITHDRAW_CONFIG[name]
    addresses = _get_addresses()
    if len(addresses) == 0:
        logger.error(f"获取地址为空, 请检查配置文件")
        return

    if not config:
        logger.error(f"配置文件中没有找到 {name} 的配置")
        return

    # 随机打乱
    random.shuffle(addresses)

    start = config["start"]
    end = config["end"]
    decimal_places_range = config["decimal_places_range"]
    sleep_range = config["sleep_range"]
    token = config["token"]
    network = config["network"]

    print(
        f"提现 {token} 至 {network}: {start} - {end}, 小数位数范围: {decimal_places_range}, 操作间隔时间范围:"
        f" {sleep_range}"
    )

    for i, addr in enumerate(addresses):
        random_count = random.randint(decimal_places_range[0], decimal_places_range[1])
        amount = get_amount(start, end, random_count)
        OKX().withdraw(addr, token, network, amount)
        if i < len(addresses) - 1:
            sleep_time = random.randint(sleep_range[0], sleep_range[1])
            logger.info(f"等待 {sleep_time}秒后继续")
            sleep(sleep_time)


@click.group()
def cli():
    pass


@cli.command("cn")
@click.argument("token", default="ETH")
def check_supported_networks(token):
    # 查看支持的链
    # token：BTC、ETH、SOL、BNB、POL等
    currencies = OKX().funding_api.get_currencies(token)
    logger.info(f"支持的链: {currencies}")


@cli.command("okxg")
def okx_withdraw_g():
    _withdraw("g_galxe")


@cli.command("okxe")
def okx_withdraw_eth():
    _withdraw("eth_eth")


@cli.command("okxpol")
def okx_withdraw_pol():
    _withdraw("pol_polygon")


@cli.command("okxarb")
def okx_withdraw_arb_eth():
    _withdraw("eth_arbitrum")


@cli.command("okxop")
def okx_withdraw_op_eth():
    _withdraw("eth_optimism")


@cli.command("okxbase")
def okx_withdraw_base_eth():
    _withdraw("eth_base")


# 1. 钱包地址，需要在okx交易所设置白名单，然后设成免验证才能提现
# https://www.okx.com/zh-hant/balance/withdrawal-address/eth

# 2. api_key、secret、passphrase 在okx交易所设置, 需要ip白名单
# https://www.okx.com/zh-hant/account/my-api


# 提现至 ETH主网 ETH
# python3 cli/cex.py okxe

# 提现至 Galxe G
# python3 cli/cex.py okxg

if __name__ == "__main__":
    cli()
