import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.hhcsv import H<PERSON>SV

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/dc.log", rotation="10MB", level="SUCCESS")


def filter_data_by_indices(data, indices: list[int]) -> list[dict]:
    filtered = []
    for row in data:
        try:
            row_id = row.get("id")
            if row_id is None:
                logger.warning(f"跳过缺少id的数据: {row}")
                continue

            if int(row_id) in indices:
                filtered.append(row)
        except ValueError:
            logger.warning(f"无法将id转换为整数: {row}")
            continue
    return filtered


def _query_all(file_path: str):
    hhcsv = HHCSV(file_path)
    return hhcsv.query()


def _login(type: BrowserType, index: int):
    browser = BrowserController(type, str(index))
    try:
        result = browser.login_gmail()
        if not result:
            return False

        # 登录成功后更改语言
        browser.change_gmail_language()
        return True
    except Exception as e:
        logger.error(f"【{index}】登录gmail失败: {e}")
        return False
    finally:
        browser.close_page()


def _update_recovery_email(
    type: BrowserType,
    index: int,
    email_password: str,
    recovery_email: str,
    new_recovery_email: str,
    new_recovery_email_pwd: str,
    new_recovery_proxy_email: str,
):
    browser = BrowserController(type, str(index))
    try:
        return browser.update_gmail_recovery_email(
            email_password, recovery_email, new_recovery_email, new_recovery_email_pwd, new_recovery_proxy_email
        )
    except Exception as e:
        logger.error(f"【{index}】登录gmail失败: {e}")
        return False
    finally:
        browser.close_page()


def _update_password(type: BrowserType, index: int):
    browser = BrowserController(type, str(index))
    try:
        return browser.update_gmail_password()
    except Exception as e:
        logger.error(f"【{index}】更新gmail密码失败: {e}")
        return False
    finally:
        browser.close_page()


def _enable_2fa(type: BrowserType, index: int):
    browser = BrowserController(type, str(index))
    try:
        browser.window_max()
        if browser.browser_config.email_two_fa:
            logger.success(f"【{index}】Gmail已经获取2FA Key")
        else:
            result = browser.get_gmail_2fa()
            if not result:
                logger.error(f"【{index}】Gmail开启2FA失败, 未获取到2FA Key")
                return False
        result = browser.gmail_enable_2fa()
        if result:
            logger.success(f"【{index}】开启Gmail 2FA成功")
        return result
    except Exception as e:
        logger.error(f"【{index}】开启Gmail 2FA失败: {e}")
        return False
    finally:
        browser.close_page()


def _get_gmail_imap4_password(type: BrowserType, index: int, is_update: bool = False):
    browser = BrowserController(type, str(index))
    try:
        return browser.get_gmail_imap4_password(is_update)
    except Exception as e:
        logger.error(f"【{index}】获取Gmail Imap4 Password失败: {e}")
        return False
    finally:
        browser.close_page()


def _check_status(type: BrowserType, index: int, login: bool):
    browser = BrowserController(type, str(index))
    try:
        return browser.get_gmail_status(login)
    except Exception as e:
        logger.error(f"【{index}】检查Gmail状态失败: {e}")
        return False
    finally:
        browser.close_page()


def _change_language(type: BrowserType, index: str):
    browser = BrowserController(type, str(index))
    try:
        return browser.change_gmail_language()
    except Exception as e:
        logger.error(f"【{index}】更改Gmail语言失败: {e}")
        return False
    finally:
        browser.close_page()


def _get_backup_code(type: BrowserType, index: str):
    browser = BrowserController(type, str(index))
    try:
        return browser.gmail_get_backup_code()
    except Exception as e:
        logger.error(f"【{index}】Gmail获取Backup Code失败: {e}")
        return False
    finally:
        browser.close_page()


@click.group()
def cli():
    pass


@cli.command("login")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def login(index, type):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _login(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail登录失败: {idxs}")


@cli.command("up")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_password(index, type):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _update_password(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail更新密码失败: {idxs}")


@cli.command("fa")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def enable_2fa(index, type):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _enable_2fa(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail开启2FA失败: {idxs}")


@cli.command("im")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-u", "--update", is_flag=True, default=False, help="是否更新IMAP4密码")
def get_gmail_imap4_password(index, type, update):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _get_gmail_imap4_password(type, _index, update):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail获取imap4密码失败: {idxs}")


@cli.command("ue")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def update_recovery_email(index, type):
    required_cols = [
        "id",
        "email_password",
        "recovery_email",
        "new_recovery_email",
        "new_recovery_email_pwd",
        "new_recovery_proxy_email",
    ]
    file_path = input(
        f"请输入Excel数据文件的完整路径(表头格式：{required_cols})：\n"
        "提示：\n"
        "1. 支持的文件格式：csv\n"
        "2. 支持拖拽文件到终端\n"
        "3. 输入 'e' 退出程序\n"
        "4. 文件中id列必须跟data/*.csv中的id列一致, 否则会更新数据失败\n"
        "> "
    ).strip()

    file_path = file_path.strip("'\"")
    data = _query_all(file_path)

    # 根据index过滤数据
    if index:
        try:
            indices = parse_indices(index)
            filtered_data = filter_data_by_indices(data, indices)
            if not filtered_data:
                logger.warning(f"未找到匹配的数据，请检查输入的序号 {index} 是否正确")
                return
        except ValueError as e:
            logger.error(f"解析索引出错: {e}")
            return
    else:
        filtered_data = data

    failed_indices = []
    for row in filtered_data:
        _id = row.get("id")
        try:
            # 从 row 中获取所需数据
            email_password = row.get("email_password", "")
            recovery_email = row.get("recovery_email", "")
            new_recovery_email = row.get("new_recovery_email", "")
            new_recovery_email_pwd = row.get("new_recovery_email_pwd", "")
            new_recovery_proxy_email = row.get("new_recovery_proxy_email", "")
            result = _update_recovery_email(
                type,
                _id,
                email_password,
                recovery_email,
                new_recovery_email,
                new_recovery_email_pwd,
                new_recovery_proxy_email,
            )
            if result:
                logger.success(f"【{_id}】更新Gmail辅助邮箱成功")
            else:
                failed_indices.append(str(_id))
        except Exception as e:
            failed_indices.append(str(_id))
            logger.error(f"【{_id}】更新Gmail辅助邮箱失败: {e}")

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail更新辅助邮箱失败: {idxs}")


@cli.command("check")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-l", "--login", is_flag=True, help="是否需要登录")
def check_status(index, type, login):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _check_status(type, _index, login):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail检查状态失败: {idxs}")


@cli.command("cl")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def change_language(index, type):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _change_language(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail语言切换失败: {idxs}")


@cli.command("bc")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=default_browser_type)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def get_backup_code(index, type):
    indices = parse_indices(index)
    failed_indices = []

    for _index in indices:
        if not _get_backup_code(type, _index):
            failed_indices.append(str(_index))

    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"Gmail获取Backup Code失败: {idxs}")


# 登录
# python cli/gmail.py login -t ads -i 1-100

# 修改密码
# python cli/gmail.py up -t ads -i 1-100

# 开启2fa
# python cli/gmail.py fa -t ads -i 1-100

# 开启imap4
# python cli/gmail.py im -t ads -i 1-100

# 更新辅助邮箱
# python cli/gmail.py ue -t ads -i 1-100

# 检查状态, -l参数表示当账号未登陆时是否登录
# python cli/gmail.py check -l -t ads -i 1-100


if __name__ == "__main__":
    cli()
    # _login(BrowserType.ADS, "1")
    # _update_password(BrowserType.ADS, "1")
    # _get_gmail_2fa(BrowserType.ADS, "1")
