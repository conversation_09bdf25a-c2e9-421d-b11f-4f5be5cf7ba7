import sys
import requests
import click
import os
import json
import shutil
from pathlib import Path
from time import sleep
from fake_useragent import User<PERSON>gent
from typing import List, Dict, Optional
from src.evm.create_wallet import generate_bip39_mnemonic, get_address_and_private_key
from src.utils.secure_encryption import SecureEncryption
from src.utils.hhcsv import HHCSV
from src.controllers.browser_controller import BrowserController, BrowserType
from src.utils.common import parse_indices
from loguru import logger

default_browser_type = BrowserType.CHROME

user_agent = UserAgent(browsers=["Chrome"], os=["Windows", "Mac OS X"])
USER_DATA_PATH = os.getenv("USER_DATA_PATH")

logger.add("logs/chrome.log", rotation="10MB", level="SUCCESS")


class ChromeConfigGenerator:
    def __init__(self):
        self.csv_tool = HHCSV(
            "data/chrome.csv",
            headers=[
                "id",
                "browser_id",
                "user_agent",
                "proxy",
                "mnemonic",
                "evm_private_key",
                "evm_address",
            ],
        )
        self.used_ports = self._load_existing_ports()
        self.proxy_ranges = [(42000, 42284), (52000, 52088)]
        self.max_id = self._get_max_id()

    def _load_existing_ports(self) -> set:
        """从现有的CSV文件中加载已使用的端口"""
        used_ports = set()
        try:
            rows = self.csv_tool.query()
            for row in rows:
                if row.get("proxy"):  # 确保 proxy 字段存在且有值
                    try:
                        # 从 proxy 字符串中提取端口号（格式：ip:port）
                        port = int(row["proxy"].split(":")[-1])
                        used_ports.add(port)
                    except (ValueError, IndexError):
                        continue  # 如果解析失败，跳过该条记录
        except Exception as e:
            print(f"警告：读取现有CSV文件时出错: {str(e)}")
        return used_ports

    def _get_max_id(self) -> int:
        """从CSV文件中获取最大的ID"""
        try:
            rows = self.csv_tool.query()
            if not rows:
                return 0
            # 直接获取最后一条数据的ID
            return int(rows[-1]["id"])
        except Exception as e:
            print(f"警告：读取CSV文件获取最大ID时出错: {str(e)}")
            return 0

    def generate_user_agent(self) -> str:
        """生成随机的Chrome用户代理"""
        try:
            return user_agent.random
        except Exception as e:
            print(f"生成用户代理时出错: {str(e)}")
            return ""

    def test_proxy(self, port: int) -> bool:
        """测试代理是否可用"""
        proxy = f"http://127.0.0.1:{port}"
        try:
            response = requests.get(
                "https://httpbin.org/ip",
                proxies={"http": proxy, "https": proxy},
                timeout=3,
            )
            print(response)
            return response.status_code == 200
        except:
            return False

    def get_available_proxy_port(self) -> int:
        """获取可用的代理端口"""
        try:
            retry_count = 3
            for _ in range(retry_count):
                for start, end in self.proxy_ranges:
                    for port in range(start, end + 1):
                        if port not in self.used_ports and self.test_proxy(port):
                            self.used_ports.add(port)
                            return port
            return 0
        except Exception as e:
            print(f"获取可用代理端口时出错: {str(e)}")
            return 0

    def generate_config(self, index: int) -> Dict:
        """生成单个浏览器配置"""
        if not isinstance(index, int) or index < 1:
            raise ValueError("index必须是大于0的整数")

        # 生成助记词和钱包信息
        mnemonic = generate_bip39_mnemonic()
        result = get_address_and_private_key(mnemonic, is_encrypt=False)
        private_key = result["evm_private_key"]
        address = result["evm_address"]

        # 验证地址格式
        if not address.startswith("0x") or len(address) != 42:
            raise ValueError("生成的钱包地址格式不正确")

        # 加密敏感信息
        encrypted_mnemonic = SecureEncryption.encrypt(mnemonic)
        encrypted_private_key = SecureEncryption.encrypt(private_key)

        return {
            "id": index,
            "browser_id": index,
            "user_agent": self.generate_user_agent(),
            # "proxy": f"127.0.0.1:{self.get_available_proxy_port()}",
            "proxy": "",
            "mnemonic": encrypted_mnemonic,
            "evm_private_key": encrypted_private_key,
            "evm_address": address,
        }

    def generate_configs(self, count: int) -> List[Dict]:
        """批量生成浏览器配置"""
        configs = []
        start_id = self.max_id + 1
        for i in range(count):
            configs.append(self.generate_config(start_id + i))
        return configs

    def save_to_csv(self, configs: List[Dict], filename: str = "data/chrome.csv"):
        """保存配置到CSV文件"""
        if not configs:
            raise ValueError("配置列表不能为空")

        try:
            for config in configs:
                self.csv_tool.add_row(config)
        except Exception as e:
            raise Exception(f"保存CSV文件失败: {str(e)}")


def check_dependencies():
    """检查必要的依赖是否已安装"""
    try:
        import fake_useragent
        import requests
    except ImportError as e:
        print(f"缺少必要的依赖: {str(e)}")
        print("请执行: pip install fake-useragent requests")
        sys.exit(1)


def copy_user_directories(master_profile: str, target_prefix: str, start_num: int, count: int) -> None:
    """
    复制Chrome用户目录

    Args:
        master_profile: 主用户目录路径，
        windows默认：
        macos默认：~/Library/Application Support/Google/Chrome/Profile1

        target_prefix: 目标保存用户目录前缀
        start_num: 起始编号
        count: 需要复制的份数

    Raises:
        FileNotFoundError: 主用户目录不存在
        ValueError: 输入参数无效
        RuntimeError: 复制过程发生错误
    """
    try:
        # 输入验证
        if not master_profile or not target_prefix:
            raise ValueError("主用户目录路径和目标前缀不能为空")
        if count < 0 or start_num < 0:
            raise ValueError("count 和 start_num 必须为非负数")

        # 转换为主路径对象并验证
        master_path = Path(master_profile).resolve()
        if not master_path.exists():
            raise FileNotFoundError(f"主用户目录 {master_profile} 不存在")
        if not master_path.is_dir():
            raise ValueError(f"主用户目录 {master_profile} 不是一个目录")

        # 复制目录
        for i in range(count):
            target_num = start_num + i
            target_path = Path(target_prefix) / str(target_num)
            target_path = target_path.resolve()

            if target_path.exists():
                logger.warning(f"跳过: 目标目录 {target_path} 已存在")
                continue

            try:
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copytree(master_path, target_path, dirs_exist_ok=False)
                logger.success(f"已创建目录: {target_path}")
            except Exception as e:
                logger.error(f"复制目录到 {target_path} 异常，error={str(e)}")
    except Exception as e:
        logger.error(f"初始化用户目录发生异常：{str(e)}")


# 更新插件
def _update_extensions(template_dir: str, indices: list[int]):
    """
    更新指定用户目录的插件

    Args:
        template_dir: 模板用户目录路径 目前存放在index 0的目录 ~/chrome/cache/0/Default/
        indices: 需要更新的用户目录编号列表
    """
    try:
        for index in indices:
            user_dir = Path(USER_DATA_PATH) / str(index)
            user_dir_path = user_dir.resolve()

            if not user_dir_path.exists():
                logger.warning(f"[{index}] 用户目录 {user_dir} 不存在，跳过")
                continue

            try:
                # 判断是否有Default文件夹,如果有先删除,在拷贝
                default_dir = user_dir_path / "Default"
                if default_dir.exists():
                    logger.info(f"[{index}] 正在删除插件目录...")
                    shutil.rmtree(default_dir)
                    if not default_dir.exists():
                        logger.success(f"[{index}] 删除插件目录成功")

                logger.info(f"[{index}] 正在更新插件目录...")
                shutil.copytree(template_dir, default_dir)
                if default_dir.exists():
                    logger.success(f"[{index}] 更新插件目录成功")
                else:
                    logger.error(f"[{index}] 更新插件目录失败")

            except Exception as e:
                logger.error(f"[{index}] 更新插件目录发生异常，error={str(e)}")
                continue
    except Exception as e:
        logger.error(f"更新插件目录发生异常，error={str(e)}")


def find_extension_version(template_dir: str, extension_id: str) -> Optional[str]:
    """
    从模板用户目录中查找指定插件的版本号

    Args:
        template_dir: 模板用户目录路径
        extension_id: 插件 ID

    Returns:
        插件版本号（字符串），如果未找到则返回 None
    """
    template_path = Path(template_dir).resolve()
    extensions_path = template_path / "Extensions" / extension_id

    if not extensions_path.exists():
        return None

    # 查找版本号（Extensions/<extension_id> 下的子文件夹名）
    for version_dir in extensions_path.iterdir():
        if version_dir.is_dir() and (version_dir / "manifest.json").exists():
            return version_dir.name

    return None


def install_extension_to_users(indices: list[int], template_dir: str, extension_id: str) -> None:
    try:
        # 验证模板目录
        template_path = Path(template_dir).resolve()
        if not template_path.exists() or not template_path.is_dir():
            raise FileNotFoundError(f"模板用户目录 {template_dir} 不存在或不是目录")

        # 查找插件版本号
        extension_version = find_extension_version(template_dir, extension_id)
        if not extension_version:
            raise FileNotFoundError(f"插件 {extension_id} 在模板目录 {template_dir} 中未找到")

        # 插件源路径
        extension_path = template_path / "Extensions" / extension_id / extension_version
        if not (extension_path / "manifest.json").exists():
            raise FileNotFoundError(f"插件路径 {extension_path} 不包含 manifest.json")

        # 为每个目标用户目录安装插件
        for index in indices:
            user_dir = Path(USER_DATA_PATH) / str(index)
            user_dir_path = user_dir.resolve()

            if not user_dir_path.exists():
                logger.warning(f"用户目录 {user_dir} 不存在，跳过")
                continue

            try:
                # 创建目标插件目录
                target_extensions_dir = user_dir_path / "Extensions" / extension_id / extension_version
                if target_extensions_dir.exists():
                    logger.warning(f"【{index}】已经安装插件，跳过。。。")
                    continue

                target_extensions_dir.parent.mkdir(parents=True, exist_ok=True)

                # 复制插件文件夹
                if target_extensions_dir.exists():
                    shutil.rmtree(target_extensions_dir)
                shutil.copytree(extension_path, target_extensions_dir)

                # 更新 Preferences 文件
                preferences_path = user_dir_path / "Preferences"
                if not preferences_path.exists():
                    preferences_path.parent.mkdir(parents=True, exist_ok=True)
                    # 创建空的 Preferences 文件
                    with open_browser(preferences_path, "w", encoding="utf-8") as f:
                        json.dump({}, f)

                # 读取现有 Preferences
                with open_browser(preferences_path, "r", encoding="utf-8") as f:
                    preferences = json.load(f)

                # 添加插件信息
                preferences.setdefault("extensions", {}).setdefault("settings", {})
                preferences["extensions"]["settings"][extension_id] = {
                    "state": 1,  # 1 表示启用
                    "manifest": None,  # 可选：从 manifest.json 读取
                    "was_installed_by_default": False,
                }

                # 保存 Preferences
                with open_browser(preferences_path, "w", encoding="utf-8") as f:
                    json.dump(preferences, f, indent=2)

                logger.success(f"【{index}】安装成功，插件 {extension_id} (版本: {extension_version})")

            except Exception as e:
                logger.error(f"【{index}】插件安装异常，error={str(e)}")
                continue
    except Exception as e:
        logger.error(f"插件安装异常，error={str(e)}")


def main(count: int):
    """主函数"""
    if count <= 0:
        raise ValueError("生成数量必须大于0")
    if count > 1000:  # 添加上限防止生成太多
        raise ValueError("单次生成数量不能超过1000")

    generator = ChromeConfigGenerator()
    configs = generator.generate_configs(count)
    generator.save_to_csv(configs)
    print(f"成功生成 {count} 个Chrome浏览器配置")


def _clear_browser_cache(index):
    try:
        master_path = Path(USER_DATA_PATH).resolve()
        if master_path.exists():
            cache_path = Path(USER_DATA_PATH) / str(index)
            cache_path = cache_path.resolve()
            wait_time = 5
            logger.info(f"{wait_time}s后删除缓存 {cache_path}")
            shutil.rmtree(cache_path)

            result = Path(cache_path).exists()
            if not result:
                logger.info(f"{cache_path} 删除成功")
                sleep(3)
            else:
                logger.error(f"{cache_path} 删除失败")
    except Exception as e:
        logger.error(f"删除{index}浏览器缓存失败,error={str(e)}")


def _check_verified(email_list):
    """
    检查邮箱列表中是否有已验证的邮箱.

    Parameters
    ----------
        email_list: list

    Returns
    -------
        tuple: (bool, str)
    """
    for item in email_list:
        email = item.get("email", "").lower()
        is_verified = item.get("email_verified", False)

        # # 检查是否是icloud邮箱且已验证
        if is_verified:
            return True, email

    return False, None


def _update_email_x(index, is_clear):
    browser = None
    try:
        browser = BrowserController(default_browser_type, str(index))

        # 判断是否登录
        result = browser.login_x(False)
        if not result:
            logger.error(f"{index} X登录失败...")
            return False

        # 检查邮箱是否已经更新过
        result = browser.get_emails_x()
        is_verified, email = _check_verified(result)
        _email = browser.browser_config.x_proxy_email or browser.browser_config.x_email
        if is_verified and email.lower() == _email.lower():
            logger.success(f"{index} 已经更新过, 当前邮箱:{email}")
            return True

        result = browser.update_email_x()
        return result
    except Exception as e:
        logger.error(f"浏览器{index}更新邮箱失败: {e}")
    finally:
        if browser:
            browser.close_page()

        if is_clear:
            _clear_browser_cache(index)


def _update_password_x(_index, is_clear):
    try:
        browser = BrowserController(default_browser_type, str(_index))
        # result = browser.login_x(False)
        # if not result:
        #     logger.error(f"{_index} X 登录失败...")
        #     return False

        result = browser.update_password_x()
        browser.close_page()
        return result
    except Exception as e:
        logger.error(f"浏览器{_index}修改twitter密码失败: {e}")
    finally:
        if is_clear:
            _clear_browser_cache(_index)


@click.group()
def cli():
    pass


@cli.command("g")
@click.option("-n", "--number", type=int, default=100, help="生成数量")
def generate(number: int):
    main(number)


@cli.command("ips")
def get_available_proxy_port():
    proxy_ranges = list(range(42000, 42284))
    for port in proxy_ranges:
        result = ChromeConfigGenerator().test_proxy(port)
        if result:
            print(f"http://127.0.0.1:{port}")

    proxy_ranges = list(range(52000, 52088))
    for port in proxy_ranges:
        result = ChromeConfigGenerator().test_proxy(port)
        if result:
            print(f"http://127.0.0.1:{port}")


@cli.command("open")
@click.option("-i", "--index", type=str, help="浏览器序号")
@click.option("-u", "--url", type=str, help="网页地址")
def open_browser(index: str, url: str):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(default_browser_type, str(_index))
            browser.okx_wallet_login()
            browser.open_url(url)
        except Exception as e:
            logger.error(f"浏览器{_index}登录失败: {e}")


@cli.command("uex")
@click.option("-i", "--index", type=str, help="浏览器序号")
@click.option("-c", "--clear", type=bool, help="是否清理浏览器缓存")
def update_email_x(index: str, clear: bool = False):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _update_email_x(_index, clear)
        if not result:
            failed_indices.append(str(_index))

    if failed_indices:
        ids = ",".join(failed_indices)
        logger.error(f"修改twitter邮箱失败IDS:{ids}")


@cli.command("upx")
@click.option("-i", "--index", type=str, help="浏览器序号")
@click.option("-c", "--clear", type=bool, help="是否清理浏览器缓存")
def update_password_x(index: str, clear: bool = False):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        result = _update_password_x(_index, clear)
        if not result:
            failed_indices.append(str(_index))

    if failed_indices:
        ids = ",".join(failed_indices)
        logger.error(f"修改twitter密码失败IDS:{ids}")


@cli.command("init")
@click.option("-d", "--template_dir", type=str, help="模版目录")
@click.option("-s", "--start", type=int, help="起始编号")
@click.option("-c", "--count", type=int, help="次数")
def init_user_dir(template_dir: str, start: int, count: int):
    copy_user_directories(template_dir, USER_DATA_PATH, start, count)


@cli.command("exts")
@click.option("-d", "--template_dir", type=str, help="模版目录")
@click.option("-i", "--index", type=str, help="浏览器序号")
def update_extensions(template_dir: str, index: str):
    indices = parse_indices(index)
    _update_extensions(template_dir, indices)


# @cli.command("install")
# @click.option("-i", "--index", type=str, help="浏览器序号")
# @click.option("-d", "--template_dir", type=str, help="目标路径")
# @click.option("-eid", "--extension_id", type=str, help="插件ID")
# def install_extension(index: str, template_dir: str, extension_id: str):
#     indices = parse_indices(index)
#     install_extension_to_users(indices, template_dir, extension_id)


# python3 cli/chrome.py g

# python3 cli/chrome.py open -i 1-10

if __name__ == "__main__":
    cli()
