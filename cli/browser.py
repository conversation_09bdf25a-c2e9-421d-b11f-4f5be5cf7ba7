import os
import shutil

def copy_default_to_folders():
    USER_DATA_PATH = "C:\\kt\\browsers\\chrome"
    source_folder = os.path.join(USER_DATA_PATH, "1", "Default")
    
    for i in range(2, 3):  # 从 2 到 100
        target_folder = os.path.join(USER_DATA_PATH, str(i))
        print(target_folder)
        # 创建目标文件夹
        os.makedirs(target_folder, exist_ok=True)
        
        # 拷贝 Default 文件夹
        target_default_folder = os.path.join(target_folder, "Default")
        
        # 如果目标Default文件夹存在，先删除它
        if os.path.exists(target_default_folder):
            shutil.rmtree(target_default_folder)
            print(f"已删除原有文件夹: {target_default_folder}")
        
        shutil.copytree(source_folder, target_default_folder)  # 删除 dirs_exist_ok=True 参数，因为我们已经确保目标文件夹不存在

def copy_file_to_folders():
    BASE_PATH = r"D:\kt\browsers\chrome"
    # Path to source file (MetaMask extension script)
    source_file = r"D:\kt\browsers\chrome\1\Default\Extensions\nkbihfbeogaeaoehlefnkodbefgpgknn\12.13.1_0\scripts\runtime-lavamoat.js"
    
    for i in range(2, 101):  # 从 2 到 100
        # Create target path with the correct number i
        target_folder = os.path.join(BASE_PATH, str(i), "Default", "Extensions", "nkbihfbeogaeaoehlefnkodbefgpgknn", "12.13.1_0", "scripts")
        print(f"Copying to: {target_folder}")
        
        # 创建目标文件夹
        os.makedirs(target_folder, exist_ok=True)
        
        # 拷贝文件到目标文件夹
        target_file = os.path.join(target_folder, "runtime-lavamoat.js")
        shutil.copy2(source_file, target_file)  # 使用copy2代替copytree，因为这是单个文件
    
    print("Copy completed successfully!")

#copy_default_to_folders()
copy_default_to_folders()