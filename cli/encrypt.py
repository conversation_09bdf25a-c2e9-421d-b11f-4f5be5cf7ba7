import click
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from src.utils.secure_encryption import SecureEncryption
from src.repositories.browser_repository import CSVBrowserRepository, BrowserRepository
from config import DEFAULT_BROWSER_TYPE

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


@click.group()
def cli():
    pass


# 加密指定浏览器数据表中的某一列数据
@cli.command("ec")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option(
    "-c",
    "--column",
    type=str,
    prompt="请输入需要加密的列名",
    help="需要加密的列名(例如: password, token)",
)
def encrypt_column(type, column):
    """加密指定浏览器数据表中的某一列数据"""
    try:
        repository = CSVBrowserRepository(type)
        encrypted_count = repository.encrypt_column(column)
        if encrypted_count > 0:
            logger.success(f"成功加密 {encrypted_count} 条数据")
        else:
            logger.warning(f"没有找到需要加密的数据")
    except Exception as e:
        logger.error(f"加密列数据过程出错: {e}")


# 解密指定浏览器数据表中的某一列数据
@cli.command("dc")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option(
    "-c",
    "--column",
    type=str,
    prompt="请输入需要解密的列名",
    help="需要解密的列名(例如: password, token)",
)
def decrypt_column(type, column):
    """解密指定浏览器数据表中的某一列数据"""
    try:
        repository = CSVBrowserRepository(type)
        decrypted_count = repository.decrypt_column(column)
        if decrypted_count > 0:
            logger.success(f"成功解密 {decrypted_count} 条数据")
        else:
            logger.warning(f"没有找到需要解密的数据")
    except Exception as e:
        logger.error(f"解密列数据过程出错: {e}")


# 加密
@cli.command("e")
@click.option(
    "-t", "--plaintext", type=str, prompt="请输入需要加密的内容", help="需要加密的内容"
)
def encrypt(plaintext):
    """给明文加密"""
    try:
        result = SecureEncryption.encrypt(plaintext)
        if result:
            logger.success(f"加密成功: {result}")
        else:
            logger.error(f"加密失败")
    except Exception as e:
        logger.error(f"加密过程出错: {e}")


# 解密
@cli.command("d")
@click.option(
    "-t", "--ciphertext", type=str, prompt="请输入密文", help="需要解密的密文"
)
def decrypt(ciphertext):
    """解密数据"""
    try:
        result = SecureEncryption.decrypt(ciphertext)
        if result:
            logger.success(f"解密成功: {result}")
        else:
            logger.error(f"解密失败")
    except Exception as e:
        logger.error(f"解密过程出错: {e}")


# 设置密码
@cli.command("sp")
def set_password():
    """设置密码"""
    password = input(
        """
    请输入加密密码：
    - 密码长度必须大于8位
    - 建议包含大小写字母、数字和特殊字符
    - 该密码将用于加密 data 文件夹下的敏感数据
    - 请务必牢记此密码，忘记将无法恢复数据
    - 如果已经设置过密码，请不要重复设置, 新密码无法解密旧密码加密的数据
    - 请输入 q 退出程序

    请输入: """
    )
    if password.lower() == "q":
        print("程序已退出")
        exit(0)
    SecureEncryption.set_password(password)


# 导入密码和salt, 用于恢复加密数据
@cli.command("eb")
@click.option("-p", "--password", type=str, prompt="请输入密码", help="密码")
@click.option("-s", "--salt", type=str, prompt="请输入salt", help="salt")
def import_password_and_salt(password, salt):
    """导入密码和salt"""
    if not password or not salt:
        logger.error("密码和salt不能为空")
        return

    try:
        SecureEncryption.set_password(password)
        SecureEncryption.set_salt_str(salt)
        logger.success("导入密码和salt成功")
    except Exception as e:
        logger.error(f"导入密码和salt失败: {e}")


# 导出密码和salt, 文件目录
@cli.command("ep")
@click.option(
    "-f",
    "--file",
    type=str,
    help="文件目录",
    required=False,  # 设置为非必须参数
    default=None,  # 设置默认值为 None
)
def export_password_and_salt(file):
    """导出密码和salt"""
    SecureEncryption().export_password_and_salt(file)


# 加密指定浏览器数据表中的某一列数据
# python3 cli/encrypt.py ec -c "列名"

# 解密指定浏览器数据表中的某一列数据
# python3 cli/encrypt.py dc -c "列名"

# 解密
# python3 cli/encrypt.py d -t "密文"

# 加密
# python3 cli/encrypt.py e -t "明文"

# 设置密码
# python3 cli/encrypt.py sp

# 导出密码和salt, 文件目录
# python3 cli/encrypt.py ep -f "文件目录"

# 导入密码和salt, 用于恢复加密数据
# python3 cli/encrypt.py eb -p "密码" -s "salt"


if __name__ == "__main__":
    cli()

    # repository = CSVBrowserRepository(BrowserType.ADS)
    # encrypted_count = repository.encrypt_column("x_password")
    # if encrypted_count > 0:
    #     logger.success(f"成功加密 {encrypted_count} 条数据")
    # else:
    #     logger.warning(f"没有找到需要加密的数据")
