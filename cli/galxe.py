import click
import random
from loguru import logger

from src.controllers import BrowserController
from src.browsers import BrowserType, BROWSER_TYPES
from time import sleep

from src.utils.common import generate_username

BIND_URL = "https://app.galxe.com/accountSetting/social"


class GalxeController:
    def __init__(self, index: int, type: BrowserType):
        self.browser = BrowserController(type, str(index))
        self.page = self.browser.page

    def clear_cache(self):
        return self.browser.clear_site_data("galxe.com")

    def register(self):
        last_tab = self.page.latest_tab
        user_name = generate_username()
        last_tab.ele('x://input[@placeholder="Enter username"]').input(user_name)
        sleep(1)
        last_tab.ele(
            'x://div[@class="flex justify-end"]/button[text()="Create"]'
        ).click()
        sleep(5)
        ele = last_tab.ele("Welcome!")
        if ele:
            last_tab.ele('x://button[./span[text()="Close"]]').click()

    def login(self, wallet_name="OKX"):
        for _ in range(3):
            self.page.latest_tab.get(BIND_URL)
            sleep(3)
            # 如果自动弹出登录窗口就直接关闭
            last_tab = self.page.latest_tab
            if wallet_name in last_tab.title:
                last_tab.close()

            last_tab = self.page.latest_tab
            ele = last_tab.ele(
                'x:(//button[@type="button" and contains(text(),"Log in")])[1]',
                timeout=6,
            )
            if not ele:
                logger.info(f"{self.index} 已登录, 无需重复登录...")
                return True

            sleep(2)
            ele.click()
            latest_tab = self.page.latest_tab
            latest_tab.ele(f'x://div[text()="{wallet_name}"]/../parent::div').click()
            sleep(3)
            ele = latest_tab.ele("Link Your Social TAGS", timeout=5)
            if ele:
                logger.success(f"{self.index} 银河登录成功...")
                return True

            self.page.wait.new_tab()

            latest_tab = self.page.latest_tab
            latest_tab.ele("x://button[2]").click()
            sleep(3)
            latest_tab = self.page.latest_tab
            latest_tab.ele("x://button[2]").click()

            sleep(3)

            # 检测是否已注册，未注册用户需先注册
            latest_tab = self.page.latest_tab
            ele = latest_tab.ele("Sign up for a Galxe ID", timeout=5)
            if ele:
                self.register()

            # 检测是否登录成功
            latest_tab = self.page.latest_tab
            ele = latest_tab.ele(
                'x:(//button[@type="button" and contains(text(),"Log in")])[1]',
                timeout=5,
            )
            if not ele:
                logger.success(f"{self.index} 银河登录成功...")
                return True

        logger.error(f"{self.index} 银河登录失败!")
        return False


@click.group()
def cli():
    pass


def _get_list(input_string):
    if not input_string:
        raise ValueError("输入不能为空")

    input_string = input_string.strip()
    if "," in input_string:
        integer_indices = [int(x) for x in input_string.split(",")]
        return integer_indices
    elif "-" in input_string:
        strings = input_string.split("-")
        start = int(strings[0])
        end = int(strings[1])
        return list(range(start, end + 1))
    else:
        return [int(input_string)]


# 清理缓存
# python galxe.py clear -t chrome -i 1
@cli.command("clear")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=BrowserType.CHROME,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def clear(index, type):
    indices = _get_list(index)
    for _index in indices:
        try:
            GalxeController(_index, type).clear_cache()
        except Exception as e:
            logger.error(f"{_index} 清理缓存失败: {e}")


if __name__ == "__main__":
    cli()
