import re

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BrowserType
from src.emails.imap4.email_client import EmailClient

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


def _last_email(credentials):
    logger.info(f"用户名: {credentials[0]}, 密码: {credentials[1]}")
    user = credentials[0]
    password = credentials[1]
    email = EmailClient(user, password).get_latest_email()
    content = clean_email_content(email.get("content"))
    subject = email.get("subject")
    folder = email.get("folder")

    logger.info(f"最新邮件标题: {subject}")
    logger.info(f"最新邮件: {content}")
    logger.info(f"文件夹: {folder}")


def clean_email_content(content):
    """清理邮件内容，去除 HTML 标签和多余的空白字符.

    Args:
        content (str): 邮件内容

    Returns
    -------
        str: 清理后的邮件内容
    """
    # 使用正则表达式移除 HTML 标签
    clean_text = re.sub(r"<[^>]+>", "", content)
    # 移除多余的空白字符和空行，但保留段落之间的换行
    clean_text = re.sub(r"\s*\n\s*\n\s*", "\n\n", clean_text)
    clean_text = re.sub(r"\s+", " ", clean_text)

    # 移除开头和结尾的空白字符
    clean_text = clean_text.strip()

    # 移除重复的感叹号和空格
    clean_text = re.sub(r"!+", "!", clean_text)
    clean_text = re.sub(r"\s+", " ", clean_text)

    return clean_text


@click.group()
def cli():
    pass


@cli.command("last")
@click.argument("credentials", nargs=2, type=str)
def last_email(credentials):
    """
    获取最后一条邮件.

    参数格式: EMAIL PASSWORD

    示例: python3 cli/email.<NAME_EMAIL> yourpassword
    """
    _last_email(credentials)


# 获取最后一条邮箱
# python3 cli/mailer.<NAME_EMAIL> yourpassword

if __name__ == "__main__":
    cli()
    # _last_email(["<EMAIL>", "j238j!ojkdbnsk!H"])
