import click
from loguru import logger
import time
from src.controllers import BrowserController
from src.browsers import BROWSER_TYPES, BrowserType
from src.emails import HotmailClient
from config import DEFAULT_BROWSER_TYPE
from src.utils.common import parse_indices

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


@click.group()
def cli():
    pass


@cli.command("open")
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-c", "--close", is_flag=True, default=False, help="是否关闭浏览器")
@click.option("-u", "--url", type=str, prompt="请输入浏览器url", help="浏览器url")
@click.option(
    "-n",
    "--new_tab",
    is_flag=True,
    default=False,
    help="是否在新标签页打开，默认为False",
)
def open(index, close, new_tab, type, url):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            # 初始化page
            browser.page
            browser.window_max()

            urls = [url]
            if "," in url:
                urls = url.split(",")
                new_tab = True
            for u in urls:
                browser.open_url(u, new_tab)
            if close:
                time.sleep(5)
                browser.close_page()
        except Exception as e:
            logger.error(f"{_index} 打开网页异常 {e}")


@cli.command("clear")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-u", "--url", type=str, prompt="请输入浏览器url", help="浏览器url")
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def clear(index, url, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            browser.page
            browser.clear_site_data(url)
            browser.close_page()
            logger.success(f"{_index} 清理站点缓存成功")
        except Exception as e:
            logger.error(f"{_index} 清理站点缓存失败: {e}")


@cli.command("extension")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-e", "--extension_id", type=str, prompt="请输入扩展插件id", help="扩展插件id")
@click.option("-s", "--status", type=bool, prompt="请输入扩展插件状态", help="扩展插件状态")
def extension(index, type, extension_id, status):
    indices = parse_indices(index)
    failed_indices = []
    for _index in indices:
        browser = BrowserController(type, str(_index))
        try:
            if browser.chrome_extension_status(extension_id, status):
                logger.success(f"{_index} 改变扩展插件状态成功")
            else:
                failed_indices.append(str(_index))
                logger.error(f"{_index} 改变扩展插件状态失败")
        except Exception as e:
            failed_indices.append(str(_index))
            logger.error(f"{_index} 改变扩展插件状态失败: {e}")
        finally:
            if browser:
                browser.close_page()
    if len(failed_indices) > 0:
        idxs = ",".join(failed_indices)
        logger.error(f"以下浏览器插件状态更新失败: {idxs}")


@cli.command("hotmail")
@click.option(
    "-c",
    "--is_clear",
    is_flag=True,
    default=False,
    help="是否清理缓存，默认为False",
)
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def hotmail_login(is_clear, type, index):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            hotmail_client = HotmailClient(browser)
            if is_clear:
                hotmail_client.clear_cache()
            hotmail_client.login()
        except Exception as e:
            logger.error(f"{_index} 登录Hotmail失败: {e}")


@cli.command("hotmail_change_password")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def hotmail_change_password(type, index):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            hotmail_client = HotmailClient(browser)
            hotmail_client.change_password()
        except Exception as e:
            logger.error(f"{_index} 修改Hotmail密码失败: {e}")


# 打开页面
# python giveaway.py open  -u https://www.baidu.com -i 1-10


# 清理缓存
# python giveaway.py clear  -u https://www.baidu.com -i 1-10   twitter.com  https://x.com

# 改变扩展插件状态
# python giveaway.py extension -e hpniggdadoniibnhglldbkocmndplgcn -s 1|0 -i 1-10


if __name__ == "__main__":
    cli()
