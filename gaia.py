import click
import random
from loguru import logger
from time import sleep

from story import (
    StoryGaiaDomainChat,
    StoryGaiaChat,
    StoryGaia,
    StoryGaiaRedeem,
    StoryBase,
)
from src.browsers import BrowserType, BROWSER_TYPES
from src.utils.common import parse_indices
from config import DEFAULT_BROWSER_TYPE
from concurrent.futures import ThreadPoolExecutor, as_completed

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]



def _gaia_chat_task(type: BrowserType, ids: list[str], amount: int, send_del: int = 0):
    current_index = 1
    for _index in ids:
        # 这里显示进度
        logger.info(f"********正在执行 {current_index}/{len(ids)} 任务********")
        try:
            story = StoryGaiaChat(type, str(_index))
            story.task(amount, send_del)
            # 如果最后一个任务，则不等待
            if current_index != len(ids):
                sleep(random.randint(60, 600))
        except Exception as e:
            logger.error(f"{_index} 任务失败: {e}") 
        finally:
            current_index += 1


def _gaia_domain_chat_task(type: BrowserType, ids: list[str]):
    try:
        # 并发运行5个任务
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(StoryGaiaDomainChat(type, str(_index)).task) for _index in ids]
            for future in as_completed(futures):
                try:
                    future.result(timeout=1200)
                except Exception as e:
                    logger.error(f"任务失败: {e}")

    except Exception as e:
        logger.error(f"任务失败: {e}")



def _gaia_task(type: BrowserType, ids: list[str]):
    current_index = 1
    for _index in ids:
        # 这里显示进度
        logger.info(f"********正在执行 {current_index}/{len(ids)} 任务********")
        try:
            story = StoryGaia(type, str(_index))
            story.task()
            # 如果最后一个任务，则不等待
            if current_index != len(ids):
                sleep(random.randint(30, 60))
        except Exception as e:
            logger.error(f"{_index} 任务失败: {e}") 
        finally:
            current_index += 1
            story.close()



def _gaia_redeem_task(type: BrowserType, ids: list[str]):
    current_index = 1
    for _index in ids:
        # 这里显示进度
        logger.info(f"********正在执行 {current_index}/{len(ids)} 任务********")
        try:
            story = StoryGaiaRedeem(type, str(_index))
            story.task()
            # 如果最后一个任务，则不等待
            if current_index != len(ids):
                sleep(random.randint(10, 60))
        except Exception as e:
            logger.error(f"{_index} 任务失败: {e}") 
        finally:
            current_index += 1
            story.close()


@click.group()
def cli():
    pass



# 执行Gaia 聊天任务  
@cli.command("gaia_chat")
@click.option(
    "-n",
    "--number",
    type=int,
    default=0,
    help="执行个数, 0为不限制, 根据传入的index为准",
)
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option(
    "-i",
    "--index",
    type=str,
    prompt="请输入浏览器序号",
    help="浏览器序号，支持逗号分隔和范围表示",
)   
@click.option(
    "-a",
    "--amount",
    type=int,
    default=1,
    help="执行个数",
)
@click.option(
    "-s",
    "--send_del",
    type=int,
    default=0,
    help="发送删除消息的个数",
)
def gaia_chat(index: str, type: BrowserType, number: int, amount: int, send_del: int):
    """执行Gaia 聊天任务"""
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        # 根据传入的number参数随机选择指定数量的索引
        if number > 0 and len(indices) > number:
            indices = random.sample(indices, number)

        logger.info(f"选择了 {len(indices)} 个指纹: {indices}")
        _gaia_chat_task(type, indices, amount, send_del)

    except Exception as e:
        logger.error(f"任务执行失败: {e}")



@cli.command("chat")
@click.option(
    "-n",
    "--number",
    type=int,
    default=0,
    help="执行个数, 0为不限制, 根据传入的index为准",
)
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option(
    "-i",
    "--index",
    type=str,
    prompt="请输入浏览器序号",
    help="浏览器序号，支持逗号分隔和范围表示",
)
def gaia_domain_chat(index: str, type: BrowserType, number: int):
    """执行Gaia Domain Chat任务"""
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        # 根据传入的number参数随机选择指定数量的索引
        if number > 0 and len(indices) > number:
            indices = random.sample(indices, number)

        logger.info(f"选择了 {len(indices)} 个指纹: {indices}")
        _gaia_domain_chat_task(type, indices)

    except Exception as e:
        logger.error(f"任务执行失败: {e}")



@cli.command("run")
@click.option(
    "-n",
    "--number",
    type=int,
    default=0,
    help="执行个数, 0为不限制, 根据传入的index为准",
)
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option(
    "-i",
    "--index",
    type=str,
    prompt="请输入浏览器序号",
    help="浏览器序号，支持逗号分隔和范围表示",
)   
def run(index: str, type: BrowserType, number: int):
    """执行Gaia 任务"""
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        # 根据传入的number参数随机选择指定数量的索引
        if number > 0 and len(indices) > number:
            indices = random.sample(indices, number)

        logger.info(f"选择了 {len(indices)} 个指纹: {indices}")
        _gaia_task(type, indices)

    except Exception as e:
        logger.error(f"任务执行失败: {e}")



# 执行Gaia 任务  
@cli.command("redeem")
@click.option(
    "-n",
    "--number",
    type=int,
    default=0,
    help="执行个数, 0为不限制, 根据传入的index为准",
)
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option(
    "-i",
    "--index",
    type=str,
    prompt="请输入浏览器序号",
    help="浏览器序号，支持逗号分隔和范围表示",
)   
def gaia_redeem(index: str, type: BrowserType, number: int):
    """执行Gaia 信用分任务"""
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        # 根据传入的number参数随机选择指定数量的索引
        if number > 0 and len(indices) > number:
            indices = random.sample(indices, number)

        logger.info(f"选择了 {len(indices)} 个指纹: {indices}")
        _gaia_redeem_task(type, indices)

    except Exception as e:
        logger.error(f"任务执行失败: {e}")


# odyssey
# python3 gaia.py o -n 50

# 1. 更新faucetme数据, 主要获取userid 跟 auth
# python3 gaia.py uf -t ads -i 1-10

# 2. 通过api领取faucetme
# python3 gaia.py fdc -t ads -i 1-10

if __name__ == "__main__":
    cli()
